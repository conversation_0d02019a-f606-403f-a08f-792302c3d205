import React, { ReactNode } from 'react';
import { <PERSON><PERSON><PERSON>, Montserrat, Playfair_Display, Space_Grotesk } from 'next/font/google';

// Call each font at the module level
const poppins = Poppins({ subsets: ['latin'], weight: ['400', '600', '700'] });
const montserrat = Montserrat({ subsets: ['latin'], weight: ['400', '600', '700'] });
const playfair = Playfair_Display({ subsets: ['latin'], weight: ['400', '700'] });
const spacegrotesk = Space_Grotesk({ subsets: ['latin'], weight: ['400', '700'] });

const fontMap = {
  poppins,
  montserrat,
  playfair,
  spacegrotesk,
};

type FontKey = keyof typeof fontMap;

export default function FontSwitcher({
  font = 'montserrat',
  children,
}: {
  font?: FontKey;
  children: ReactNode;
}) {
  const fontObj = fontMap[font] || fontMap.poppins;
  return <div className={fontObj.className}>{children}</div>;
} 