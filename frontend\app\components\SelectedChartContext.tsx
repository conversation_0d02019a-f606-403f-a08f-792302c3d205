import React from 'react';
import { useChat } from '../contexts/ChatContext';

export default function SelectedChartContext() {
  const { chartSelection, setChartSelection } = useChat();

  // If no selection, don't render anything
  if (!chartSelection.point && !chartSelection.range) return null;

  // Function to clear the selection
  const clearSelection = () => {
    setChartSelection({});
  };

  return (
    <div className="mt-2 flex flex-wrap gap-2">
      {chartSelection.point && (
        <div className="flex items-center bg-gray-800/50 rounded px-2 py-1 text-xs">
          <span className="text-kairosYellow mr-1">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="10" />
              <circle cx="12" cy="12" r="3" />
            </svg>
          </span>
          <span className="text-gray-300 truncate max-w-[150px]">
            Point: {chartSelection.point.name} ({chartSelection.point.value})
          </span>
          
          {/* Remove button */}
          <button
            type="button"
            onClick={clearSelection}
            className="ml-1 text-gray-400 hover:text-gray-200 transition-colors"
            aria-label="Clear selection"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
      )}

      {chartSelection.range && (
        <div className="flex items-center bg-gray-800/50 rounded px-2 py-1 text-xs">
          <span className="text-kairosYellow mr-1">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M3 6h18M3 12h18M3 18h18" />
            </svg>
          </span>
          <span className="text-gray-300 truncate max-w-[150px]">
            {chartSelection.range.start === chartSelection.range.end 
              ? `Point: ${chartSelection.range.start}` 
              : `Range: ${chartSelection.range.start} to ${chartSelection.range.end}`}
          </span>

          {/* Remove button */}
          <button
            type="button"
            onClick={clearSelection}
            className="ml-1 text-gray-400 hover:text-gray-200 transition-colors"
            aria-label="Clear selection"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
      )}
    </div>
  );
}


