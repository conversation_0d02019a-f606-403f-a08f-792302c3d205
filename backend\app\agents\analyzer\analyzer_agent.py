from typing import Dict, Any, List, Optional
from ..base_agent import Agent
from datetime import datetime
from enum import Enum

class MessageSource(Enum):
    USER = "user"
    AGENT = "agent"

class AnalyzerAgent(Agent):
    def __init__(self):
        super().__init__(
            name="analyzer",
            description="Agent responsible for context analysis, operation planning, and blackboard interaction"
        )
        self.conversation_history = []
        self.blackboard_state = {}  
    
    async def process(self, input_data: dict) -> dict:
        """
        Process the input data through analysis and blackboard operations.
        
        Args:
            input_data: Dictionary containing input data and context
            
        Returns:
            Dictionary containing analysis results and next steps
        """
        # Update conversation history with user message
        self._update_conversation_history(
            message=input_data.get("message", ""),
            source=MessageSource.USER,
            context=input_data.get("context", {})
        )
        
        # Analyze context and determine operations
        analysis_result = await self._analyze_context(input_data)
        
        # Determine required blackboard operations
        blackboard_ops = self._plan_blackboard_operations(analysis_result)
        
        # Execute blackboard operations
        operation_results = await self._execute_blackboard_operations(blackboard_ops)
        
        # Update blackboard view
        await self._update_blackboard_view(operation_results)
        
        response = {
            "status": "success",
            "analysis": analysis_result,
            "operations_performed": blackboard_ops,
            "blackboard_updates": operation_results,
            "next_agent": self._determine_next_agent(analysis_result)
        }

        # Update conversation history with agent's response
        self._update_conversation_history(
            message=str(response),  # Convert response to string for history
            source=MessageSource.AGENT,
            context={"analysis": analysis_result}
        )
        
        return response

    def _update_conversation_history(self, message: str, source: MessageSource, context: dict = None) -> None:
        """
        Maintain conversation history with message source tracking.
        
        Args:
            message: The message content
            source: Source of the message (user or agent)
            context: Additional context for the message
        """
        entry = {
            "timestamp": datetime.now().isoformat(),
            "source": source.value,
            "message": message,
            "context": context or {}
        }
        self.conversation_history.append(entry)

    def get_conversation_history(self, limit: Optional[int] = None) -> List[dict]:
        """
        Retrieve conversation history.
        
        Args:
            limit: Optional limit for number of recent messages to return
            
        Returns:
            List of conversation history entries
        """
        if limit is not None:
            return self.conversation_history[-limit:]
        return self.conversation_history

    def get_user_messages(self) -> List[dict]:
        """Get all user messages from conversation history."""
        return [
            entry for entry in self.conversation_history 
            if entry["source"] == MessageSource.USER.value
        ]

    def get_agent_messages(self) -> List[dict]:
        """Get all agent messages from conversation history."""
        return [
            entry for entry in self.conversation_history 
            if entry["source"] == MessageSource.AGENT.value
        ]

    async def _analyze_context(self, input_data: dict) -> dict:
        """
        Analyze the input context and determine required actions.
        This is a simplified implementation.
        """
        message = input_data.get("message", "")
        context = input_data.get("context", {})
        
        # Simple analysis based on message content
        analysis = {
            "intent": self._determine_intent(message),
            "required_tools": self._identify_required_tools(message),
            "context_relevance": self._assess_context_relevance(context)
        }
        
        return analysis

    def _determine_intent(self, message: str) -> str:
        """Dummy intent detection."""
        # Add LLM calls
        if "retrieve" in message.lower():
            return "data_retrieval"
        elif "update" in message.lower():
            return "data_update"
        return "general_query"

    def _identify_required_tools(self, message: str) -> List[str]:
        """Identify tools needed based on the message."""
        # Add LLM calls
        tools = []
        if "search" in message.lower():
            tools.append("search_tool")
        if "analyze" in message.lower():
            tools.append("analysis_tool")
        return tools

    def _assess_context_relevance(self, context: dict) -> float:
        """Dummy context relevance scoring."""
        # Dummy metric
        return 0.8 if context else 0.0

    def _plan_blackboard_operations(self, analysis: dict) -> List[dict]:
        """Plan operations to be performed on the blackboard."""
        operations = []
        intent = analysis.get("intent")
        
        if intent == "data_retrieval":
            operations.append({
                "type": "retrieve",
                "target": "blackboard",
                "parameters": {"scope": "recent"}
            })
        elif intent == "data_update":
            operations.append({
                "type": "update",
                "target": "blackboard",
                "parameters": {"update_type": "append"}
            })
            
        return operations

    async def _execute_blackboard_operations(self, operations: List[dict]) -> dict:
        """Execute planned operations on the blackboard."""
        results = {
            "executed_operations": [],
            "status": "success"
        }
        
        for op in operations:
            # Simulate operation execution
            results["executed_operations"].append({
                "operation": op["type"],
                "status": "completed",
                "timestamp": datetime.now().isoformat()
            })
            
        return results

    async def _update_blackboard_view(self, operation_results: dict) -> None:
        """Update the blackboard view based on operation results."""
        self.blackboard_state.update({
            "last_update": datetime.now().isoformat(),
            "last_operation": operation_results
        })

    def _determine_next_agent(self, analysis_result: dict) -> Optional[str]:
        """Determine which agent should process next based on analysis."""
        intent = analysis_result.get("intent")
        
        if intent == "data_retrieval":
            return "retriever"
        elif intent == "data_update":
            return "forecast"
        return None  # End of processing chain 