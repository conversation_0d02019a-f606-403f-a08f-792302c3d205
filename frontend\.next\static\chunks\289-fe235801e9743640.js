(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[289],{2119:(e,t,n)=>{var r=n(54260);e.exports=function(e,t,n){var r,o,s,a,l;return r=e,o=t||i,s=n||function(t){return e.outEdges(t)},a={},(l=r.nodes()).forEach(function(e){a[e]={},a[e][e]={distance:0},l.forEach(function(t){e!==t&&(a[e][t]={distance:Number.POSITIVE_INFINITY})}),s(e).forEach(function(t){var n=t.v===e?t.w:t.v,r=o(t);a[e][n]={distance:r,predecessor:e}})}),l.forEach(function(e){var t=a[e];l.forEach(function(n){var r=a[n];l.forEach(function(n){var i=r[e],o=t[n],s=r[n],a=i.distance+o.distance;a<s.distance&&(s.distance=a,s.predecessor=o.predecessor)})})}),a};var i=r.constant(1)},2866:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(12115);let i=r.forwardRef(function(e,t){let{title:n,titleId:i,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),n?r.createElement("title",{id:i},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m15 15 6-6m0 0-6-6m6 6H9a6 6 0 0 0 0 12h3"}))})},4377:(e,t,n)=>{var r=n(24376),i=r?r.prototype:void 0,o=i?i.valueOf:void 0;e.exports=function(e){return o?Object(o.call(e)):{}}},5279:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(12115);let i=r.forwardRef(function(e,t){let{title:n,titleId:i,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),n?r.createElement("title",{id:i},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"}))})},5518:(e,t,n)=>{var r=n(11850),i=n(28897);e.exports=function(e,t,n,o){var s=!n;n||(n={});for(var a=-1,l=t.length;++a<l;){var u=t[a],c=o?o(n[u],e[u],u,n,e):void 0;void 0===c&&(c=e[u]),s?i(n,u,c):r(n,u,c)}return n}},6543:(e,t,n)=>{var r=n(5518),i=n(54648);e.exports=function(e,t){return e&&r(t,i(t),e)}},7039:e=>{e.exports=function(e,t,n,r,i){return i(e,function(e,i,o){n=r?(r=!1,e):t(n,e,i,o)}),n}},7749:(e,t,n)=>{var r=n(34711),i=n(36707),o=n(73800);e.exports=function(e,t,n){for(var s=-1,a=t.length,l={};++s<a;){var u=t[s],c=r(e,u);n(c,u)&&i(l,o(u,e),c)}return l}},7854:(e,t,n)=>{e.exports=n(84747)},9808:(e,t,n)=>{"use strict";var r=n(18816).longestPath,i=n(33448),o=n(23677);e.exports=function(e){switch(e.graph().ranker){case"network-simplex":default:o(e);break;case"tight-tree":var t;r(t=e),i(t);break;case"longest-path":s(e)}};var s=r},10184:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(12115);let i=r.forwardRef(function(e,t){let{title:n,titleId:i,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),n?r.createElement("title",{id:i},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},10255:(e,t,n)=>{"use strict";function r(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return r}}),n(95155),n(47650),n(85744),n(20589)},10707:(e,t,n)=>{"use strict";var r=n(25016);function i(e){r.forEach(e.nodes(),function(t){o(e.node(t))}),r.forEach(e.edges(),function(t){o(e.edge(t))})}function o(e){var t=e.width;e.width=e.height,e.height=t}function s(e){e.y=-e.y}function a(e){var t=e.x;e.x=e.y,e.y=t}e.exports={adjust:function(e){var t=e.graph().rankdir.toLowerCase();("lr"===t||"rl"===t)&&i(e)},undo:function(e){var t,n,o=e.graph().rankdir.toLowerCase();("bt"===o||"rl"===o)&&(t=e,r.forEach(t.nodes(),function(e){s(t.node(e))}),r.forEach(t.edges(),function(e){var n=t.edge(e);r.forEach(n.points,s),r.has(n,"y")&&s(n)})),("lr"===o||"rl"===o)&&(n=e,r.forEach(n.nodes(),function(e){a(n.node(e))}),r.forEach(n.edges(),function(e){var t=n.edge(e);r.forEach(t.points,a),r.has(t,"x")&&a(t)}),i(e))}}},11368:(e,t,n)=>{var r=n(83172);e.exports=function(e,t){var n=t?r(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}},11850:(e,t,n)=>{var r=n(28897),i=n(58817),o=Object.prototype.hasOwnProperty;e.exports=function(e,t,n){var s=e[t];o.call(e,t)&&i(s,n)&&(void 0!==n||t in e)||r(e,t,n)}},11928:(e,t,n)=>{var r=n(83172);e.exports=function(e,t){var n=t?r(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}},13703:(e,t,n)=>{var r=n(94380),i=n(48611);e.exports=function(e){return i(e)&&"[object Set]"==r(e)}},13738:(e,t,n)=>{var r=n(54260);e.exports=function(e){var t,n={},i=[];return r.each(e.nodes(),function(o){t=[],function i(o){r.has(n,o)||(n[o]=!0,t.push(o),r.each(e.successors(o),i),r.each(e.predecessors(o),i))}(o),t.length&&i.push(t)}),i}},14268:(e,t,n)=>{var r=n(58918),i=n(18028),o=n(52521);e.exports=function(e,t){return e&&e.length?r(e,i(t,2),o):void 0}},14856:(e,t,n)=>{var r=n(54260),i=n(27880);e.exports=function(e,t,n,r){return function(e,t,n,r){var o,s,a={},l=new i,u=function(e){var t=e.v!==o?e.v:e.w,r=a[t],i=n(e),u=s.distance+i;if(i<0)throw Error("dijkstra does not allow negative edge weights. Bad edge: "+e+" Weight: "+i);u<r.distance&&(r.distance=u,r.predecessor=o,l.decrease(t,u))};for(e.nodes().forEach(function(e){var n=e===t?0:Number.POSITIVE_INFINITY;a[e]={distance:n},l.add(e,n)});l.size()>0&&(s=a[o=l.removeMin()]).distance!==Number.POSITIVE_INFINITY;)r(o).forEach(u);return a}(e,String(t),n||o,r||function(t){return e.outEdges(t)})};var o=r.constant(1)},14909:(e,t,n)=>{var r=n(38675),i=n(91783),o=n(18028),s=n(39608);e.exports=function(e,t){return(s(e)?r:i)(e,o(t,3))}},17828:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return r}});let r=(0,n(64054).createAsyncLocalStorage)()},18285:(e,t,n)=>{var r=n(25016);e.exports=function(e,t){return r.map(t,function(t){var n=e.inEdges(t);if(!n.length)return{v:t};var i=r.reduce(n,function(t,n){var r=e.edge(n),i=e.node(n.v);return{sum:t.sum+r.weight*i.order,weight:t.weight+r.weight}},{sum:0,weight:0});return{v:t,barycenter:i.sum/i.weight,weight:i.weight}})}},18590:(e,t,n)=>{var r=n(14856),i=n(54260);e.exports=function(e,t,n){return i.transform(e.nodes(),function(i,o){i[o]=r(e,o,t,n)},{})}},18816:(e,t,n)=>{"use strict";var r=n(25016);e.exports={longestPath:function(e){var t={};r.forEach(e.sources(),function n(i){var o=e.node(i);if(r.has(t,i))return o.rank;t[i]=!0;var s=r.min(r.map(e.outEdges(i),function(t){return n(t.w)-e.edge(t).minlen}));return(s===Number.POSITIVE_INFINITY||null==s)&&(s=0),o.rank=s})},slack:function(e,t){return e.node(t.w).rank-e.node(t.v).rank-e.edge(t).minlen}}},19229:(e,t,n)=>{var r=n(94380),i=n(48611);e.exports=function(e){return i(e)&&"[object Map]"==r(e)}},19582:(e,t,n)=>{var r=n(54260),i=n(77951),o=n(27880);e.exports=function(e,t){var n,s=new i,a={},l=new o;function u(e){var r=e.v===n?e.w:e.v,i=l.priority(r);if(void 0!==i){var o=t(e);o<i&&(a[r]=n,l.decrease(r,o))}}if(0===e.nodeCount())return s;r.each(e.nodes(),function(e){l.add(e,Number.POSITIVE_INFINITY),s.setNode(e)}),l.decrease(e.nodes()[0],0);for(var c=!1;l.size()>0;){if(n=l.removeMin(),r.has(a,n))s.setEdge(n,a[n]);else if(c)throw Error("Input graph is not connected: "+e);else c=!0;e.nodeEdges(n).forEach(u)}return s}},20772:(e,t,n)=>{var r=n(5518),i=n(35095);e.exports=function(e,t){return e&&r(t,i(t),e)}},21699:(e,t,n)=>{"use strict";var r=n(25016),i=n(86319),o=n(54968),s=n(9808),a=n(45285).normalizeRanks,l=n(93557),u=n(45285).removeEmptyRanks,c=n(63928),h=n(52014),d=n(10707),f=n(71524),p=n(99753),m=n(45285),g=n(71814).Graph;e.exports=function(e,t){var n=t&&t.debugTiming?m.time:m.notime;n("layout",function(){var t=n("  buildLayoutGraph",function(){var t,n,i;return t=e,n=new g({multigraph:!0,compound:!0}),i=P(t.graph()),n.setGraph(r.merge({},y,T(i,v),r.pick(i,x))),r.forEach(t.nodes(),function(e){var i=P(t.node(e));n.setNode(e,r.defaults(T(i,b),w)),n.setParent(e,t.parent(e))}),r.forEach(t.edges(),function(e){var i=P(t.edge(e));n.setEdge(e,r.merge({},E,T(i,k),r.pick(i,S)))}),n});n("  runLayout",function(){var e,g;e=t,(g=n)("    makeSpaceForEdgeLabels",function(){var t,n;n=(t=e).graph(),n.ranksep/=2,r.forEach(t.edges(),function(e){var r=t.edge(e);r.minlen*=2,"c"!==r.labelpos.toLowerCase()&&("TB"===n.rankdir||"BT"===n.rankdir?r.width+=r.labeloffset:r.height+=r.labeloffset)})}),g("    removeSelfEdges",function(){var t;t=e,r.forEach(t.edges(),function(e){if(e.v===e.w){var n=t.node(e.v);n.selfEdges||(n.selfEdges=[]),n.selfEdges.push({e:e,label:t.edge(e)}),t.removeEdge(e)}})}),g("    acyclic",function(){i.run(e)}),g("    nestingGraph.run",function(){c.run(e)}),g("    rank",function(){s(m.asNonCompoundGraph(e))}),g("    injectEdgeLabelProxies",function(){var t;t=e,r.forEach(t.edges(),function(e){var n=t.edge(e);if(n.width&&n.height){var r=t.node(e.v),i={rank:(t.node(e.w).rank-r.rank)/2+r.rank,e:e};m.addDummyNode(t,"edge-proxy",i,"_ep")}})}),g("    removeEmptyRanks",function(){u(e)}),g("    nestingGraph.cleanup",function(){c.cleanup(e)}),g("    normalizeRanks",function(){a(e)}),g("    assignRankMinMax",function(){var t,n;t=e,n=0,r.forEach(t.nodes(),function(e){var i=t.node(e);i.borderTop&&(i.minRank=t.node(i.borderTop).rank,i.maxRank=t.node(i.borderBottom).rank,n=r.max(n,i.maxRank))}),t.graph().maxRank=n}),g("    removeEdgeLabelProxies",function(){var t;t=e,r.forEach(t.nodes(),function(e){var n=t.node(e);"edge-proxy"===n.dummy&&(t.edge(n.e).labelRank=n.rank,t.removeNode(e))})}),g("    normalize.run",function(){o.run(e)}),g("    parentDummyChains",function(){l(e)}),g("    addBorderSegments",function(){h(e)}),g("    order",function(){f(e)}),g("    insertSelfEdges",function(){var t,n;t=e,n=m.buildLayerMatrix(t),r.forEach(n,function(e){var n=0;r.forEach(e,function(e,i){var o=t.node(e);o.order=i+n,r.forEach(o.selfEdges,function(e){m.addDummyNode(t,"selfedge",{width:e.label.width,height:e.label.height,rank:o.rank,order:i+ ++n,e:e.e,label:e.label},"_se")}),delete o.selfEdges})})}),g("    adjustCoordinateSystem",function(){d.adjust(e)}),g("    position",function(){p(e)}),g("    positionSelfEdges",function(){var t;t=e,r.forEach(t.nodes(),function(e){var n=t.node(e);if("selfedge"===n.dummy){var r=t.node(n.e.v),i=r.x+r.width/2,o=r.y,s=n.x-i,a=r.height/2;t.setEdge(n.e,n.label),t.removeNode(e),n.label.points=[{x:i+2*s/3,y:o-a},{x:i+5*s/6,y:o-a},{x:i+s,y:o},{x:i+5*s/6,y:o+a},{x:i+2*s/3,y:o+a}],n.label.x=n.x,n.label.y=n.y}})}),g("    removeBorderNodes",function(){var t;t=e,r.forEach(t.nodes(),function(e){if(t.children(e).length){var n=t.node(e),i=t.node(n.borderTop),o=t.node(n.borderBottom),s=t.node(r.last(n.borderLeft)),a=t.node(r.last(n.borderRight));n.width=Math.abs(a.x-s.x),n.height=Math.abs(o.y-i.y),n.x=s.x+n.width/2,n.y=i.y+n.height/2}}),r.forEach(t.nodes(),function(e){"border"===t.node(e).dummy&&t.removeNode(e)})}),g("    normalize.undo",function(){o.undo(e)}),g("    fixupEdgeLabelCoords",function(){var t;t=e,r.forEach(t.edges(),function(e){var n=t.edge(e);if(r.has(n,"x"))switch(("l"===n.labelpos||"r"===n.labelpos)&&(n.width-=n.labeloffset),n.labelpos){case"l":n.x-=n.width/2+n.labeloffset;break;case"r":n.x+=n.width/2+n.labeloffset}})}),g("    undoCoordinateSystem",function(){d.undo(e)}),g("    translateGraph",function(){!function(e){var t=Number.POSITIVE_INFINITY,n=0,i=Number.POSITIVE_INFINITY,o=0,s=e.graph(),a=s.marginx||0,l=s.marginy||0;function u(e){var r=e.x,s=e.y,a=e.width,l=e.height;t=Math.min(t,r-a/2),n=Math.max(n,r+a/2),i=Math.min(i,s-l/2),o=Math.max(o,s+l/2)}r.forEach(e.nodes(),function(t){u(e.node(t))}),r.forEach(e.edges(),function(t){var n=e.edge(t);r.has(n,"x")&&u(n)}),t-=a,i-=l,r.forEach(e.nodes(),function(n){var r=e.node(n);r.x-=t,r.y-=i}),r.forEach(e.edges(),function(n){var o=e.edge(n);r.forEach(o.points,function(e){e.x-=t,e.y-=i}),r.has(o,"x")&&(o.x-=t),r.has(o,"y")&&(o.y-=i)}),s.width=n-t+a,s.height=o-i+l}(e)}),g("    assignNodeIntersects",function(){var t;t=e,r.forEach(t.edges(),function(e){var n,r,i=t.edge(e),o=t.node(e.v),s=t.node(e.w);i.points?(n=i.points[0],r=i.points[i.points.length-1]):(i.points=[],n=s,r=o),i.points.unshift(m.intersectRect(o,n)),i.points.push(m.intersectRect(s,r))})}),g("    reversePoints",function(){var t;t=e,r.forEach(t.edges(),function(e){var n=t.edge(e);n.reversed&&n.points.reverse()})}),g("    acyclic.undo",function(){i.undo(e)})}),n("  updateInputGraph",function(){var n,i;n=e,i=t,r.forEach(n.nodes(),function(e){var t=n.node(e),r=i.node(e);t&&(t.x=r.x,t.y=r.y,i.children(e).length&&(t.width=r.width,t.height=r.height))}),r.forEach(n.edges(),function(e){var t=n.edge(e),o=i.edge(e);t.points=o.points,r.has(o,"x")&&(t.x=o.x,t.y=o.y)}),n.graph().width=i.graph().width,n.graph().height=i.graph().height})})};var v=["nodesep","edgesep","ranksep","marginx","marginy"],y={ranksep:50,edgesep:20,nodesep:50,rankdir:"tb"},x=["acyclicer","ranker","rankdir","align"],b=["width","height"],w={width:0,height:0},k=["minlen","weight","width","height","labeloffset"],E={minlen:1,weight:1,width:0,height:0,labeloffset:10,labelpos:"r"},S=["labelpos"];function T(e,t){return r.mapValues(r.pick(e,t),Number)}function P(e){var t={};return r.forEach(e,function(e,n){t[n.toLowerCase()]=e}),t}},21775:(e,t,n)=>{var r=n(54260),i=n(77951);e.exports={write:function(e){var t,n,i={options:{directed:e.isDirected(),multigraph:e.isMultigraph(),compound:e.isCompound()},nodes:(t=e,r.map(t.nodes(),function(e){var n=t.node(e),i=t.parent(e),o={v:e};return r.isUndefined(n)||(o.value=n),r.isUndefined(i)||(o.parent=i),o})),edges:(n=e,r.map(n.edges(),function(e){var t=n.edge(e),i={v:e.v,w:e.w};return r.isUndefined(e.name)||(i.name=e.name),r.isUndefined(t)||(i.value=t),i}))};return r.isUndefined(e.graph())||(i.value=r.clone(e.graph())),i},read:function(e){var t=new i(e.options).setGraph(e.value);return r.each(e.nodes,function(e){t.setNode(e.v,e.value),e.parent&&t.setParent(e.v,e.parent)}),r.each(e.edges,function(e){t.setEdge({v:e.v,w:e.w,name:e.name},e.value)}),t}}},23406:e=>{e.exports=function(e){return void 0===e}},23677:(e,t,n)=>{"use strict";var r=n(25016),i=n(33448),o=n(18816).slack,s=n(18816).longestPath,a=n(71814).alg.preorder,l=n(71814).alg.postorder,u=n(45285).simplify;function c(e){s(e=u(e));var t,n,r=i(e);for(f(r),h(r,e);t=p(r);)n=m(r,e,t),g(r,e,t,n)}function h(e,t){var n=l(e,e.nodes());n=n.slice(0,n.length-1),r.forEach(n,function(n){var r,i,o,s;r=e,i=t,o=n,s=r.node(o).parent,r.edge(o,s).cutvalue=d(r,i,o)})}function d(e,t,n){var i=e.node(n).parent,o=!0,s=t.edge(n,i),a=0;return s||(o=!1,s=t.edge(i,n)),a=s.weight,r.forEach(t.nodeEdges(n),function(r){var s=r.v===n,l=s?r.w:r.v;if(l!==i){var u,c,h,d=s===o,f=t.edge(r).weight;if(a+=d?f:-f,u=e,c=n,h=l,u.hasEdge(c,h)){var p=e.edge(n,l).cutvalue;a+=d?-p:p}}}),a}function f(e,t){arguments.length<2&&(t=e.nodes()[0]),function e(t,n,i,o,s){var a=i,l=t.node(o);return n[o]=!0,r.forEach(t.neighbors(o),function(s){r.has(n,s)||(i=e(t,n,i,s,o))}),l.low=a,l.lim=i++,s?l.parent=s:delete l.parent,i}(e,{},1,t)}function p(e){return r.find(e.edges(),function(t){return e.edge(t).cutvalue<0})}function m(e,t,n){var i=n.v,s=n.w;t.hasEdge(i,s)||(i=n.w,s=n.v);var a=e.node(i),l=e.node(s),u=a,c=!1;a.lim>l.lim&&(u=l,c=!0);var h=r.filter(t.edges(),function(t){return c===v(e,e.node(t.v),u)&&c!==v(e,e.node(t.w),u)});return r.minBy(h,function(e){return o(t,e)})}function g(e,t,n,i){var o,s,l,u,c=n.v,d=n.w;e.removeEdge(c,d),e.setEdge(i.v,i.w,{}),f(e),h(e,t),o=e,s=t,l=r.find(o.nodes(),function(e){return!s.node(e).parent}),u=(u=a(o,l)).slice(1),r.forEach(u,function(e){var t=o.node(e).parent,n=s.edge(e,t),r=!1;n||(n=s.edge(t,e),r=!0),s.node(e).rank=s.node(t).rank+(r?n.minlen:-n.minlen)})}function v(e,t,n){return n.low<=t.lim&&t.lim<=n.lim}e.exports=c,c.initLowLimValues=f,c.initCutValues=h,c.calcCutValue=d,c.leaveEdge=p,c.enterEdge=m,c.exchangeEdges=g},23746:(e,t,n)=>{var r=n(77969),i=n(21087),o=n(65836),s=n(55836);e.exports=i(function(e){return o(r(e,1,s,!0))})},24035:(e,t,n)=>{e=n.nmd(e);var r=n(82500),i=t&&!t.nodeType&&t,o=i&&e&&!e.nodeType&&e,s=o&&o.exports===i?r.Buffer:void 0,a=s?s.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var n=e.length,r=a?a(n):new e.constructor(n);return e.copy(r),r}},24138:e=>{function t(){var e={};e._next=e._prev=e,this._sentinel=e}function n(e){e._prev._next=e._next,e._next._prev=e._prev,delete e._next,delete e._prev}function r(e,t){if("_next"!==e&&"_prev"!==e)return t}e.exports=t,t.prototype.dequeue=function(){var e=this._sentinel,t=e._prev;if(t!==e)return n(t),t},t.prototype.enqueue=function(e){var t=this._sentinel;e._prev&&e._next&&n(e),e._next=t._next,t._next._prev=e,t._next=e,e._prev=t},t.prototype.toString=function(){for(var e=[],t=this._sentinel,n=t._prev;n!==t;)e.push(JSON.stringify(n,r)),n=n._prev;return"["+e.join(", ")+"]"}},25016:(e,t,n)=>{var r;try{r={cloneDeep:n(78966),constant:n(50687),defaults:n(60013),each:n(7854),filter:n(14909),find:n(97124),flatten:n(29147),forEach:n(84747),forIn:n(36085),has:n(55041),isUndefined:n(23406),last:n(83979),map:n(69363),mapValues:n(74925),max:n(22315),merge:n(32813),min:n(89053),minBy:n(14268),now:n(76685),pick:n(41998),range:n(18940),reduce:n(78701),sortBy:n(67206),uniqueId:n(45577),values:n(89865),zipObject:n(70865)}}catch(e){}r||(r=window._),e.exports=r},25052:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(12115);let i=r.forwardRef(function(e,t){let{title:n,titleId:i,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),n?r.createElement("title",{id:i},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"}))})},25528:(e,t,n)=>{var r=n(25016),i=n(45285);function o(e,t,n){for(var i;t.length&&(i=r.last(t)).i<=n;)t.pop(),e.push(i.vs),n++;return n}e.exports=function(e,t){var n,s=i.partition(e,function(e){return r.has(e,"barycenter")}),a=s.lhs,l=r.sortBy(s.rhs,function(e){return-e.i}),u=[],c=0,h=0,d=0;a.sort((n=!!t,function(e,t){return e.barycenter<t.barycenter?-1:e.barycenter>t.barycenter?1:n?t.i-e.i:e.i-t.i})),d=o(u,l,d),r.forEach(a,function(e){d+=e.vs.length,u.push(e.vs),c+=e.barycenter*e.weight,h+=e.weight,d=o(u,l,d)});var f={vs:r.flatten(u,!0)};return h&&(f.barycenter=c/h,f.weight=h),f}},27148:(e,t,n)=>{var r=n(25016),i=n(45285),o=n(71814).Graph;e.exports={debugOrdering:function(e){var t=i.buildLayerMatrix(e),n=new o({compound:!0,multigraph:!0}).setGraph({});return r.forEach(e.nodes(),function(t){n.setNode(t,{label:t}),n.setParent(t,"layer"+e.node(t).rank)}),r.forEach(e.edges(),function(e){n.setEdge(e.v,e.w,{},e.name)}),r.forEach(t,function(e,t){n.setNode("layer"+t,{rank:"same"}),r.reduce(e,function(e,t){return n.setEdge(e,t,{style:"invis"}),t})}),n}}},27880:(e,t,n)=>{var r=n(54260);function i(){this._arr=[],this._keyIndices={}}e.exports=i,i.prototype.size=function(){return this._arr.length},i.prototype.keys=function(){return this._arr.map(function(e){return e.key})},i.prototype.has=function(e){return r.has(this._keyIndices,e)},i.prototype.priority=function(e){var t=this._keyIndices[e];if(void 0!==t)return this._arr[t].priority},i.prototype.min=function(){if(0===this.size())throw Error("Queue underflow");return this._arr[0].key},i.prototype.add=function(e,t){var n=this._keyIndices;if(e=String(e),!r.has(n,e)){var i=this._arr,o=i.length;return n[e]=o,i.push({key:e,priority:t}),this._decrease(o),!0}return!1},i.prototype.removeMin=function(){this._swap(0,this._arr.length-1);var e=this._arr.pop();return delete this._keyIndices[e.key],this._heapify(0),e.key},i.prototype.decrease=function(e,t){var n=this._keyIndices[e];if(t>this._arr[n].priority)throw Error("New priority is greater than current priority. Key: "+e+" Old: "+this._arr[n].priority+" New: "+t);this._arr[n].priority=t,this._decrease(n)},i.prototype._heapify=function(e){var t=this._arr,n=2*e,r=n+1,i=e;n<t.length&&(i=t[n].priority<t[i].priority?n:i,r<t.length&&(i=t[r].priority<t[i].priority?r:i),i!==e&&(this._swap(e,i),this._heapify(i)))},i.prototype._decrease=function(e){for(var t,n=this._arr,r=n[e].priority;0!==e&&!(n[t=e>>1].priority<r);)this._swap(e,t),e=t},i.prototype._swap=function(e,t){var n=this._arr,r=this._keyIndices,i=n[e],o=n[t];n[e]=o,n[t]=i,r[o.key]=e,r[i.key]=t}},28175:(e,t,n)=>{e.exports={graphlib:n(71814),layout:n(21699),debug:n(27148),util:{time:n(45285).time,notime:n(45285).notime},version:n(77311)}},29147:(e,t,n)=>{var r=n(77969);e.exports=function(e){return(null==e?0:e.length)?r(e,1):[]}},29721:(e,t,n)=>{var r=n(54260);function i(e){var t={},n={},i=[];if(r.each(e.sinks(),function s(a){if(r.has(n,a))throw new o;r.has(t,a)||(n[a]=!0,t[a]=!0,r.each(e.predecessors(a),s),delete n[a],i.push(a))}),r.size(t)!==e.nodeCount())throw new o;return i}function o(){}e.exports=i,i.CycleException=o,o.prototype=Error()},31300:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var n=/^--[a-zA-Z0-9_-]+$/,r=/-([a-z])/g,i=/^[^-]+$/,o=/^-(webkit|moz|ms|o|khtml)-/,s=/^-(ms)-/,a=function(e,t){return t.toUpperCase()},l=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){var u;return(void 0===t&&(t={}),!(u=e)||i.test(u)||n.test(u))?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(s,l):e.replace(o,l)).replace(r,a))}},31814:(e,t,n)=>{var r=n(21087),i=n(39641);e.exports=function(e){return r(function(t,n){var r=-1,o=n.length,s=o>1?n[o-1]:void 0,a=o>2?n[2]:void 0;for(s=e.length>3&&"function"==typeof s?(o--,s):void 0,a&&i(n[0],n[1],a)&&(s=o<3?void 0:s,o=1),t=Object(t);++r<o;){var l=n[r];l&&e(t,l,r,s)}return t})}},31913:(e,t,n)=>{var r=n(29147),i=n(64588),o=n(61632);e.exports=function(e){return o(i(e,void 0,r),e+"")}},32082:(e,t,n)=>{"use strict";n.d(t,{xQ:()=>o});var r=n(12115),i=n(80845);function o(e=!0){let t=(0,r.useContext)(i.t);if(null===t)return[!0,null];let{isPresent:n,onExitComplete:s,register:a}=t,l=(0,r.useId)();(0,r.useEffect)(()=>{if(e)return a(l)},[e]);let u=(0,r.useCallback)(()=>e&&s&&s(l),[l,s,e]);return!n&&s?[!1,u]:[!0]}},32813:(e,t,n)=>{var r=n(71939);e.exports=n(31814)(function(e,t,n){r(e,t,n)})},33044:e=>{e.exports=function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t}},33448:(e,t,n)=>{"use strict";var r=n(25016),i=n(71814).Graph,o=n(18816).slack;e.exports=function(e){var t,n,s,a,l=new i({directed:!1}),u=e.nodes()[0],c=e.nodeCount();for(l.setNode(u,{});t=l,n=e,r.forEach(t.nodes(),function e(i){r.forEach(n.nodeEdges(i),function(r){var s=r.v,a=i===s?r.w:s;t.hasNode(a)||o(n,r)||(t.setNode(a,{}),t.setEdge(i,a,{}),e(a))})}),t.nodeCount()<c;)s=function(e,t){return r.minBy(t.edges(),function(n){if(e.hasNode(n.v)!==e.hasNode(n.w))return o(t,n)})}(l,e),a=l.hasNode(s.v)?o(e,s):-o(e,s),function(e,t,n){r.forEach(e.nodes(),function(e){t.node(e).rank+=n})}(l,e,a);return l}},35776:e=>{var t=/\w*$/;e.exports=function(e){var n=new e.constructor(e.source,t.exec(e));return n.lastIndex=e.lastIndex,n}},35932:(e,t,n)=>{var r=n(28897),i=n(58817);e.exports=function(e,t,n){(void 0===n||i(e[t],n))&&(void 0!==n||t in e)||r(e,t,n)}},36085:(e,t,n)=>{var r=n(86216),i=n(43803),o=n(54648);e.exports=function(e,t){return null==e?e:r(e,i(t),o)}},36157:(e,t,n)=>{var r=n(5518),i=n(54648);e.exports=function(e){return r(e,i(e))}},36301:e=>{var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,i=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,o=/^:\s*/,s=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,a=/^[;\s]*/,l=/^\s+|\s+$/g;function u(e){return e?e.replace(l,""):""}e.exports=function(e,l){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];l=l||{};var c=1,h=1;function d(e){var t=e.match(n);t&&(c+=t.length);var r=e.lastIndexOf("\n");h=~r?e.length-r:h+e.length}function f(){var e={line:c,column:h};return function(t){return t.position=new p(e),v(r),t}}function p(e){this.start=e,this.end={line:c,column:h},this.source=l.source}p.prototype.content=e;var m=[];function g(t){var n=Error(l.source+":"+c+":"+h+": "+t);if(n.reason=t,n.filename=l.source,n.line=c,n.column=h,n.source=e,l.silent)m.push(n);else throw n}function v(t){var n=t.exec(e);if(n){var r=n[0];return d(r),e=e.slice(r.length),n}}function y(e){var t;for(e=e||[];t=x();)!1!==t&&e.push(t);return e}function x(){var t=f();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return g("End of comment missing");var r=e.slice(2,n-2);return h+=2,d(r),e=e.slice(n),h+=2,t({type:"comment",comment:r})}}v(r);var b,w=[];for(y(w);b=function(){var e=f(),n=v(i);if(n){if(x(),!v(o))return g("property missing ':'");var r=v(s),l=e({type:"declaration",property:u(n[0].replace(t,"")),value:r?u(r[0].replace(t,"")):""});return v(a),l}}();)!1!==b&&(w.push(b),y(w));return w}},36488:(e,t,n)=>{var r=n(88706),i=n(65531),o=n(58430);e.exports=function(e){return i(e)?o(e):r(e)}},36645:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let r=n(88229)._(n(67357));function i(e,t){var n;let i={};"function"==typeof e&&(i.loader=e);let o={...i,...t};return(0,r.default)({...o,modules:null==(n=o.loadableGenerated)?void 0:n.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},36707:(e,t,n)=>{var r=n(11850),i=n(73800),o=n(99544),s=n(67460),a=n(94356);e.exports=function(e,t,n,l){if(!s(e))return e;t=i(t,e);for(var u=-1,c=t.length,h=c-1,d=e;null!=d&&++u<c;){var f=a(t[u]),p=n;if("__proto__"===f||"constructor"===f||"prototype"===f)break;if(u!=h){var m=d[f];void 0===(p=l?l(m,f,d):void 0)&&(p=s(m)?m:o(t[u+1])?[]:{})}r(d,f,p),d=d[f]}return e}},37562:(e,t,n)=>{var r=n(18489),i=n(94380),o=n(22471),s=n(15438),a=n(36488);e.exports=function(e){if(null==e)return 0;if(o(e))return s(e)?a(e):e.length;var t=i(e);return"[object Map]"==t||"[object Set]"==t?e.size:r(e).length}},38735:e=>{e.exports=function(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}},39208:(e,t,n)=>{var r=n(63457),i=n(73726),o=n(96294);e.exports=function(e){return"function"!=typeof e.constructor||o(e)?{}:r(i(e))}},41998:(e,t,n)=>{var r=n(49592);e.exports=n(31913)(function(e,t){return null==e?{}:r(e,t)})},43803:(e,t,n)=>{var r=n(13465);e.exports=function(e){return"function"==typeof e?e:r}},45285:(e,t,n)=>{"use strict";var r=n(25016),i=n(71814).Graph;function o(e,t,n,i){var o;do o=r.uniqueId(i);while(e.hasNode(o));return n.dummy=t,e.setNode(o,n),o}function s(e){return r.max(r.map(e.nodes(),function(t){var n=e.node(t).rank;if(!r.isUndefined(n))return n}))}e.exports={addDummyNode:o,simplify:function(e){var t=new i().setGraph(e.graph());return r.forEach(e.nodes(),function(n){t.setNode(n,e.node(n))}),r.forEach(e.edges(),function(n){var r=t.edge(n.v,n.w)||{weight:0,minlen:1},i=e.edge(n);t.setEdge(n.v,n.w,{weight:r.weight+i.weight,minlen:Math.max(r.minlen,i.minlen)})}),t},asNonCompoundGraph:function(e){var t=new i({multigraph:e.isMultigraph()}).setGraph(e.graph());return r.forEach(e.nodes(),function(n){e.children(n).length||t.setNode(n,e.node(n))}),r.forEach(e.edges(),function(n){t.setEdge(n,e.edge(n))}),t},successorWeights:function(e){var t=r.map(e.nodes(),function(t){var n={};return r.forEach(e.outEdges(t),function(t){n[t.w]=(n[t.w]||0)+e.edge(t).weight}),n});return r.zipObject(e.nodes(),t)},predecessorWeights:function(e){var t=r.map(e.nodes(),function(t){var n={};return r.forEach(e.inEdges(t),function(t){n[t.v]=(n[t.v]||0)+e.edge(t).weight}),n});return r.zipObject(e.nodes(),t)},intersectRect:function(e,t){var n,r,i=e.x,o=e.y,s=t.x-i,a=t.y-o,l=e.width/2,u=e.height/2;if(!s&&!a)throw Error("Not possible to find intersection inside of the rectangle");return Math.abs(a)*l>Math.abs(s)*u?(a<0&&(u=-u),n=u*s/a,r=u):(s<0&&(l=-l),n=l,r=l*a/s),{x:i+n,y:o+r}},buildLayerMatrix:function(e){var t=r.map(r.range(s(e)+1),function(){return[]});return r.forEach(e.nodes(),function(n){var i=e.node(n),o=i.rank;r.isUndefined(o)||(t[o][i.order]=n)}),t},normalizeRanks:function(e){var t=r.min(r.map(e.nodes(),function(t){return e.node(t).rank}));r.forEach(e.nodes(),function(n){var i=e.node(n);r.has(i,"rank")&&(i.rank-=t)})},removeEmptyRanks:function(e){var t=r.min(r.map(e.nodes(),function(t){return e.node(t).rank})),n=[];r.forEach(e.nodes(),function(r){var i=e.node(r).rank-t;n[i]||(n[i]=[]),n[i].push(r)});var i=0,o=e.graph().nodeRankFactor;r.forEach(n,function(t,n){r.isUndefined(t)&&n%o!=0?--i:i&&r.forEach(t,function(t){e.node(t).rank+=i})})},addBorderNode:function(e,t,n,r){var i={width:0,height:0};return arguments.length>=4&&(i.rank=n,i.order=r),o(e,"border",i,t)},maxRank:s,partition:function(e,t){var n={lhs:[],rhs:[]};return r.forEach(e,function(e){t(e)?n.lhs.push(e):n.rhs.push(e)}),n},time:function(e,t){var n=r.now();try{return t()}finally{console.log(e+" time: "+(r.now()-n)+"ms")}},notime:function(e,t){return t()}}},45577:(e,t,n)=>{var r=n(85855),i=0;e.exports=function(e){var t=++i;return r(e)+t}},46853:(e,t,n)=>{var r=n(25016),i=n(71814).Graph;e.exports=function(e,t,n){var o=function(e){for(var t;e.hasNode(t=r.uniqueId("_root")););return t}(e),s=new i({compound:!0}).setGraph({root:o}).setDefaultNodeLabel(function(t){return e.node(t)});return r.forEach(e.nodes(),function(i){var a=e.node(i),l=e.parent(i);(a.rank===t||a.minRank<=t&&t<=a.maxRank)&&(s.setNode(i),s.setParent(i,l||o),r.forEach(e[n](i),function(t){var n=t.v===i?t.w:t.v,o=s.edge(n,i),a=r.isUndefined(o)?0:o.weight;s.setEdge(n,i,{weight:e.edge(t).weight+a})}),r.has(a,"minRank")&&s.setNode(i,{borderLeft:a.borderLeft[t],borderRight:a.borderRight[t]}))}),s}},47165:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(12115);let i=r.forwardRef(function(e,t){let{title:n,titleId:i,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),n?r.createElement("title",{id:i},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 12h14"}))})},47312:(e,t,n)=>{var r=n(18489),i=n(94380),o=n(9813),s=n(39608),a=n(22471),l=n(33497),u=n(96294),c=n(35190),h=Object.prototype.hasOwnProperty;e.exports=function(e){if(null==e)return!0;if(a(e)&&(s(e)||"string"==typeof e||"function"==typeof e.splice||l(e)||c(e)||o(e)))return!e.length;var t=i(e);if("[object Map]"==t||"[object Set]"==t)return!e.size;if(u(e))return!r(e).length;for(var n in e)if(h.call(e,n))return!1;return!0}},47532:(e,t,n)=>{var r=n(65646),i=n(78558),o=n(54648);e.exports=function(e){return r(e,o,i)}},47655:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(12115);let i=r.forwardRef(function(e,t){let{title:n,titleId:i,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),n?r.createElement("title",{id:i},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 15 3 9m0 0 6-6M3 9h12a6 6 0 0 1 0 12h-3"}))})},48987:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(12115);let i=r.forwardRef(function(e,t){let{title:n,titleId:i,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),n?r.createElement("title",{id:i},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))})},49382:(e,t,n)=>{"use strict";var r=n(25016);e.exports=function(e,t){var n={};return r.forEach(e,function(e,t){var i=n[e.v]={indegree:0,in:[],out:[],vs:[e.v],i:t};r.isUndefined(e.barycenter)||(i.barycenter=e.barycenter,i.weight=e.weight)}),r.forEach(t.edges(),function(e){var t=n[e.v],i=n[e.w];r.isUndefined(t)||r.isUndefined(i)||(i.indegree++,t.out.push(n[e.w]))}),function(e){for(var t=[];e.length;){var n=e.pop();t.push(n),r.forEach(n.in.reverse(),function(e){return function(t){!t.merged&&(r.isUndefined(t.barycenter)||r.isUndefined(e.barycenter)||t.barycenter>=e.barycenter)&&function(e,t){var n=0,r=0;e.weight&&(n+=e.barycenter*e.weight,r+=e.weight),t.weight&&(n+=t.barycenter*t.weight,r+=t.weight),e.vs=t.vs.concat(e.vs),e.barycenter=n/r,e.weight=r,e.i=Math.min(t.i,e.i),t.merged=!0}(e,t)}}(n)),r.forEach(n.out,function(t){return function(n){n.in.push(t),0==--n.indegree&&e.push(n)}}(n))}return r.map(r.filter(t,function(e){return!e.merged}),function(e){return r.pick(e,["vs","i","barycenter","weight"])})}(r.filter(n,function(e){return!e.indegree}))}},49592:(e,t,n)=>{var r=n(7749),i=n(20134);e.exports=function(e,t){return r(e,t,function(t,n){return i(e,n)})}},49719:(e,t,n)=>{e.exports={Graph:n(77951),version:n(55499)}},51505:(e,t,n)=>{var r=n(13703),i=n(33332),o=n(49840),s=o&&o.isSet;e.exports=s?i(s):r},51508:(e,t,n)=>{"use strict";n.d(t,{Q:()=>r});let r=(0,n(12115).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},52014:(e,t,n)=>{var r=n(25016),i=n(45285);function o(e,t,n,r,o,s){var a=o[t][s-1],l=i.addDummyNode(e,"border",{width:0,height:0,rank:s,borderType:t},n);o[t][s]=l,e.setParent(l,r),a&&e.setEdge(a,l,{weight:1})}e.exports=function(e){r.forEach(e.children(),function t(n){var i=e.children(n),s=e.node(n);if(i.length&&r.forEach(i,t),r.has(s,"minRank")){s.borderLeft=[],s.borderRight=[];for(var a=s.minRank,l=s.maxRank+1;a<l;++a)o(e,"borderLeft","_bl",n,s,a),o(e,"borderRight","_br",n,s,a)}})}},53360:e=>{"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},s=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,i=t.call(e,"constructor"),o=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!i&&!o)return!1;for(r in e);return void 0===r||t.call(e,r)},a=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},l=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;else if(i)return i(e,n).value}return e[n]};e.exports=function e(){var t,n,r,i,u,c,h=arguments[0],d=1,f=arguments.length,p=!1;for("boolean"==typeof h&&(p=h,h=arguments[1]||{},d=2),(null==h||"object"!=typeof h&&"function"!=typeof h)&&(h={});d<f;++d)if(t=arguments[d],null!=t)for(n in t)r=l(h,n),h!==(i=l(t,n))&&(p&&i&&(s(i)||(u=o(i)))?(u?(u=!1,c=r&&o(r)?r:[]):c=r&&s(r)?r:{},a(h,{name:n,newValue:e(p,c,i)})):void 0!==i&&a(h,{name:n,newValue:i}));return h}},53724:function(e,t,n){"use strict";var r=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(87924)),i=n(31300);function o(e,t){var n={};return e&&"string"==typeof e&&(0,r.default)(e,function(e,r){e&&r&&(n[(0,i.camelCase)(e,t)]=r)}),n}o.default=o,e.exports=o},54260:(e,t,n)=>{var r;try{r={clone:n(77292),constant:n(50687),each:n(7854),filter:n(14909),has:n(55041),isArray:n(39608),isEmpty:n(47312),isFunction:n(40139),isUndefined:n(23406),keys:n(35095),map:n(69363),reduce:n(78701),size:n(37562),transform:n(62425),union:n(23746),values:n(89865)}}catch(e){}r||(r=window._),e.exports=r},54542:(e,t,n)=>{"use strict";var r=n(25016);e.exports=function(e,t){for(var n=0,i=1;i<t.length;++i)n+=function(e,t,n){for(var i=r.zipObject(n,r.map(n,function(e,t){return t})),o=r.flatten(r.map(t,function(t){return r.sortBy(r.map(e.outEdges(t),function(t){return{pos:i[t.w],weight:e.edge(t).weight}}),"pos")}),!0),s=1;s<n.length;)s<<=1;var a=2*s-1;s-=1;var l=r.map(Array(a),function(){return 0}),u=0;return r.forEach(o.forEach(function(e){var t=e.pos+s;l[t]+=e.weight;for(var n=0;t>0;)t%2&&(n+=l[t+1]),t=t-1>>1,l[t]+=e.weight;u+=e.weight*n})),u}(e,t[i-1],t[i]);return n}},54648:(e,t,n)=>{var r=n(11670),i=n(78302),o=n(22471);e.exports=function(e){return o(e)?r(e,!0):i(e)}},54899:(e,t,n)=>{var r=n(49719);e.exports={Graph:r.Graph,json:n(21775),alg:n(83314),version:r.version}},54968:(e,t,n)=>{"use strict";var r=n(25016),i=n(45285);e.exports={run:function(e){e.graph().dummyChains=[],r.forEach(e.edges(),function(t){!function(e,t){var n,r,o,s=t.v,a=e.node(s).rank,l=t.w,u=e.node(l).rank,c=t.name,h=e.edge(t),d=h.labelRank;if(u!==a+1){for(e.removeEdge(t),o=0,++a;a<u;++o,++a)h.points=[],r={width:0,height:0,edgeLabel:h,edgeObj:t,rank:a},n=i.addDummyNode(e,"edge",r,"_d"),a===d&&(r.width=h.width,r.height=h.height,r.dummy="edge-label",r.labelpos=h.labelpos),e.setEdge(s,n,{weight:h.weight},c),0===o&&e.graph().dummyChains.push(n),s=n;e.setEdge(s,l,{weight:h.weight},c)}}(e,t)})},undo:function(e){r.forEach(e.graph().dummyChains,function(t){var n,r=e.node(t),i=r.edgeLabel;for(e.setEdge(r.edgeObj,i);r.dummy;)n=e.successors(t)[0],e.removeNode(t),i.points.push({x:r.x,y:r.y}),"edge-label"===r.dummy&&(i.x=r.x,i.y=r.y,i.width=r.width,i.height=r.height),t=n,r=e.node(t)})}}},55028:(e,t,n)=>{"use strict";n.d(t,{default:()=>i.a});var r=n(36645),i=n.n(r)},55041:(e,t,n)=>{var r=n(62891),i=n(50111);e.exports=function(e,t){return null!=e&&i(e,t,r)}},55076:(e,t,n)=>{"use strict";var r=n(25016),i=n(71814).Graph,o=n(45285);function s(e,t){var n={};return r.reduce(t,function(t,i){var o=0,s=0,a=t.length,u=r.last(i);return r.forEach(i,function(t,c){var h=function(e,t){if(e.node(t).dummy)return r.find(e.predecessors(t),function(t){return e.node(t).dummy})}(e,t),d=h?e.node(h).order:a;(h||t===u)&&(r.forEach(i.slice(s,c+1),function(t){r.forEach(e.predecessors(t),function(r){var i=e.node(r),s=i.order;(s<o||d<s)&&!(i.dummy&&e.node(t).dummy)&&l(n,r,t)})}),s=c+1,o=d)}),i}),n}function a(e,t){var n={};function i(t,i,o,s,a){var u;r.forEach(r.range(i,o),function(i){u=t[i],e.node(u).dummy&&r.forEach(e.predecessors(u),function(t){var r=e.node(t);r.dummy&&(r.order<s||r.order>a)&&l(n,t,u)})})}return r.reduce(t,function(t,n){var o,s=-1,a=0;return r.forEach(n,function(r,l){if("border"===e.node(r).dummy){var u=e.predecessors(r);u.length&&(o=e.node(u[0]).order,i(n,a,l,s,o),a=l,s=o)}i(n,a,n.length,o,t.length)}),n}),n}function l(e,t,n){if(t>n){var r=t;t=n,n=r}var i=e[t];i||(e[t]=i={}),i[n]=!0}function u(e,t,n){if(t>n){var i=t;t=n,n=i}return r.has(e[t],n)}function c(e,t,n,i){var o={},s={},a={};return r.forEach(t,function(e){r.forEach(e,function(e,t){o[e]=e,s[e]=e,a[e]=t})}),r.forEach(t,function(e){var t=-1;r.forEach(e,function(e){var l=i(e);if(l.length)for(var c=((l=r.sortBy(l,function(e){return a[e]})).length-1)/2,h=Math.floor(c),d=Math.ceil(c);h<=d;++h){var f=l[h];s[e]===e&&t<a[f]&&!u(n,e,f)&&(s[f]=e,s[e]=o[e]=o[f],t=a[f])}})}),{root:o,align:s}}function h(e,t,n,o,s){var a,l,u,c,h,d,f,p,m,g,v={},y=(a=e,l=t,u=n,c=s,p=new i,g=(h=(m=a.graph()).nodesep,d=m.edgesep,f=c,function(e,t,n){var i,o,s=e.node(t),a=e.node(n);if(i=0+s.width/2,r.has(s,"labelpos"))switch(s.labelpos.toLowerCase()){case"l":o=-s.width/2;break;case"r":o=s.width/2}if(o&&(i+=f?o:-o),o=0,i+=(s.dummy?d:h)/2,i+=(a.dummy?d:h)/2,i+=a.width/2,r.has(a,"labelpos"))switch(a.labelpos.toLowerCase()){case"l":o=a.width/2;break;case"r":o=-a.width/2}return o&&(i+=f?o:-o),o=0,i}),r.forEach(l,function(e){var t;r.forEach(e,function(e){var n=u[e];if(p.setNode(n),t){var r=u[t],i=p.edge(r,n);p.setEdge(r,n,Math.max(g(a,e,t),i||0))}t=e})}),p),x=s?"borderLeft":"borderRight";function b(e,t){for(var n=y.nodes(),r=n.pop(),i={};r;)i[r]?e(r):(i[r]=!0,n.push(r),n=n.concat(t(r))),r=n.pop()}return b(function(e){v[e]=y.inEdges(e).reduce(function(e,t){return Math.max(e,v[t.v]+y.edge(t))},0)},y.predecessors.bind(y)),b(function(t){var n=y.outEdges(t).reduce(function(e,t){return Math.min(e,v[t.w]-y.edge(t))},Number.POSITIVE_INFINITY),r=e.node(t);n!==Number.POSITIVE_INFINITY&&r.borderType!==x&&(v[t]=Math.max(v[t],n))},y.successors.bind(y)),r.forEach(o,function(e){v[e]=v[n[e]]}),v}function d(e,t){return r.minBy(r.values(t),function(t){var n=Number.NEGATIVE_INFINITY,i=Number.POSITIVE_INFINITY;return r.forIn(t,function(t,r){var o,s,a=(o=e,s=r,o.node(s).width/2);n=Math.max(t+a,n),i=Math.min(t-a,i)}),n-i})}function f(e,t){var n=r.values(t),i=r.min(n),o=r.max(n);r.forEach(["u","d"],function(n){r.forEach(["l","r"],function(s){var a,l=n+s,u=e[l];if(u!==t){var c=r.values(u);(a="l"===s?i-r.min(c):o-r.max(c))&&(e[l]=r.mapValues(u,function(e){return e+a}))}})})}function p(e,t){return r.mapValues(e.ul,function(n,i){if(t)return e[t.toLowerCase()][i];var o=r.sortBy(r.map(e,i));return(o[1]+o[2])/2})}e.exports={positionX:function(e){var t,n=o.buildLayerMatrix(e),i=r.merge(s(e,n),a(e,n)),l={};r.forEach(["u","d"],function(o){t="u"===o?n:r.values(n).reverse(),r.forEach(["l","r"],function(n){"r"===n&&(t=r.map(t,function(e){return r.values(e).reverse()}));var s=("u"===o?e.predecessors:e.successors).bind(e),a=c(e,t,i,s),u=h(e,t,a.root,a.align,"r"===n);"r"===n&&(u=r.mapValues(u,function(e){return-e})),l[o+n]=u})});var u=d(e,l);return f(l,u),p(l,e.graph().align)},findType1Conflicts:s,findType2Conflicts:a,addConflict:l,hasConflict:u,verticalAlignment:c,horizontalCompaction:h,alignCoordinates:f,findSmallestWidthAlignment:d,balance:p}},55499:e=>{e.exports="2.1.8"},55836:(e,t,n)=>{var r=n(22471),i=n(48611);e.exports=function(e){return i(e)&&r(e)}},56065:(e,t,n)=>{var r=n(58965);e.exports=function(e,t){return r(e,t,"pre")}},57765:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(12115);let i=r.forwardRef(function(e,t){let{title:n,titleId:i,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),n?r.createElement("title",{id:i},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))})},58430:e=>{var t="\ud800-\udfff",n="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",r="\ud83c[\udffb-\udfff]",i="[^"+t+"]",o="(?:\ud83c[\udde6-\uddff]){2}",s="[\ud800-\udbff][\udc00-\udfff]",a="(?:"+n+"|"+r+")?",l="[\\ufe0e\\ufe0f]?",u="(?:\\u200d(?:"+[i,o,s].join("|")+")"+l+a+")*",c=RegExp(r+"(?="+r+")|"+("(?:"+[i+n+"?",n,o,s,"["+t+"]"].join("|"))+")"+(l+a+u),"g");e.exports=function(e){for(var t=c.lastIndex=0;c.test(e);)++t;return t}},58965:(e,t,n)=>{var r=n(54260);e.exports=function(e,t,n){r.isArray(t)||(t=[t]);var i=(e.isDirected()?e.successors:e.neighbors).bind(e),o=[],s={};return r.each(t,function(t){if(!e.hasNode(t))throw Error("Graph does not have node: "+t);!function e(t,n,i,o,s,a){!r.has(o,n)&&(o[n]=!0,i||a.push(n),r.each(s(n),function(n){e(t,n,i,o,s,a)}),i&&a.push(n))}(e,t,"post"===n,s,i,o)}),o}},59238:(e,t,n)=>{var r=n(5518),i=n(38649);e.exports=function(e,t){return r(e,i(e),t)}},59507:(e,t,n)=>{var r=n(19229),i=n(33332),o=n(49840),s=o&&o.isMap;e.exports=s?i(s):r},60013:(e,t,n)=>{var r=n(21087),i=n(58817),o=n(39641),s=n(54648),a=Object.prototype,l=a.hasOwnProperty;e.exports=r(function(e,t){e=Object(e);var n=-1,r=t.length,u=r>2?t[2]:void 0;for(u&&o(t[0],t[1],u)&&(r=1);++n<r;)for(var c=t[n],h=s(c),d=-1,f=h.length;++d<f;){var p=h[d],m=e[p];(void 0===m||i(m,a[p])&&!l.call(e,p))&&(e[p]=c[p])}return e})},60760:(e,t,n)=>{"use strict";n.d(t,{N:()=>v});var r=n(95155),i=n(12115),o=n(90869),s=n(82885),a=n(97494),l=n(80845),u=n(51508);class c extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=e instanceof HTMLElement&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function h(e){let{children:t,isPresent:n,anchorX:o}=e,s=(0,i.useId)(),a=(0,i.useRef)(null),l=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:h}=(0,i.useContext)(u.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:i,right:u}=l.current;if(n||!a.current||!e||!t)return;a.current.dataset.motionPopId=s;let c=document.createElement("style");return h&&(c.nonce=h),document.head.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(s,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===o?"left: ".concat(i):"right: ".concat(u),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{document.head.contains(c)&&document.head.removeChild(c)}},[n]),(0,r.jsx)(c,{isPresent:n,childRef:a,sizeRef:l,children:i.cloneElement(t,{ref:a})})}let d=e=>{let{children:t,initial:n,isPresent:o,onExitComplete:a,custom:u,presenceAffectsLayout:c,mode:d,anchorX:p}=e,m=(0,s.M)(f),g=(0,i.useId)(),v=!0,y=(0,i.useMemo)(()=>(v=!1,{id:g,initial:n,isPresent:o,custom:u,onExitComplete:e=>{for(let t of(m.set(e,!0),m.values()))if(!t)return;a&&a()},register:e=>(m.set(e,!1),()=>m.delete(e))}),[o,m,a]);return c&&v&&(y={...y}),(0,i.useMemo)(()=>{m.forEach((e,t)=>m.set(t,!1))},[o]),i.useEffect(()=>{o||m.size||!a||a()},[o]),"popLayout"===d&&(t=(0,r.jsx)(h,{isPresent:o,anchorX:p,children:t})),(0,r.jsx)(l.t.Provider,{value:y,children:t})};function f(){return new Map}var p=n(32082);let m=e=>e.key||"";function g(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let v=e=>{let{children:t,custom:n,initial:l=!0,onExitComplete:u,presenceAffectsLayout:c=!0,mode:h="sync",propagate:f=!1,anchorX:v="left"}=e,[y,x]=(0,p.xQ)(f),b=(0,i.useMemo)(()=>g(t),[t]),w=f&&!y?[]:b.map(m),k=(0,i.useRef)(!0),E=(0,i.useRef)(b),S=(0,s.M)(()=>new Map),[T,P]=(0,i.useState)(b),[C,A]=(0,i.useState)(b);(0,a.E)(()=>{k.current=!1,E.current=b;for(let e=0;e<C.length;e++){let t=m(C[e]);w.includes(t)?S.delete(t):!0!==S.get(t)&&S.set(t,!1)}},[C,w.length,w.join("-")]);let M=[];if(b!==T){let e=[...b];for(let t=0;t<C.length;t++){let n=C[t],r=m(n);w.includes(r)||(e.splice(t,0,n),M.push(n))}return"wait"===h&&M.length&&(e=M),A(g(e)),P(b),null}let{forceRender:I}=(0,i.useContext)(o.L);return(0,r.jsx)(r.Fragment,{children:C.map(e=>{let t=m(e),i=(!f||!!y)&&(b===C||w.includes(t));return(0,r.jsx)(d,{isPresent:i,initial:(!k.current||!!l)&&void 0,custom:n,presenceAffectsLayout:c,mode:h,onExitComplete:i?void 0:()=>{if(!S.has(t))return;S.set(t,!0);let e=!0;S.forEach(t=>{t||(e=!1)}),e&&(null==I||I(),A(E.current),f&&(null==x||x()),u&&u())},anchorX:v,children:e},t)})})}},62146:(e,t,n)=>{"use strict";function r(e){let{reason:t,children:n}=e;return n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return r}}),n(45262)},62425:(e,t,n)=>{var r=n(72288),i=n(63457),o=n(20480),s=n(18028),a=n(73726),l=n(39608),u=n(33497),c=n(40139),h=n(67460),d=n(35190);e.exports=function(e,t,n){var f=l(e),p=f||u(e)||d(e);if(t=s(t,4),null==n){var m=e&&e.constructor;n=p?f?new m:[]:h(e)&&c(m)?i(a(e)):{}}return(p?r:o)(e,function(e,r,i){return t(n,e,r,i)}),n}},62729:()=>{},62795:e=>{e.exports=function(e,t,n,r){var i=-1,o=null==e?0:e.length;for(r&&o&&(n=e[++i]);++i<o;)n=t(n,e[i],i,e);return n}},62891:e=>{var t=Object.prototype.hasOwnProperty;e.exports=function(e,n){return null!=e&&t.call(e,n)}},63457:(e,t,n)=>{var r=n(67460),i=Object.create;e.exports=function(){function e(){}return function(t){if(!r(t))return{};if(i)return i(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}()},63928:(e,t,n)=>{var r=n(25016),i=n(45285);e.exports={run:function(e){var t,n,o,s=i.addDummyNode(e,"root",{},"_root"),a=(t=e,n={},r.forEach(t.children(),function(e){!function e(i,o){var s=t.children(i);s&&s.length&&r.forEach(s,function(t){e(t,o+1)}),n[i]=o}(e,1)}),n),l=r.max(r.values(a))-1,u=2*l+1;e.graph().nestingRoot=s,r.forEach(e.edges(),function(t){e.edge(t).minlen*=u});var c=(o=e,r.reduce(o.edges(),function(e,t){return e+o.edge(t).weight},0)+1);r.forEach(e.children(),function(t){!function e(t,n,o,s,a,l,u){var c=t.children(u);if(!c.length){u!==n&&t.setEdge(n,u,{weight:0,minlen:o});return}var h=i.addBorderNode(t,"_bt"),d=i.addBorderNode(t,"_bb"),f=t.node(u);t.setParent(h,u),f.borderTop=h,t.setParent(d,u),f.borderBottom=d,r.forEach(c,function(r){e(t,n,o,s,a,l,r);var i=t.node(r),c=i.borderTop?i.borderTop:r,f=i.borderBottom?i.borderBottom:r,p=i.borderTop?s:2*s,m=c!==f?1:a-l[u]+1;t.setEdge(h,c,{weight:p,minlen:m,nestingEdge:!0}),t.setEdge(f,d,{weight:p,minlen:m,nestingEdge:!0})}),t.parent(u)||t.setEdge(n,h,{weight:0,minlen:a+l[u]})}(e,s,u,c,l,a,t)}),e.graph().nodeRankFactor=u},cleanup:function(e){var t=e.graph();e.removeNode(t.nestingRoot),delete t.nestingRoot,r.forEach(e.edges(),function(t){e.edge(t).nestingEdge&&e.removeEdge(t)})}}},63957:(e,t,n)=>{var r=n(5518),i=n(78558);e.exports=function(e,t){return r(e,i(e),t)}},64054:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{bindSnapshot:function(){return s},createAsyncLocalStorage:function(){return o},createSnapshot:function(){return a}});let n=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class r{disable(){throw n}getStore(){}run(){throw n}exit(){throw n}enterWith(){throw n}static bind(e){return e}}let i="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function o(){return i?new i:new r}function s(e){return i?i.bind(e):r.bind(e)}function a(){return i?i.snapshot():function(e,...t){return e(...t)}}},65265:(e,t,n)=>{var r=n(35932),i=n(24035),o=n(11368),s=n(86030),a=n(39208),l=n(9813),u=n(39608),c=n(55836),h=n(33497),d=n(40139),f=n(67460),p=n(44482),m=n(35190),g=n(38735),v=n(36157);e.exports=function(e,t,n,y,x,b,w){var k=g(e,n),E=g(t,n),S=w.get(E);if(S)return void r(e,n,S);var T=b?b(k,E,n+"",e,t,w):void 0,P=void 0===T;if(P){var C=u(E),A=!C&&h(E),M=!C&&!A&&m(E);T=E,C||A||M?u(k)?T=k:c(k)?T=s(k):A?(P=!1,T=i(E,!0)):M?(P=!1,T=o(E,!0)):T=[]:p(E)||l(E)?(T=k,l(k)?T=v(k):(!f(k)||d(k))&&(T=a(E))):P=!1}P&&(w.set(E,T),x(T,E,y,b,w),w.delete(E)),r(e,n,T)}},65679:(e,t,n)=>{"use strict";var r=n(25016);e.exports=function(e){var t={},n=r.filter(e.nodes(),function(t){return!e.children(t).length}),i=r.max(r.map(n,function(t){return e.node(t).rank})),o=r.map(r.range(i+1),function(){return[]}),s=r.sortBy(n,function(t){return e.node(t).rank});return r.forEach(s,function n(i){r.has(t,i)||(t[i]=!0,o[e.node(i).rank].push(i),r.forEach(e.successors(i),n))}),o}},67357:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let r=n(95155),i=n(12115),o=n(62146);function s(e){return{default:e&&"default"in e?e.default:e}}n(10255);let a={loader:()=>Promise.resolve(s(()=>null)),loading:null,ssr:!0},l=function(e){let t={...a,...e},n=(0,i.lazy)(()=>t.loader().then(s)),l=t.loading;function u(e){let s=l?(0,r.jsx)(l,{isLoading:!0,pastDelay:!0,error:null}):null,a=!t.ssr||!!t.loading,u=a?i.Suspense:i.Fragment,c=t.ssr?(0,r.jsxs)(r.Fragment,{children:[null,(0,r.jsx)(n,{...e})]}):(0,r.jsx)(o.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(n,{...e})});return(0,r.jsx)(u,{...a?{fallback:s}:{},children:c})}return u.displayName="LoadableComponent",u}},68972:(e,t,n)=>{"use strict";n.d(t,{B:()=>r});let r="undefined"!=typeof window},69471:(e,t,n)=>{var r=n(54260),i=n(84306);e.exports=function(e){return r.filter(i(e),function(t){return t.length>1||1===t.length&&e.hasEdge(t[0],t[0])})}},69934:function(e,t,n){e.exports=function(e){var t=[function(e,t,n){var r=n(1),i=function(e){e&&e("layout","dagre",r)};"undefined"!=typeof cytoscape&&i(cytoscape),e.exports=i},function(e,t,n){function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var i=function(e){return"function"==typeof e},o=n(2),s=n(3),a=n(4);function l(e){this.options=s({},o,e)}l.prototype.run=function(){var e,t=this.options,n=t.cy,o=t.eles,s=function(e,t){return i(t)?t.apply(e,[e]):t},l=t.boundingBox||{x1:0,y1:0,w:n.width(),h:n.height()};void 0===l.x2&&(l.x2=l.x1+l.w),void 0===l.w&&(l.w=l.x2-l.x1),void 0===l.y2&&(l.y2=l.y1+l.h),void 0===l.h&&(l.h=l.y2-l.y1);var u=new a.graphlib.Graph({multigraph:!0,compound:!0}),c={},h=function(e,t){null!=t&&(c[e]=t)};h("nodesep",t.nodeSep),h("edgesep",t.edgeSep),h("ranksep",t.rankSep),h("rankdir",t.rankDir),h("align",t.align),h("ranker",t.ranker),h("acyclicer",t.acyclicer),u.setGraph(c),u.setDefaultEdgeLabel(function(){return{}}),u.setDefaultNodeLabel(function(){return{}});var d=o.nodes();i(t.sort)&&(d=d.sort(t.sort));for(var f=0;f<d.length;f++){var p=d[f],m=p.layoutDimensions(t);u.setNode(p.id(),{width:m.w,height:m.h,name:p.id()})}for(var g=0;g<d.length;g++){var v=d[g];v.isChild()&&u.setParent(v.id(),v.parent().id())}var y=o.edges().stdFilter(function(e){return!e.source().isParent()&&!e.target().isParent()});i(t.sort)&&(y=y.sort(t.sort));for(var x=0;x<y.length;x++){var b=y[x];u.setEdge(b.source().id(),b.target().id(),{minlen:s(b,t.minLen),weight:s(b,t.edgeWeight),name:b.id()},b.id())}a.layout(u);for(var w=u.nodes(),k=0;k<w.length;k++){var E=w[k],S=u.node(E);n.getElementById(E).scratch().dagre=S}t.boundingBox?(e={x1:1/0,x2:-1/0,y1:1/0,y2:-1/0},d.forEach(function(t){var n=t.scratch().dagre;e.x1=Math.min(e.x1,n.x),e.x2=Math.max(e.x2,n.x),e.y1=Math.min(e.y1,n.y),e.y2=Math.max(e.y2,n.y)}),e.w=e.x2-e.x1,e.h=e.y2-e.y1):e=l;var T=function(n){if(!t.boundingBox)return n;var r=0===e.w?0:(n.x-e.x1)/e.w,i=0===e.h?0:(n.y-e.y1)/e.h;return{x:l.x1+r*l.w,y:l.y1+i*l.h}};return d.layoutPositions(this,t,function(e){var t=(e="object"===r(e)?e:this).scratch().dagre;return T({x:t.x,y:t.y})}),this},e.exports=l},function(e,t){e.exports={nodeSep:void 0,edgeSep:void 0,rankSep:void 0,rankDir:void 0,align:void 0,acyclicer:void 0,ranker:void 0,minLen:function(e){return 1},edgeWeight:function(e){return 1},fit:!0,padding:30,spacingFactor:void 0,nodeDimensionsIncludeLabels:!1,animate:!1,animateFilter:function(e,t){return!0},animationDuration:500,animationEasing:void 0,boundingBox:void 0,transform:function(e,t){return t},ready:function(){},sort:void 0,stop:function(){}}},function(e,t){e.exports=null!=Object.assign?Object.assign.bind(Object):function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return n.forEach(function(t){Object.keys(t).forEach(function(n){return e[n]=t[n]})}),e}},function(t,n){t.exports=e}],n={};function r(e){if(n[e])return n[e].exports;var i=n[e]={i:e,l:!1,exports:{}};return t[e].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=t,r.c=n,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t||4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,(function(t){return e[t]}).bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=0)}(n(28175))},70451:(e,t,n)=>{var r=n(29721);e.exports=function(e){try{r(e)}catch(e){if(e instanceof r.CycleException)return!1;throw e}return!0}},70865:(e,t,n)=>{var r=n(11850),i=n(79243);e.exports=function(e,t){return i(e||[],t||[],r)}},71524:(e,t,n)=>{"use strict";var r=n(25016),i=n(65679),o=n(54542),s=n(89377),a=n(46853),l=n(79713),u=n(71814).Graph,c=n(45285);function h(e,t,n){return r.map(t,function(t){return a(e,t,n)})}function d(e,t){r.forEach(t,function(t){r.forEach(t,function(t,n){e.node(t).order=n})})}e.exports=function(e){var t=c.maxRank(e),n=h(e,r.range(1,t+1),"inEdges"),a=h(e,r.range(t-1,-1,-1),"outEdges"),f=i(e);d(e,f);for(var p,m=Number.POSITIVE_INFINITY,g=0,v=0;v<4;++g,++v){(function(e,t){var n=new u;r.forEach(e,function(e){var i=e.graph().root,o=s(e,i,n,t);r.forEach(o.vs,function(t,n){e.node(t).order=n}),l(e,n,o.vs)})})(g%2?n:a,g%4>=2),f=c.buildLayerMatrix(e);var y=o(e,f);y<m&&(v=0,p=r.cloneDeep(f),m=y)}d(e,p)}},71814:(e,t,n)=>{var r;try{r=n(54899)}catch(e){}r||(r=window.graphlib),e.exports=r},71939:(e,t,n)=>{var r=n(67472),i=n(35932),o=n(86216),s=n(65265),a=n(67460),l=n(54648),u=n(38735);e.exports=function e(t,n,c,h,d){t!==n&&o(n,function(o,l){if(d||(d=new r),a(o))s(t,n,l,c,e,h,d);else{var f=h?h(u(t,l),o,l+"",t,n,d):void 0;void 0===f&&(f=o),i(t,l,f)}},l)}},72288:e=>{e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n,e););return e}},74500:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(12115);let i=r.forwardRef(function(e,t){let{title:n,titleId:i,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),n?r.createElement("title",{id:i},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))})},77292:(e,t,n)=>{var r=n(86110);e.exports=function(e){return r(e,4)}},77311:e=>{e.exports="0.8.5"},77951:(e,t,n)=>{"use strict";var r=n(54260);function i(e){this._isDirected=!r.has(e,"directed")||e.directed,this._isMultigraph=!!r.has(e,"multigraph")&&e.multigraph,this._isCompound=!!r.has(e,"compound")&&e.compound,this._label=void 0,this._defaultNodeLabelFn=r.constant(void 0),this._defaultEdgeLabelFn=r.constant(void 0),this._nodes={},this._isCompound&&(this._parent={},this._children={},this._children["\0"]={}),this._in={},this._preds={},this._out={},this._sucs={},this._edgeObjs={},this._edgeLabels={}}function o(e,t){e[t]?e[t]++:e[t]=1}function s(e,t){--e[t]||delete e[t]}function a(e,t,n,i){var o=""+t,s=""+n;if(!e&&o>s){var a=o;o=s,s=a}return o+"\x01"+s+"\x01"+(r.isUndefined(i)?"\0":i)}function l(e,t){return a(e,t.v,t.w,t.name)}e.exports=i,i.prototype._nodeCount=0,i.prototype._edgeCount=0,i.prototype.isDirected=function(){return this._isDirected},i.prototype.isMultigraph=function(){return this._isMultigraph},i.prototype.isCompound=function(){return this._isCompound},i.prototype.setGraph=function(e){return this._label=e,this},i.prototype.graph=function(){return this._label},i.prototype.setDefaultNodeLabel=function(e){return r.isFunction(e)||(e=r.constant(e)),this._defaultNodeLabelFn=e,this},i.prototype.nodeCount=function(){return this._nodeCount},i.prototype.nodes=function(){return r.keys(this._nodes)},i.prototype.sources=function(){var e=this;return r.filter(this.nodes(),function(t){return r.isEmpty(e._in[t])})},i.prototype.sinks=function(){var e=this;return r.filter(this.nodes(),function(t){return r.isEmpty(e._out[t])})},i.prototype.setNodes=function(e,t){var n=arguments,i=this;return r.each(e,function(e){n.length>1?i.setNode(e,t):i.setNode(e)}),this},i.prototype.setNode=function(e,t){return r.has(this._nodes,e)?arguments.length>1&&(this._nodes[e]=t):(this._nodes[e]=arguments.length>1?t:this._defaultNodeLabelFn(e),this._isCompound&&(this._parent[e]="\0",this._children[e]={},this._children["\0"][e]=!0),this._in[e]={},this._preds[e]={},this._out[e]={},this._sucs[e]={},++this._nodeCount),this},i.prototype.node=function(e){return this._nodes[e]},i.prototype.hasNode=function(e){return r.has(this._nodes,e)},i.prototype.removeNode=function(e){var t=this;if(r.has(this._nodes,e)){var n=function(e){t.removeEdge(t._edgeObjs[e])};delete this._nodes[e],this._isCompound&&(this._removeFromParentsChildList(e),delete this._parent[e],r.each(this.children(e),function(e){t.setParent(e)}),delete this._children[e]),r.each(r.keys(this._in[e]),n),delete this._in[e],delete this._preds[e],r.each(r.keys(this._out[e]),n),delete this._out[e],delete this._sucs[e],--this._nodeCount}return this},i.prototype.setParent=function(e,t){if(!this._isCompound)throw Error("Cannot set parent in a non-compound graph");if(r.isUndefined(t))t="\0";else{t+="";for(var n=t;!r.isUndefined(n);n=this.parent(n))if(n===e)throw Error("Setting "+t+" as parent of "+e+" would create a cycle");this.setNode(t)}return this.setNode(e),this._removeFromParentsChildList(e),this._parent[e]=t,this._children[t][e]=!0,this},i.prototype._removeFromParentsChildList=function(e){delete this._children[this._parent[e]][e]},i.prototype.parent=function(e){if(this._isCompound){var t=this._parent[e];if("\0"!==t)return t}},i.prototype.children=function(e){if(r.isUndefined(e)&&(e="\0"),this._isCompound){var t=this._children[e];if(t)return r.keys(t)}else if("\0"===e)return this.nodes();else if(this.hasNode(e))return[]},i.prototype.predecessors=function(e){var t=this._preds[e];if(t)return r.keys(t)},i.prototype.successors=function(e){var t=this._sucs[e];if(t)return r.keys(t)},i.prototype.neighbors=function(e){var t=this.predecessors(e);if(t)return r.union(t,this.successors(e))},i.prototype.isLeaf=function(e){var t;return 0===(this.isDirected()?this.successors(e):this.neighbors(e)).length},i.prototype.filterNodes=function(e){var t=new this.constructor({directed:this._isDirected,multigraph:this._isMultigraph,compound:this._isCompound});t.setGraph(this.graph());var n=this;r.each(this._nodes,function(n,r){e(r)&&t.setNode(r,n)}),r.each(this._edgeObjs,function(e){t.hasNode(e.v)&&t.hasNode(e.w)&&t.setEdge(e,n.edge(e))});var i={};return this._isCompound&&r.each(t.nodes(),function(e){t.setParent(e,function e(r){var o=n.parent(r);return void 0===o||t.hasNode(o)?(i[r]=o,o):o in i?i[o]:e(o)}(e))}),t},i.prototype.setDefaultEdgeLabel=function(e){return r.isFunction(e)||(e=r.constant(e)),this._defaultEdgeLabelFn=e,this},i.prototype.edgeCount=function(){return this._edgeCount},i.prototype.edges=function(){return r.values(this._edgeObjs)},i.prototype.setPath=function(e,t){var n=this,i=arguments;return r.reduce(e,function(e,r){return i.length>1?n.setEdge(e,r,t):n.setEdge(e,r),r}),this},i.prototype.setEdge=function(){var e,t,n,i,s=!1,l=arguments[0];"object"==typeof l&&null!==l&&"v"in l?(e=l.v,t=l.w,n=l.name,2==arguments.length&&(i=arguments[1],s=!0)):(e=l,t=arguments[1],n=arguments[3],arguments.length>2&&(i=arguments[2],s=!0)),e=""+e,t=""+t,r.isUndefined(n)||(n=""+n);var u=a(this._isDirected,e,t,n);if(r.has(this._edgeLabels,u))return s&&(this._edgeLabels[u]=i),this;if(!r.isUndefined(n)&&!this._isMultigraph)throw Error("Cannot set a named edge when isMultigraph = false");this.setNode(e),this.setNode(t),this._edgeLabels[u]=s?i:this._defaultEdgeLabelFn(e,t,n);var c=function(e,t,n,r){var i=""+t,o=""+n;if(!e&&i>o){var s=i;i=o,o=s}var a={v:i,w:o};return r&&(a.name=r),a}(this._isDirected,e,t,n);return e=c.v,t=c.w,Object.freeze(c),this._edgeObjs[u]=c,o(this._preds[t],e),o(this._sucs[e],t),this._in[t][u]=c,this._out[e][u]=c,this._edgeCount++,this},i.prototype.edge=function(e,t,n){var r=1==arguments.length?l(this._isDirected,arguments[0]):a(this._isDirected,e,t,n);return this._edgeLabels[r]},i.prototype.hasEdge=function(e,t,n){var i=1==arguments.length?l(this._isDirected,arguments[0]):a(this._isDirected,e,t,n);return r.has(this._edgeLabels,i)},i.prototype.removeEdge=function(e,t,n){var r=1==arguments.length?l(this._isDirected,arguments[0]):a(this._isDirected,e,t,n),i=this._edgeObjs[r];return i&&(e=i.v,t=i.w,delete this._edgeLabels[r],delete this._edgeObjs[r],s(this._preds[t],e),s(this._sucs[e],t),delete this._in[t][r],delete this._out[e][r],this._edgeCount--),this},i.prototype.inEdges=function(e,t){var n=this._in[e];if(n){var i=r.values(n);return t?r.filter(i,function(e){return e.v===t}):i}},i.prototype.outEdges=function(e,t){var n=this._out[e];if(n){var i=r.values(n);return t?r.filter(i,function(e){return e.w===t}):i}},i.prototype.nodeEdges=function(e,t){var n=this.inEdges(e,t);if(n)return n.concat(this.outEdges(e,t))}},78302:(e,t,n)=>{var r=n(67460),i=n(96294),o=n(33044),s=Object.prototype.hasOwnProperty;e.exports=function(e){if(!r(e))return o(e);var t=i(e),n=[];for(var a in e)"constructor"==a&&(t||!s.call(e,a))||n.push(a);return n}},78558:(e,t,n)=>{var r=n(91569),i=n(73726),o=n(38649),s=n(43720);e.exports=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)r(t,o(e)),e=i(e);return t}:s},78701:(e,t,n)=>{var r=n(62795),i=n(53516),o=n(18028),s=n(7039),a=n(39608);e.exports=function(e,t,n){var l=a(e)?r:s,u=arguments.length<3;return l(e,o(t,4),n,u,i)}},78966:(e,t,n)=>{var r=n(86110);e.exports=function(e){return r(e,5)}},79243:e=>{e.exports=function(e,t,n){for(var r=-1,i=e.length,o=t.length,s={};++r<i;){var a=r<o?t[r]:void 0;n(s,e[r],a)}return s}},79713:(e,t,n)=>{var r=n(25016);e.exports=function(e,t,n){var i,o={};r.forEach(n,function(n){for(var r,s,a=e.parent(n);a;){if((r=e.parent(a))?(s=o[r],o[r]=a):(s=i,i=a),s&&s!==a)return void t.setEdge(s,a);a=r}})}},80025:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(12115);let i=r.forwardRef(function(e,t){let{title:n,titleId:i,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),n?r.createElement("title",{id:i},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 3.75v4.5m0-4.5h4.5m-4.5 0L9 9M3.75 20.25v-4.5m0 4.5h4.5m-4.5 0L9 15M20.25 3.75h-4.5m4.5 0v4.5m0-4.5L15 9m5.25 11.25h-4.5m4.5 0v-4.5m0 4.5L15 15"}))})},80845:(e,t,n)=>{"use strict";n.d(t,{t:()=>r});let r=(0,n(12115).createContext)(null)},81932:e=>{var t=Object.prototype.hasOwnProperty;e.exports=function(e){var n=e.length,r=new e.constructor(n);return n&&"string"==typeof e[0]&&t.call(e,"index")&&(r.index=e.index,r.input=e.input),r}},82885:(e,t,n)=>{"use strict";n.d(t,{M:()=>i});var r=n(12115);function i(e){let t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}},83172:(e,t,n)=>{var r=n(76957);e.exports=function(e){var t=new e.constructor(e.byteLength);return new r(t).set(new r(e)),t}},83314:(e,t,n)=>{e.exports={components:n(13738),dijkstra:n(14856),dijkstraAll:n(18590),findCycles:n(69471),floydWarshall:n(2119),isAcyclic:n(70451),postorder:n(84548),preorder:n(56065),prim:n(19582),tarjan:n(84306),topsort:n(29721)}},84306:(e,t,n)=>{var r=n(54260);e.exports=function(e){var t=0,n=[],i={},o=[];return e.nodes().forEach(function(s){r.has(i,s)||function s(a){var l=i[a]={onStack:!0,lowlink:t,index:t++};if(n.push(a),e.successors(a).forEach(function(e){r.has(i,e)?i[e].onStack&&(l.lowlink=Math.min(l.lowlink,i[e].index)):(s(e),l.lowlink=Math.min(l.lowlink,i[e].lowlink))}),l.lowlink===l.index){var u,c=[];do i[u=n.pop()].onStack=!1,c.push(u);while(a!==u);o.push(c)}}(s)}),o}},84548:(e,t,n)=>{var r=n(58965);e.exports=function(e,t){return r(e,t,"post")}},84747:(e,t,n)=>{var r=n(72288),i=n(53516),o=n(43803),s=n(39608);e.exports=function(e,t){return(s(e)?r:i)(e,o(t))}},85744:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return r.workAsyncStorageInstance}});let r=n(17828)},86030:e=>{e.exports=function(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t}},86110:(e,t,n)=>{var r=n(67472),i=n(72288),o=n(11850),s=n(20772),a=n(6543),l=n(24035),u=n(86030),c=n(59238),h=n(63957),d=n(20963),f=n(47532),p=n(94380),m=n(81932),g=n(98862),v=n(39208),y=n(39608),x=n(33497),b=n(59507),w=n(67460),k=n(51505),E=n(35095),S=n(54648),T="[object Arguments]",P="[object Function]",C="[object Object]",A={};A[T]=A["[object Array]"]=A["[object ArrayBuffer]"]=A["[object DataView]"]=A["[object Boolean]"]=A["[object Date]"]=A["[object Float32Array]"]=A["[object Float64Array]"]=A["[object Int8Array]"]=A["[object Int16Array]"]=A["[object Int32Array]"]=A["[object Map]"]=A["[object Number]"]=A[C]=A["[object RegExp]"]=A["[object Set]"]=A["[object String]"]=A["[object Symbol]"]=A["[object Uint8Array]"]=A["[object Uint8ClampedArray]"]=A["[object Uint16Array]"]=A["[object Uint32Array]"]=!0,A["[object Error]"]=A[P]=A["[object WeakMap]"]=!1,e.exports=function e(t,n,M,I,L,D){var j,_=1&n,R=2&n,O=4&n;if(M&&(j=L?M(t,I,L,D):M(t)),void 0!==j)return j;if(!w(t))return t;var N=y(t);if(N){if(j=m(t),!_)return u(t,j)}else{var V=p(t),F=V==P||"[object GeneratorFunction]"==V;if(x(t))return l(t,_);if(V==C||V==T||F&&!L){if(j=R||F?{}:v(t),!_)return R?h(t,a(j,t)):c(t,s(j,t))}else{if(!A[V])return L?t:{};j=g(t,V,_)}}D||(D=new r);var B=D.get(t);if(B)return B;D.set(t,j),k(t)?t.forEach(function(r){j.add(e(r,n,M,r,t,D))}):b(t)&&t.forEach(function(r,i){j.set(i,e(r,n,M,i,t,D))});var z=O?R?f:d:R?S:E,U=N?void 0:z(t);return i(U||t,function(r,i){U&&(r=t[i=r]),o(j,i,e(r,n,M,i,t,D))}),j}},86319:(e,t,n)=>{"use strict";var r=n(25016),i=n(98694);e.exports={run:function(e){var t,n,o,s,a,l="greedy"===e.graph().acyclicer?i(e,(t=e,function(e){return t.edge(e).weight})):(n=e,o=[],s={},a={},r.forEach(n.nodes(),function e(t){r.has(a,t)||(a[t]=!0,s[t]=!0,r.forEach(n.outEdges(t),function(t){r.has(s,t.w)?o.push(t):e(t.w)}),delete s[t])}),o);r.forEach(l,function(t){var n=e.edge(t);e.removeEdge(t),n.forwardName=t.name,n.reversed=!0,e.setEdge(t.w,t.v,n,r.uniqueId("rev"))})},undo:function(e){r.forEach(e.edges(),function(t){var n=e.edge(t);if(n.reversed){e.removeEdge(t);var r=n.forwardName;delete n.reversed,delete n.forwardName,e.setEdge(t.w,t.v,n,r)}})}}},87869:(e,t,n)=>{"use strict";let r;function i(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function o(e){let t=[{},{}];return e?.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function s(e,t,n,r){if("function"==typeof t){let[i,s]=o(r);t=t(void 0!==n?n:e.custom,i,s)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,s]=o(r);t=t(void 0!==n?n:e.custom,i,s)}return t}function a(e,t,n){let r=e.getProps();return s(r,t,void 0!==n?n:r.custom,e)}function l(e,t){return e?.[t]??e?.default??e}n.d(t,{P:()=>oS});let u=e=>e,c={},h=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],d={value:null,addProjectionMetrics:null};function f(e,t){let n=!1,r=!0,i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,s=h.reduce((e,n)=>(e[n]=function(e,t){let n=new Set,r=new Set,i=!1,o=!1,s=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){s.has(t)&&(c.schedule(t),e()),l++,t(a)}let c={schedule:(e,t=!1,o=!1)=>{let a=o&&i?n:r;return t&&s.add(e),a.has(e)||a.add(e),e},cancel:e=>{r.delete(e),s.delete(e)},process:e=>{if(a=e,i){o=!0;return}i=!0,[n,r]=[r,n],n.forEach(u),t&&d.value&&d.value.frameloop[t].push(l),l=0,n.clear(),i=!1,o&&(o=!1,c.process(e))}};return c}(o,t?n:void 0),e),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:f,update:p,preRender:m,render:g,postRender:v}=s,y=()=>{let o=c.useManualTiming?i.timestamp:performance.now();n=!1,c.useManualTiming||(i.delta=r?1e3/60:Math.max(Math.min(o-i.timestamp,40),1)),i.timestamp=o,i.isProcessing=!0,a.process(i),l.process(i),u.process(i),f.process(i),p.process(i),m.process(i),g.process(i),v.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(y))},x=()=>{n=!0,r=!0,i.isProcessing||e(y)};return{schedule:h.reduce((e,t)=>{let r=s[t];return e[t]=(e,t=!1,i=!1)=>(n||x(),r.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<h.length;t++)s[h[t]].cancel(e)},state:i,steps:s}}let{schedule:p,cancel:m,state:g,steps:v}=f("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),y=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],x=new Set(y),b=new Set(["width","height","top","left","right","bottom",...y]);function w(e,t){-1===e.indexOf(t)&&e.push(t)}function k(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}class E{constructor(){this.subscriptions=[]}add(e){return w(this.subscriptions,e),()=>k(this.subscriptions,e)}notify(e,t,n){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function S(){r=void 0}let T={now:()=>(void 0===r&&T.set(g.isProcessing||c.useManualTiming?g.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(S)}},P=e=>!isNaN(parseFloat(e)),C={current:void 0};class A{constructor(e,t={}){this.version="__VERSION__",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let n=T.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=T.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=P(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new E);let n=this.events[e].add(t);return"change"===e?()=>{n(),p.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return C.current&&C.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=T.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),n?1e3/n*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function M(e,t){return new A(e,t)}let I=e=>Array.isArray(e),L=e=>!!(e&&e.getVelocity);function D(e,t){let n=e.getValue("willChange");if(L(n)&&n.add)return n.add(t);if(!n&&c.WillChange){let n=new c.WillChange("auto");e.addValue("willChange",n),n.add(t)}}let j=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),_="data-"+j("framerAppearId"),R=(e,t)=>n=>t(e(n)),O=(...e)=>e.reduce(R),N=(e,t,n)=>n>t?t:n<e?e:n,V=e=>1e3*e,F=e=>e/1e3,B={layout:0,mainThread:0,waapi:0},z=()=>{},U=()=>{},H=e=>t=>"string"==typeof t&&t.startsWith(e),W=H("--"),q=H("var(--"),$=e=>!!q(e)&&Y.test(e.split("/*")[0].trim()),Y=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,G={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},X={...G,transform:e=>N(0,1,e)},K={...G,default:1},Z=e=>Math.round(1e5*e)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ee=(e,t)=>n=>!!("string"==typeof n&&J.test(n)&&n.startsWith(e)||t&&null!=n&&Object.prototype.hasOwnProperty.call(n,t)),et=(e,t,n)=>r=>{if("string"!=typeof r)return r;let[i,o,s,a]=r.match(Q);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:void 0!==a?parseFloat(a):1}},en=e=>N(0,255,e),er={...G,transform:e=>Math.round(en(e))},ei={test:ee("rgb","red"),parse:et("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+er.transform(e)+", "+er.transform(t)+", "+er.transform(n)+", "+Z(X.transform(r))+")"},eo={test:ee("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:ei.transform},es=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),ea=es("deg"),el=es("%"),eu=es("px"),ec=es("vh"),eh=es("vw"),ed={...el,parse:e=>el.parse(e)/100,transform:e=>el.transform(100*e)},ef={test:ee("hsl","hue"),parse:et("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+el.transform(Z(t))+", "+el.transform(Z(n))+", "+Z(X.transform(r))+")"},ep={test:e=>ei.test(e)||eo.test(e)||ef.test(e),parse:e=>ei.test(e)?ei.parse(e):ef.test(e)?ef.parse(e):eo.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?ei.transform(e):ef.transform(e)},em=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eg="number",ev="color",ey=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ex(e){let t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[],o=0,s=t.replace(ey,e=>(ep.test(e)?(r.color.push(o),i.push(ev),n.push(ep.parse(e))):e.startsWith("var(")?(r.var.push(o),i.push("var"),n.push(e)):(r.number.push(o),i.push(eg),n.push(parseFloat(e))),++o,"${}")).split("${}");return{values:n,split:s,indexes:r,types:i}}function eb(e){return ex(e).values}function ew(e){let{split:t,types:n}=ex(e),r=t.length;return e=>{let i="";for(let o=0;o<r;o++)if(i+=t[o],void 0!==e[o]){let t=n[o];t===eg?i+=Z(e[o]):t===ev?i+=ep.transform(e[o]):i+=e[o]}return i}}let ek=e=>"number"==typeof e?0:e,eE={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(Q)?.length||0)+(e.match(em)?.length||0)>0},parse:eb,createTransformer:ew,getAnimatableNone:function(e){let t=eb(e);return ew(e)(t.map(ek))}};function eS(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function eT(e,t){return n=>n>0?t:e}let eP=(e,t,n)=>e+(t-e)*n,eC=(e,t,n)=>{let r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},eA=[eo,ei,ef],eM=e=>eA.find(t=>t.test(e));function eI(e){let t=eM(e);if(z(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let n=t.parse(e);return t===ef&&(n=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let i=0,o=0,s=0;if(t/=100){let r=n<.5?n*(1+t):n+t-n*t,a=2*n-r;i=eS(a,r,e+1/3),o=eS(a,r,e),s=eS(a,r,e-1/3)}else i=o=s=n;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*s),alpha:r}}(n)),n}let eL=(e,t)=>{let n=eI(e),r=eI(t);if(!n||!r)return eT(e,t);let i={...n};return e=>(i.red=eC(n.red,r.red,e),i.green=eC(n.green,r.green,e),i.blue=eC(n.blue,r.blue,e),i.alpha=eP(n.alpha,r.alpha,e),ei.transform(i))},eD=new Set(["none","hidden"]);function ej(e,t){return n=>eP(e,t,n)}function e_(e){return"number"==typeof e?ej:"string"==typeof e?$(e)?eT:ep.test(e)?eL:eN:Array.isArray(e)?eR:"object"==typeof e?ep.test(e)?eL:eO:eT}function eR(e,t){let n=[...e],r=n.length,i=e.map((e,n)=>e_(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function eO(e,t){let n={...e,...t},r={};for(let i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=e_(e[i])(e[i],t[i]));return e=>{for(let t in r)n[t]=r[t](e);return n}}let eN=(e,t)=>{let n=eE.createTransformer(t),r=ex(e),i=ex(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?eD.has(e)&&!i.values.length||eD.has(t)&&!r.values.length?function(e,t){return eD.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):O(eR(function(e,t){let n=[],r={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let o=t.types[i],s=e.indexes[o][r[o]],a=e.values[s]??0;n[i]=a,r[o]++}return n}(r,i),i.values),n):(z(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eT(e,t))};function eV(e,t,n){return"number"==typeof e&&"number"==typeof t&&"number"==typeof n?eP(e,t,n):e_(e)(e,t)}let eF=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>p.update(t,e),stop:()=>m(t),now:()=>g.isProcessing?g.timestamp:T.now()}},eB=(e,t,n=10)=>{let r="",i=Math.max(Math.round(t/n),2);for(let t=0;t<i;t++)r+=e(t/(i-1))+", ";return`linear(${r.substring(0,r.length-2)})`};function ez(e){let t=0,n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}function eU(e,t,n){var r,i;let o=Math.max(t-5,0);return r=n-e(o),(i=t-o)?1e3/i*r:0}let eH={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eW(e,t){return e*Math.sqrt(1-t*t)}let eq=["duration","bounce"],e$=["stiffness","damping","mass"];function eY(e,t){return t.some(t=>void 0!==e[t])}function eG(e=eH.visualDuration,t=eH.bounce){let n,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:o}=r,s=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:s},{stiffness:u,damping:c,mass:h,duration:d,velocity:f,isResolvedFromDuration:p}=function(e){let t={velocity:eH.velocity,stiffness:eH.stiffness,damping:eH.damping,mass:eH.mass,isResolvedFromDuration:!1,...e};if(!eY(e,e$)&&eY(e,eq))if(e.visualDuration){let n=2*Math.PI/(1.2*e.visualDuration),r=n*n,i=2*N(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:eH.mass,stiffness:r,damping:i}}else{let n=function({duration:e=eH.duration,bounce:t=eH.bounce,velocity:n=eH.velocity,mass:r=eH.mass}){let i,o;z(e<=V(eH.maxDuration),"Spring duration must be 10 seconds or less");let s=1-t;s=N(eH.minDamping,eH.maxDamping,s),e=N(eH.minDuration,eH.maxDuration,F(e)),s<1?(i=t=>{let r=t*s,i=r*e;return .001-(r-n)/eW(t,s)*Math.exp(-i)},o=t=>{let r=t*s*e,o=Math.pow(s,2)*Math.pow(t,2)*e,a=Math.exp(-r),l=eW(Math.pow(t,2),s);return(r*n+n-o)*a*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-n)*e+1),o=t=>e*e*(n-t)*Math.exp(-t*e));let a=function(e,t,n){let r=n;for(let n=1;n<12;n++)r-=e(r)/t(r);return r}(i,o,5/e);if(e=V(e),isNaN(a))return{stiffness:eH.stiffness,damping:eH.damping,duration:e};{let t=Math.pow(a,2)*r;return{stiffness:t,damping:2*s*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...n,mass:eH.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-F(r.velocity||0)}),m=f||0,g=c/(2*Math.sqrt(u*h)),v=a-s,y=F(Math.sqrt(u/h)),x=5>Math.abs(v);if(i||(i=x?eH.restSpeed.granular:eH.restSpeed.default),o||(o=x?eH.restDelta.granular:eH.restDelta.default),g<1){let e=eW(y,g);n=t=>a-Math.exp(-g*y*t)*((m+g*y*v)/e*Math.sin(e*t)+v*Math.cos(e*t))}else if(1===g)n=e=>a-Math.exp(-y*e)*(v+(m+y*v)*e);else{let e=y*Math.sqrt(g*g-1);n=t=>{let n=Math.exp(-g*y*t),r=Math.min(e*t,300);return a-n*((m+g*y*v)*Math.sinh(r)+e*v*Math.cosh(r))/e}}let b={calculatedDuration:p&&d||null,next:e=>{let t=n(e);if(p)l.done=e>=d;else{let r=0===e?m:0;g<1&&(r=0===e?V(m):eU(n,e,t));let s=Math.abs(a-t)<=o;l.done=Math.abs(r)<=i&&s}return l.value=l.done?a:t,l},toString:()=>{let e=Math.min(ez(b),2e4),t=eB(t=>b.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return b}function eX({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:c}){let h,d,f=e[0],p={done:!1,value:f},m=e=>void 0!==a&&e<a||void 0!==l&&e>l,g=e=>void 0===a?l:void 0===l||Math.abs(a-e)<Math.abs(l-e)?a:l,v=n*t,y=f+v,x=void 0===s?y:s(y);x!==y&&(v=x-f);let b=e=>-v*Math.exp(-e/r),w=e=>x+b(e),k=e=>{let t=b(e),n=w(e);p.done=Math.abs(t)<=u,p.value=p.done?x:n},E=e=>{m(p.value)&&(h=e,d=eG({keyframes:[p.value,g(p.value)],velocity:eU(w,e,p.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return E(0),{calculatedDuration:null,next:e=>{let t=!1;return(d||void 0!==h||(t=!0,k(e),E(e)),void 0!==h&&e>=h)?d.next(e-h):(t||k(e),p)}}}eG.applyToOptions=e=>{let t=function(e,t=100,n){let r=n({...e,keyframes:[0,t]}),i=Math.min(ez(r),2e4);return{type:"keyframes",ease:e=>r.next(i*e).value/t,duration:F(i)}}(e,100,eG);return e.ease=t.ease,e.duration=V(t.duration),e.type="keyframes",e};let eK=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function eZ(e,t,n,r){if(e===t&&n===r)return u;let i=t=>(function(e,t,n,r,i){let o,s,a=0;do(o=eK(s=t+(n-t)/2,r,i)-e)>0?n=s:t=s;while(Math.abs(o)>1e-7&&++a<12);return s})(t,0,1,e,n);return e=>0===e||1===e?e:eK(i(e),t,r)}let eQ=eZ(.42,0,1,1),eJ=eZ(0,0,.58,1),e0=eZ(.42,0,.58,1),e1=e=>Array.isArray(e)&&"number"!=typeof e[0],e2=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e5=e=>t=>1-e(1-t),e4=eZ(.33,1.53,.69,.99),e3=e5(e4),e6=e2(e3),e8=e=>(e*=2)<1?.5*e3(e):.5*(2-Math.pow(2,-10*(e-1))),e9=e=>1-Math.sin(Math.acos(e)),e7=e5(e9),te=e2(e9),tt=e=>Array.isArray(e)&&"number"==typeof e[0],tn={linear:u,easeIn:eQ,easeInOut:e0,easeOut:eJ,circIn:e9,circInOut:te,circOut:e7,backIn:e3,backInOut:e6,backOut:e4,anticipate:e8},tr=e=>"string"==typeof e,ti=e=>{if(tt(e)){U(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,n,r,i]=e;return eZ(t,n,r,i)}return tr(e)?(U(void 0!==tn[e],`Invalid easing type '${e}'`),tn[e]):e},to=(e,t,n)=>{let r=t-e;return 0===r?1:(n-e)/r};function ts({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){var i;let o=e1(r)?r.map(ti):ti(r),s={done:!1,value:t[0]},a=function(e,t,{clamp:n=!0,ease:r,mixer:i}={}){let o=e.length;if(U(o===t.length,"Both input and output ranges must be the same length"),1===o)return()=>t[0];if(2===o&&t[0]===t[1])return()=>t[1];let s=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=function(e,t,n){let r=[],i=n||c.mix||eV,o=e.length-1;for(let n=0;n<o;n++){let o=i(e[n],e[n+1]);t&&(o=O(Array.isArray(t)?t[n]||u:t,o)),r.push(o)}return r}(t,r,i),l=a.length,h=n=>{if(s&&n<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(n<e[r+1]);r++);let i=to(e[r],e[r+1],n);return a[r](i)};return n?t=>h(N(e[0],e[o-1],t)):h}((i=n&&n.length===t.length?n:function(e){let t=[0];return!function(e,t){let n=e[e.length-1];for(let r=1;r<=t;r++){let i=to(0,t,r);e.push(eP(n,1,i))}}(t,e.length-1),t}(t),i.map(t=>t*e)),t,{ease:Array.isArray(o)?o:t.map(()=>o||e0).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(s.value=a(t),s.done=t>=e,s)}}let ta=e=>null!==e;function tl(e,{repeat:t,repeatType:n="loop"},r,i=1){let o=e.filter(ta),s=i<0||t&&"loop"!==n&&t%2==1?0:o.length-1;return s&&void 0!==r?r:o[s]}let tu={decay:eX,inertia:eX,tween:ts,keyframes:ts,spring:eG};function tc(e){"string"==typeof e.type&&(e.type=tu[e.type])}class th{constructor(){this.count=0,this.updateFinished()}get finished(){return this._finished}updateFinished(){this.count++,this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let td=e=>e/100;class tf extends th{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(e=!0)=>{if(e){let{motionValue:e}=this.options;e&&e.updatedAt!==T.now()&&this.tick(T.now())}if(this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()},B.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;tc(e);let{type:t=ts,repeat:n=0,repeatDelay:r=0,repeatType:i,velocity:o=0}=e,{keyframes:s}=e,a=t||ts;a!==ts&&"number"!=typeof s[0]&&(this.mixKeyframes=O(td,eV(s[0],s[1])),s=[0,100]);let l=a({...e,keyframes:s});"mirror"===i&&(this.mirroredGenerator=a({...e,keyframes:[...s].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=ez(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(n+1)-r,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:n,totalDuration:r,mixKeyframes:i,mirroredGenerator:o,resolvedDuration:s,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:h,repeatDelay:d,type:f,onUpdate:p,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let y=this.currentTime,x=n;if(c){let e=Math.min(this.currentTime,r)/s,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,(t=Math.min(t,c+1))%2&&("reverse"===h?(n=1-n,d&&(n-=d/s)):"mirror"===h&&(x=o)),y=N(0,1,n)*s}let b=v?{done:!1,value:u[0]}:x.next(y);i&&(b.value=i(b.value));let{done:w}=b;v||null===a||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let k=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return k&&f!==eX&&(b.value=tl(u,this.options,m,this.speed)),p&&p(b.value),k&&this.finish(),b}then(e,t){return this.finished.then(e,t)}get duration(){return F(this.calculatedDuration)}get time(){return F(this.currentTime)}set time(e){e=V(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(T.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=F(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=eF,onPlay:t,startTime:n}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),t&&t();let r=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=r):null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime||(this.startTime=n??r),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(T.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:e}=this.options;e&&e()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown()}teardown(){this.notifyFinished(),this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,B.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tp=e=>180*e/Math.PI,tm=e=>tv(tp(Math.atan2(e[1],e[0]))),tg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tm,rotateZ:tm,skewX:e=>tp(Math.atan(e[1])),skewY:e=>tp(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},tv=e=>((e%=360)<0&&(e+=360),e),ty=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tx=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tb={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ty,scaleY:tx,scale:e=>(ty(e)+tx(e))/2,rotateX:e=>tv(tp(Math.atan2(e[6],e[5]))),rotateY:e=>tv(tp(Math.atan2(-e[2],e[0]))),rotateZ:tm,rotate:tm,skewX:e=>tp(Math.atan(e[4])),skewY:e=>tp(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tw(e){return+!!e.includes("scale")}function tk(e,t){let n,r;if(!e||"none"===e)return tw(t);let i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)n=tb,r=i;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=tg,r=t}if(!r)return tw(t);let o=n[t],s=r[1].split(",").map(tS);return"function"==typeof o?o(s):s[o]}let tE=(e,t)=>{let{transform:n="none"}=getComputedStyle(e);return tk(n,t)};function tS(e){return parseFloat(e.trim())}let tT=e=>e===G||e===eu,tP=new Set(["x","y","z"]),tC=y.filter(e=>!tP.has(e)),tA={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tk(t,"x"),y:(e,{transform:t})=>tk(t,"y")};tA.translateX=tA.x,tA.translateY=tA.y;let tM=new Set,tI=!1,tL=!1,tD=!1;function tj(){if(tL){let e=Array.from(tM).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{let t=function(e){let t=[];return tC.forEach(n=>{let r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(+!!n.startsWith("scale")))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=n.get(e);t&&t.forEach(([t,n])=>{e.getValue(t)?.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tL=!1,tI=!1,tM.forEach(e=>e.complete(tD)),tM.clear()}function t_(){tM.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tL=!0)})}class tR{constructor(e,t,n,r,i,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(tM.add(this),tI||(tI=!0,p.read(t_),p.resolveKeyframes(tj))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;if(null===e[0]){let i=r?.get(),o=e[e.length-1];if(void 0!==i)e[0]=i;else if(n&&t){let r=n.readValue(t,o);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=o),r&&void 0===i&&r.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tM.delete(this)}cancel(){"scheduled"===this.state&&(tM.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tO=e=>e.startsWith("--");function tN(e){let t;return()=>(void 0===t&&(t=e()),t)}let tV=tN(()=>void 0!==window.ScrollTimeline),tF={},tB=function(e,t){let n=tN(e);return()=>tF[t]??n()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tz=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,tU={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tz([0,.65,.55,1]),circOut:tz([.55,0,1,.45]),backIn:tz([.31,.01,.66,-.59]),backOut:tz([.33,1.53,.69,.99])};function tH(e){return"function"==typeof e&&"applyToOptions"in e}class tW extends th{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:n,keyframes:r,pseudoElement:i,allowFlatten:o=!1,finalKeyframe:s,onComplete:a}=e;this.isPseudoElement=!!i,this.allowFlatten=o,this.options=e,U("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return tH(e)&&tB()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,n,{delay:r=0,duration:i=300,repeat:o=0,repeatType:s="loop",ease:a="easeOut",times:l}={},u){let c={[t]:n};l&&(c.offset=l);let h=function e(t,n){if(t)return"function"==typeof t?tB()?eB(t,n):"ease-out":tt(t)?tz(t):Array.isArray(t)?t.map(t=>e(t,n)||tU.easeOut):tU[t]}(a,i);Array.isArray(h)&&(c.easing=h),d.value&&B.waapi++;let f={delay:r,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:"reverse"===s?"alternate":"normal"};u&&(f.pseudoElement=u);let p=e.animate(c,f);return d.value&&p.finished.finally(()=>{B.waapi--}),p}(t,n,r,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=tl(r,this.options,s,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,n){tO(t)?e.style.setProperty(t,n):e.style[t]=n}(t,n,e),this.animation.cancel()}a?.(),this.notifyFinished()},this.animation.oncancel=()=>this.notifyFinished()}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return F(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return F(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=V(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&tV())?(this.animation.timeline=e,u):t(this)}}let tq={anticipate:e8,backInOut:e6,circInOut:te};class t$ extends tW{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in tq&&(e.ease=tq[e.ease])}(e),tc(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:n,onComplete:r,element:i,...o}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let s=new tf({...o,autoplay:!1}),a=V(this.finishedTime??this.time);t.setWithVelocity(s.sample(a-10).value,s.sample(a).value,10),s.stop()}}let tY=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eE.test(e)||"0"===e)&&!e.startsWith("url(")),tG=new Set(["opacity","clipPath","filter","transform"]),tX=tN(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tK extends th{constructor({autoplay:e=!0,delay:t=0,type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:o="loop",keyframes:s,name:a,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=T.now();let h={autoplay:e,delay:t,type:n,repeat:r,repeatDelay:i,repeatType:o,name:a,motionValue:l,element:u,...c},d=u?.KeyframeResolver||tR;this.keyframeResolver=new d(s,(e,t,n)=>this.onKeyframesResolved(e,t,h,!n),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,n,r){this.keyframeResolver=void 0;let{name:i,type:o,velocity:s,delay:a,isHandoff:l,onUpdate:h}=n;this.resolvedAt=T.now(),!function(e,t,n,r){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let o=e[e.length-1],s=tY(i,t),a=tY(o,t);return z(s===a,`You are trying to animate ${t} from "${i}" to "${o}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${o} via the \`style\` property.`),!!s&&!!a&&(function(e){let t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||tH(n))&&r)}(e,i,o,s)&&((c.instantAnimations||!a)&&h?.(tl(e,n,t)),e[0]=e[e.length-1],n.duration=0,n.repeat=0);let d={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...n,keyframes:e},f=!l&&function(e){let{motionValue:t,name:n,repeatDelay:r,repeatType:i,damping:o,type:s}=e;if(!t||!t.owner||!(t.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=t.owner.getProps();return tX()&&n&&tG.has(n)&&("transform"!==n||!l)&&!a&&!r&&"mirror"!==i&&0!==o&&"inertia"!==s}(d)?new t$({...d,element:d.motionValue.owner.current}):new tf(d);f.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=f.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=f}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tD=!0,t_(),tj(),tD=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let tZ=e=>null!==e,tQ={type:"spring",stiffness:500,damping:25,restSpeed:10},tJ=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),t0={type:"keyframes",duration:.8},t1={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},t2=(e,{keyframes:t})=>t.length>2?t0:x.has(e)?e.startsWith("scale")?tJ(t[1]):tQ:t1,t5=(e,t,n,r={},i,o)=>s=>{let a=l(r,e)||{},u=a.delay||r.delay||0,{elapsed:h=0}=r;h-=V(u);let d={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-h,onUpdate:e=>{t.set(e),a.onUpdate&&a.onUpdate(e)},onComplete:()=>{s(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:o?void 0:i};!function({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(a)&&Object.assign(d,t2(e,d)),d.duration&&(d.duration=V(d.duration)),d.repeatDelay&&(d.repeatDelay=V(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let f=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0===d.delay&&(f=!0)),(c.instantAnimations||c.skipAnimations)&&(f=!0,d.duration=0,d.delay=0),d.allowFlatten=!a.type&&!a.ease,f&&!o&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:n="loop"},r){let i=e.filter(tZ),o=t&&"loop"!==n&&t%2==1?0:i.length-1;return i[o]}(d.keyframes,a);if(void 0!==e)return void p.update(()=>{d.onUpdate(e),d.onComplete()})}return a.isSync?new tf(d):new tK(d)};function t4(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:s,...u}=t;r&&(o=r);let c=[],h=i&&e.animationState&&e.animationState.getState()[i];for(let t in u){let r=e.getValue(t,e.latestValues[t]??null),i=u[t];if(void 0===i||h&&function({protectedKeys:e,needsAnimating:t},n){let r=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,r}(h,t))continue;let s={delay:n,...l(o||{},t)},a=r.get();if(void 0!==a&&!r.isAnimating&&!Array.isArray(i)&&i===a&&!s.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let n=e.props[_];if(n){let e=window.MotionHandoffAnimation(n,t,p);null!==e&&(s.startTime=e,d=!0)}}D(e,t),r.start(t5(t,r,i,e.shouldReduceMotion&&b.has(t)?{type:!1}:s,e,d));let f=r.animation;f&&c.push(f)}return s&&Promise.all(c).then(()=>{p.update(()=>{s&&function(e,t){let{transitionEnd:n={},transition:r={},...i}=a(e,t)||{};for(let t in i={...i,...n}){var o;let n=I(o=i[t])?o[o.length-1]||0:o;e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,M(n))}}(e,s)})}),c}function t3(e,t,n={}){let r=a(e,t,"exit"===n.type?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);let o=r?()=>Promise.all(t4(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:o=0,staggerChildren:s,staggerDirection:a}=i;return function(e,t,n=0,r=0,i=1,o){let s=[],a=(e.variantChildren.size-1)*r,l=1===i?(e=0)=>e*r:(e=0)=>a-e*r;return Array.from(e.variantChildren).sort(t6).forEach((e,r)=>{e.notify("AnimationStart",t),s.push(t3(e,t,{...o,delay:n+l(r)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(s)}(e,t,o+r,s,a,n)}:()=>Promise.resolve(),{when:l}=i;if(!l)return Promise.all([o(),s(n.delay)]);{let[e,t]="beforeChildren"===l?[o,s]:[s,o];return e().then(()=>t())}}function t6(e,t){return e.sortNodePosition(t)}function t8(e,t){if(!Array.isArray(t))return!1;let n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function t9(e){return"string"==typeof e||Array.isArray(e)}let t7=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ne=["initial",...t7],nt=ne.length,nn=[...t7].reverse(),nr=t7.length;function ni(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function no(){return{animate:ni(!0),whileInView:ni(),whileHover:ni(),whileTap:ni(),whileDrag:ni(),whileFocus:ni(),exit:ni()}}class ns{constructor(e){this.isMounted=!1,this.node=e}update(){}}class na extends ns{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:n})=>(function(e,t,n={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>t3(e,t,n)));else if("string"==typeof t)r=t3(e,t,n);else{let i="function"==typeof t?a(e,t,n.custom):t;r=Promise.all(t4(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,n))),n=no(),r=!0,o=t=>(n,r)=>{let i=a(e,r,"exit"===t?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...r}=i;n={...n,...r,...t}}return n};function s(s){let{props:l}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let n=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(n.initial=t.props.initial),n}let n={};for(let e=0;e<nt;e++){let r=ne[e],i=t.props[r];(t9(i)||!1===i)&&(n[r]=i)}return n}(e.parent)||{},c=[],h=new Set,d={},f=1/0;for(let t=0;t<nr;t++){var p,m;let a=nn[t],g=n[a],v=void 0!==l[a]?l[a]:u[a],y=t9(v),x=a===s?g.isActive:null;!1===x&&(f=t);let b=v===u[a]&&v!==l[a]&&y;if(b&&r&&e.manuallyAnimateOnMount&&(b=!1),g.protectedKeys={...d},!g.isActive&&null===x||!v&&!g.prevProp||i(v)||"boolean"==typeof v)continue;let w=(p=g.prevProp,"string"==typeof(m=v)?m!==p:!!Array.isArray(m)&&!t8(m,p)),k=w||a===s&&g.isActive&&!b&&y||t>f&&y,E=!1,S=Array.isArray(v)?v:[v],T=S.reduce(o(a),{});!1===x&&(T={});let{prevResolvedValues:P={}}=g,C={...P,...T},A=t=>{k=!0,h.has(t)&&(E=!0,h.delete(t)),g.needsAnimating[t]=!0;let n=e.getValue(t);n&&(n.liveStyle=!1)};for(let e in C){let t=T[e],n=P[e];if(d.hasOwnProperty(e))continue;let r=!1;(I(t)&&I(n)?t8(t,n):t===n)?void 0!==t&&h.has(e)?A(e):g.protectedKeys[e]=!0:null!=t?A(e):h.add(e)}g.prevProp=v,g.prevResolvedValues=T,g.isActive&&(d={...d,...T}),r&&e.blockInitialAnimation&&(k=!1);let M=!(b&&w)||E;k&&M&&c.push(...S.map(e=>({animation:e,options:{type:a}})))}if(h.size){let t={};if("boolean"!=typeof l.initial){let n=a(e,Array.isArray(l.initial)?l.initial[0]:l.initial);n&&n.transition&&(t.transition=n.transition)}h.forEach(n=>{let r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=r??null}),c.push({animation:t})}let g=!!c.length;return r&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(g=!1),r=!1,g?t(c):Promise.resolve()}return{animateChanges:s,setActive:function(t,r){if(n[t].isActive===r)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,r)),n[t].isActive=r;let i=s(t);for(let e in n)n[e].protectedKeys={};return i},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=no(),r=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();i(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let nl=0;class nu extends ns{constructor(){super(...arguments),this.id=nl++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let nc={x:!1,y:!1};function nh(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}let nd=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function nf(e){return{point:{x:e.pageX,y:e.pageY}}}let np=e=>t=>nd(t)&&e(t,nf(t));function nm(e,t,n,r){return nh(e,t,np(n),r)}function ng({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function nv(e){return e.max-e.min}function ny(e,t,n,r=.5){e.origin=r,e.originPoint=eP(t.min,t.max,e.origin),e.scale=nv(n)/nv(t),e.translate=eP(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function nx(e,t,n,r){ny(e.x,t.x,n.x,r?r.originX:void 0),ny(e.y,t.y,n.y,r?r.originY:void 0)}function nb(e,t,n){e.min=n.min+t.min,e.max=e.min+nv(t)}function nw(e,t,n){e.min=t.min-n.min,e.max=e.min+nv(t)}function nk(e,t,n){nw(e.x,t.x,n.x),nw(e.y,t.y,n.y)}let nE=()=>({translate:0,scale:1,origin:0,originPoint:0}),nS=()=>({x:nE(),y:nE()}),nT=()=>({min:0,max:0}),nP=()=>({x:nT(),y:nT()});function nC(e){return[e("x"),e("y")]}function nA(e){return void 0===e||1===e}function nM({scale:e,scaleX:t,scaleY:n}){return!nA(e)||!nA(t)||!nA(n)}function nI(e){return nM(e)||nL(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function nL(e){var t,n;return(t=e.x)&&"0%"!==t||(n=e.y)&&"0%"!==n}function nD(e,t,n,r,i){return void 0!==i&&(e=r+i*(e-r)),r+n*(e-r)+t}function nj(e,t=0,n=1,r,i){e.min=nD(e.min,t,n,r,i),e.max=nD(e.max,t,n,r,i)}function n_(e,{x:t,y:n}){nj(e.x,t.translate,t.scale,t.originPoint),nj(e.y,n.translate,n.scale,n.originPoint)}function nR(e,t){e.min=e.min+t,e.max=e.max+t}function nO(e,t,n,r,i=.5){let o=eP(e.min,e.max,i);nj(e,t,n,o,r)}function nN(e,t){nO(e.x,t.x,t.scaleX,t.scale,t.originX),nO(e.y,t.y,t.scaleY,t.scale,t.originY)}function nV(e,t){return ng(function(e,t){if(!t)return e;let n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let nF=({current:e})=>e?e.ownerDocument.defaultView:null;function nB(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let nz=(e,t)=>Math.abs(e-t);class nU{constructor(e,t,{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=nq(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){return Math.sqrt(nz(e.x,t.x)**2+nz(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;let{point:r}=e,{timestamp:i}=g;this.history.push({...r,timestamp:i});let{onStart:o,onMove:s}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=nH(t,this.transformPagePoint),p.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=nq("pointercancel"===e.type?this.lastMoveEventInfo:nH(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,o),r&&r(e,o)},!nd(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.contextWindow=r||window;let o=nH(nf(e),this.transformPagePoint),{point:s}=o,{timestamp:a}=g;this.history=[{...s,timestamp:a}];let{onSessionStart:l}=t;l&&l(e,nq(o,this.history)),this.removeListeners=O(nm(this.contextWindow,"pointermove",this.handlePointerMove),nm(this.contextWindow,"pointerup",this.handlePointerUp),nm(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}}function nH(e,t){return t?{point:t(e.point)}:e}function nW(e,t){return{x:e.x-t.x,y:e.y-t.y}}function nq({point:e},t){return{point:e,delta:nW(e,n$(t)),offset:nW(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null,i=n$(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>V(.1)));)n--;if(!r)return{x:0,y:0};let o=F(i.timestamp-r.timestamp);if(0===o)return{x:0,y:0};let s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(t,.1)}}function n$(e){return e[e.length-1]}function nY(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function nG(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function nX(e,t,n){return{min:nK(e,t),max:nK(e,n)}}function nK(e,t){return"number"==typeof e?e:e[t]||0}let nZ=new WeakMap;class nQ{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=nP(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new nU(e,{onSessionStart:e=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(nf(e).point)},onStart:(e,t)=>{let{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(nc[e])return null;else return nc[e]=!0,()=>{nc[e]=!1};return nc.x||nc.y?null:(nc.x=nc.y=!0,()=>{nc.x=nc.y=!1})}(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nC(e=>{let t=this.getAxisMotionValue(e).get()||0;if(el.test(t)){let{projection:n}=this.visualElement;if(n&&n.layout){let r=n.layout.layoutBox[e];r&&(t=nv(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),i&&p.postRender(()=>i(e,t)),D(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:s}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}(s),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,s),this.updateAxis("y",t.point,s),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>nC(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:nF(this.visualElement)})}stop(e,t){let n=this.isDragging;if(this.cancel(),!n)return;let{velocity:r}=t;this.startAnimation(r);let{onDragEnd:i}=this.getProps();i&&p.postRender(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){let{drag:r}=this.getProps();if(!n||!nJ(e,r,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:n},r){return void 0!==t&&e<t?e=r?eP(t,e,r.min):Math.max(e,t):void 0!==n&&e>n&&(e=r?eP(n,e,r.max):Math.min(e,n)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;e&&nB(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(e,{top:t,left:n,bottom:r,right:i}){return{x:nY(e.x,n,i),y:nY(e.y,t,r)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:nX(e,"left","right"),y:nX(e,"top","bottom")}}(t),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&nC(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!nB(t))return!1;let r=t.current;U(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,n){let r=nV(e,n),{scroll:i}=t;return i&&(nR(r.x,i.offset.x),nR(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),s=(e=i.layout.layoutBox,{x:nG(e.x,o.x),y:nG(e.y,o.y)});if(n){let e=n(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(s));this.hasMutatedConstraints=!!e,e&&(s=ng(e))}return s}startAnimation(e){let{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),a=this.constraints||{};return Promise.all(nC(s=>{if(!nJ(s,t,this.currentDirection))return;let l=a&&a[s]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:n?e[s]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(e,t){let n=this.getAxisMotionValue(e);return D(this.visualElement,e),n.start(t5(e,n,0,t,this.visualElement,!1))}stopAnimation(){nC(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){nC(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps();return n[t]||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){nC(t=>{let{drag:n}=this.getProps();if(!nJ(t,n,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){let{min:n,max:o}=r.layout.layoutBox[t];i.set(e[t]-eP(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!nB(t)||!n||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};nC(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let n=t.get();r[e]=function(e,t){let n=.5,r=nv(e),i=nv(t);return i>r?n=to(t.min,t.max-r,e.min):r>i&&(n=to(e.min,e.max-i,t.min)),N(0,1,n)}({min:n,max:n},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),nC(t=>{if(!nJ(t,e,null))return;let n=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];n.set(eP(i,o,r[t]))})}addListeners(){if(!this.visualElement.current)return;nZ.set(this.visualElement,this);let e=nm(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();nB(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),p.read(t);let i=nh(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(nC(t=>{let n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:s=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:o,dragMomentum:s}}}function nJ(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}class n0 extends ns{constructor(e){super(e),this.removeGroupControls=u,this.removeListeners=u,this.controls=new nQ(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let n1=e=>(t,n)=>{e&&p.postRender(()=>e(t,n))};class n2 extends ns{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(e){this.session=new nU(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nF(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:n1(e),onStart:n1(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&p.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=nm(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var n5,n4,n3=n(95155);let{schedule:n6}=f(queueMicrotask,!1);var n8=n(12115),n9=n(32082),n7=n(90869);let re=(0,n8.createContext)({}),rt={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rn(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let rr={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eu.test(e))return e;else e=parseFloat(e);let n=rn(e,t.target.x),r=rn(e,t.target.y);return`${n}% ${r}%`}},ri={};class ro extends n8.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;for(let e in ra)ri[e]=ra[e],W(e)&&(ri[e].isCSSVariable=!0);i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),rt.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,{projection:o}=n;return o&&(o.isPresent=i,r||e.layoutDependency!==t||void 0===t||e.isPresent!==i?o.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?o.promote():o.relegate()||p.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),n6.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function rs(e){let[t,n]=(0,n9.xQ)(),r=(0,n8.useContext)(n7.L);return(0,n3.jsx)(ro,{...e,layoutGroup:r,switchLayoutGroup:(0,n8.useContext)(re),isPresent:t,safeToRemove:n})}let ra={borderRadius:{...rr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:rr,borderTopRightRadius:rr,borderBottomLeftRadius:rr,borderBottomRightRadius:rr,boxShadow:{correct:(e,{treeScale:t,projectionDelta:n})=>{let r=eE.parse(e);if(r.length>5)return e;let i=eE.createTransformer(e),o=+("number"!=typeof r[0]),s=n.x.scale*t.x,a=n.y.scale*t.y;r[0+o]/=s,r[1+o]/=a;let l=eP(s,a,.5);return"number"==typeof r[2+o]&&(r[2+o]/=l),"number"==typeof r[3+o]&&(r[3+o]/=l),i(r)}}},rl=(e,t)=>e.depth-t.depth;class ru{constructor(){this.children=[],this.isDirty=!1}add(e){w(this.children,e),this.isDirty=!0}remove(e){k(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(rl),this.isDirty=!1,this.children.forEach(e)}}function rc(e){return L(e)?e.get():e}let rh=["TopLeft","TopRight","BottomLeft","BottomRight"],rd=rh.length,rf=e=>"string"==typeof e?parseFloat(e):e,rp=e=>"number"==typeof e||eu.test(e);function rm(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let rg=ry(0,.5,e7),rv=ry(.5,.95,u);function ry(e,t,n){return r=>r<e?0:r>t?1:n(to(e,t,r))}function rx(e,t){e.min=t.min,e.max=t.max}function rb(e,t){rx(e.x,t.x),rx(e.y,t.y)}function rw(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rk(e,t,n,r,i){return e-=t,e=r+1/n*(e-r),void 0!==i&&(e=r+1/i*(e-r)),e}function rE(e,t,[n,r,i],o,s){!function(e,t=0,n=1,r=.5,i,o=e,s=e){if(el.test(t)&&(t=parseFloat(t),t=eP(s.min,s.max,t/100)-s.min),"number"!=typeof t)return;let a=eP(o.min,o.max,r);e===o&&(a-=t),e.min=rk(e.min,t,n,a,i),e.max=rk(e.max,t,n,a,i)}(e,t[n],t[r],t[i],t.scale,o,s)}let rS=["x","scaleX","originX"],rT=["y","scaleY","originY"];function rP(e,t,n,r){rE(e.x,t,rS,n?n.x:void 0,r?r.x:void 0),rE(e.y,t,rT,n?n.y:void 0,r?r.y:void 0)}function rC(e){return 0===e.translate&&1===e.scale}function rA(e){return rC(e.x)&&rC(e.y)}function rM(e,t){return e.min===t.min&&e.max===t.max}function rI(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rL(e,t){return rI(e.x,t.x)&&rI(e.y,t.y)}function rD(e){return nv(e.x)/nv(e.y)}function rj(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class r_{constructor(){this.members=[]}add(e){w(this.members,e),e.scheduleRender()}remove(e){if(k(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,n=this.members.findIndex(t=>e===t);if(0===n)return!1;for(let e=n;e>=0;e--){let n=this.members[e];if(!1!==n.isPresent){t=n;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rR={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rO=["","X","Y","Z"],rN={visibility:"hidden"},rV=0;function rF(e,t,n,r){let{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function rB({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},n=t?.()){this.id=rV++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,d.value&&(rR.nodes=rR.calculatedTargetDeltas=rR.calculatedProjections=0),this.nodes.forEach(rH),this.nodes.forEach(rK),this.nodes.forEach(rZ),this.nodes.forEach(rW),d.addProjectionMetrics&&d.addProjectionMetrics(rR)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new ru)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new E),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:n,layout:r,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||n)&&(this.isLayoutDirty=!0),e){let n,r=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){let n=T.now(),r=({timestamp:i})=>{let o=i-n;o>=250&&(m(r),e(o-t))};return p.setup(r,!0),()=>m(r)}(r,250),rt.hasAnimatedSinceResize&&(rt.hasAnimatedSinceResize=!1,this.nodes.forEach(rX))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&i&&(n||r)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||i.getDefaultTransition()||r5,{onLayoutAnimationStart:s,onLayoutAnimationComplete:a}=i.getProps(),u=!this.targetLayout||!rL(this.targetLayout,r),c=!t&&n;if(this.options.layoutRoot||this.resumeFrom||c||t&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,c);let t={...l(o,"layout"),onPlay:s,onComplete:a};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||rX(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(rQ),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:n}=t.options;if(!n)return;let r=n.props[_];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",p,!(e||n))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(r$);return}this.isUpdating||this.nodes.forEach(rY),this.isUpdating=!1,this.nodes.forEach(rG),this.nodes.forEach(rz),this.nodes.forEach(rU),this.clearAllSnapshots();let e=T.now();g.delta=N(0,1e3/60,e-g.timestamp),g.timestamp=e,g.isProcessing=!0,v.update.process(g),v.preRender.process(g),v.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,n6.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rq),this.sharedNodes.forEach(rJ)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,p.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){p.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||nv(this.snapshot.measuredBox.x)||nv(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=nP(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!rA(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,o=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||nI(this.latestValues)||o)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let n=this.measurePageBox(),r=this.removeElementScroll(n);return e&&(r=this.removeTransform(r)),r6((t=r).x),r6(t.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return nP();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(r9))){let{scroll:e}=this.root;e&&(nR(t.x,e.offset.x),nR(t.y,e.offset.y))}return t}removeElementScroll(e){let t=nP();if(rb(t,e),this.scroll?.wasRoot)return t;for(let n=0;n<this.path.length;n++){let r=this.path[n],{scroll:i,options:o}=r;r!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&rb(t,e),nR(t.x,i.offset.x),nR(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let n=nP();rb(n,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&nN(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),nI(r.latestValues)&&nN(n,r.latestValues)}return nI(this.latestValues)&&nN(n,this.latestValues),n}removeTransform(e){let t=nP();rb(t,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];if(!n.instance||!nI(n.latestValues))continue;nM(n.latestValues)&&n.updateSnapshot();let r=nP();rb(r,n.measurePageBox()),rP(t,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,r)}return nI(this.latestValues)&&rP(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let n=!!this.resumingFrom||this!==t;if(!(e||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:i}=this.options;if(this.layout&&(r||i)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nP(),this.relativeTargetOrigin=nP(),nk(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rb(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=nP(),this.targetWithTransforms=nP()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var o,s,a;this.forceRelativeParentToResolveTarget(),o=this.target,s=this.relativeTarget,a=this.relativeParent.target,nb(o.x,s.x,a.x),nb(o.y,s.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rb(this.target,this.layout.layoutBox),n_(this.target,this.targetDelta)):rb(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nP(),this.relativeTargetOrigin=nP(),nk(this.relativeTargetOrigin,this.target,e.target),rb(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}d.value&&rR.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||nM(this.parent.latestValues)||nL(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===g.timestamp&&(n=!1),n)return;let{layout:r,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||i))return;rb(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,s=this.treeScale.y;!function(e,t,n,r=!1){let i,o,s=n.length;if(s){t.x=t.y=1;for(let a=0;a<s;a++){o=(i=n[a]).projectionDelta;let{visualElement:s}=i.options;(!s||!s.props.style||"contents"!==s.props.style.display)&&(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&nN(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,n_(e,o)),r&&nI(i.latestValues)&&nN(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=nP());let{target:a}=e;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rw(this.prevProjectionDelta.x,this.projectionDelta.x),rw(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nx(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===s&&rj(this.projectionDelta.x,this.prevProjectionDelta.x)&&rj(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),d.value&&rR.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=nS(),this.projectionDelta=nS(),this.projectionDeltaWithTransform=nS()}setAnimationOrigin(e,t=!1){let n,r=this.snapshot,i=r?r.latestValues:{},o={...this.latestValues},s=nS();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let a=nP(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,h=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(r2));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(r0(s.x,e.x,r),r0(s.y,e.y,r),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,f,p,m,g;nk(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,m=a,g=r,r1(f.x,p.x,m.x,g),r1(f.y,p.y,m.y,g),n&&(u=this.relativeTarget,d=n,rM(u.x,d.x)&&rM(u.y,d.y))&&(this.isProjectionDirty=!1),n||(n=nP()),rb(n,this.relativeTarget)}l&&(this.animationValues=o,function(e,t,n,r,i,o){i?(e.opacity=eP(0,n.opacity??1,rg(r)),e.opacityExit=eP(t.opacity??1,0,rv(r))):o&&(e.opacity=eP(t.opacity??1,n.opacity??1,r));for(let i=0;i<rd;i++){let o=`border${rh[i]}Radius`,s=rm(t,o),a=rm(n,o);(void 0!==s||void 0!==a)&&(s||(s=0),a||(a=0),0===s||0===a||rp(s)===rp(a)?(e[o]=Math.max(eP(rf(s),rf(a),r),0),(el.test(a)||el.test(s))&&(e[o]+="%")):e[o]=a)}(t.rotate||n.rotate)&&(e.rotate=eP(t.rotate||0,n.rotate||0,r))}(o,i,this.latestValues,r,h,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(!1),this.resumingFrom?.currentAnimation?.stop(!1),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=p.update(()=>{rt.hasAnimatedSinceResize=!0,B.layout++,this.motionValue||(this.motionValue=M(0)),this.currentAnimation=function(e,t,n){let r=L(e)?e:M(e);return r.start(t5("",r,t,n)),r.animation}(this.motionValue,[0,1e3],{...e,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{B.layout--},onComplete:()=>{B.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop(!1)),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&r8(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||nP();let t=nv(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;let r=nv(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}rb(t,n),nN(t,i),nx(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new r_),this.sharedNodes.get(e).add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){let r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;let r={};n.z&&rF("z",e,r,this.animationValues);for(let t=0;t<rO.length;t++)rF(`rotate${rO[t]}`,e,r,this.animationValues),rF(`skew${rO[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return rN;let t={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=rc(e?.pointerEvents)||"",t.transform=n?n(this.latestValues,""):"none",t;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=rc(e?.pointerEvents)||""),this.hasProjected&&!nI(this.latestValues)&&(t.transform=n?n({},""):"none",this.hasProjected=!1),t}let i=r.animationValues||r.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,n){let r="",i=e.x.translate/t.x,o=e.y.translate/t.y,s=n?.z||0;if((i||o||s)&&(r=`translate3d(${i}px, ${o}px, ${s}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:o,skewX:s,skewY:a}=n;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),i&&(r+=`rotateX(${i}deg) `),o&&(r+=`rotateY(${o}deg) `),s&&(r+=`skewX(${s}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,i),n&&(t.transform=n(i,t.transform));let{x:o,y:s}=this.projectionDelta;for(let e in t.transformOrigin=`${100*o.origin}% ${100*s.origin}% 0`,r.animationValues?t.opacity=r===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:t.opacity=r===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,ri){if(void 0===i[e])continue;let{correct:n,applyTo:o,isCSSVariable:s}=ri[e],a="none"===t.transform?i[e]:n(i[e],r);if(o){let e=o.length;for(let n=0;n<e;n++)t[o[n]]=a}else s?this.options.visualElement.renderState.vars[e]=a:t[e]=a}return this.options.layoutId&&(t.pointerEvents=r===this?rc(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop(!1)),this.root.nodes.forEach(r$),this.root.sharedNodes.clear()}}}function rz(e){e.updateLayout()}function rU(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:n,measuredBox:r}=e.layout,{animationType:i}=e.options,o=t.source!==e.layout.source;"size"===i?nC(e=>{let r=o?t.measuredBox[e]:t.layoutBox[e],i=nv(r);r.min=n[e].min,r.max=r.min+i}):r8(i,t.layoutBox,n)&&nC(r=>{let i=o?t.measuredBox[r]:t.layoutBox[r],s=nv(n[r]);i.max=i.min+s,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+s)});let s=nS();nx(s,n,t.layoutBox);let a=nS();o?nx(a,e.applyTransform(r,!0),t.measuredBox):nx(a,n,t.layoutBox);let l=!rA(s),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:o}=r;if(i&&o){let s=nP();nk(s,t.layoutBox,i.layoutBox);let a=nP();nk(a,n,o.layoutBox),rL(s,a)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=a,e.relativeTargetOrigin=s,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:a,layoutDelta:s,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function rH(e){d.value&&rR.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function rW(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function rq(e){e.clearSnapshot()}function r$(e){e.clearMeasurements()}function rY(e){e.isLayoutDirty=!1}function rG(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function rX(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function rK(e){e.resolveTargetDelta()}function rZ(e){e.calcProjection()}function rQ(e){e.resetSkewAndRotation()}function rJ(e){e.removeLeadSnapshot()}function r0(e,t,n){e.translate=eP(t.translate,0,n),e.scale=eP(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function r1(e,t,n,r){e.min=eP(t.min,n.min,r),e.max=eP(t.max,n.max,r)}function r2(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let r5={duration:.45,ease:[.4,0,.1,1]},r4=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),r3=r4("applewebkit/")&&!r4("chrome/")?Math.round:u;function r6(e){e.min=r3(e.min),e.max=r3(e.max)}function r8(e,t,n){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rD(t)-rD(n)))}function r9(e){return e!==e.root&&e.scroll?.wasRoot}let r7=rB({attachResizeListener:(e,t)=>nh(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ie={current:void 0},it=rB({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ie.current){let e=new r7({});e.mount(window),e.setOptions({layoutScroll:!0}),ie.current=e}return ie.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function ir(e,t){let n=function(e,t,n){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,n=(void 0)??t.querySelectorAll(e);return n?Array.from(n):[]}return Array.from(e)}(e),r=new AbortController;return[n,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function ii(e){return!("touch"===e.pointerType||nc.x||nc.y)}function io(e,t,n){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);let i=r["onHover"+n];i&&p.postRender(()=>i(t,nf(t)))}class is extends ns{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=ir(e,n),s=e=>{if(!ii(e))return;let{target:n}=e,r=t(n,e);if("function"!=typeof r||!n)return;let o=e=>{ii(e)&&(r(e),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,i)};return r.forEach(e=>{e.addEventListener("pointerenter",s,i)}),o}(e,(e,t)=>(io(this.node,t,"Start"),e=>io(this.node,e,"End"))))}unmount(){}}class ia extends ns{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=O(nh(this.node.current,"focus",()=>this.onFocus()),nh(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let il=(e,t)=>!!t&&(e===t||il(e,t.parentElement)),iu=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),ic=new WeakSet;function ih(e){return t=>{"Enter"===t.key&&e(t)}}function id(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let ip=(e,t)=>{let n=e.currentTarget;if(!n)return;let r=ih(()=>{if(ic.has(n))return;id(n,"down");let e=ih(()=>{id(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>id(n,"cancel"),t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function im(e){return nd(e)&&!(nc.x||nc.y)}function ig(e,t,n){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);let i=r["onTap"+("End"===n?"":n)];i&&p.postRender(()=>i(t,nf(t)))}class iv extends ns{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=ir(e,n),s=e=>{let r=e.currentTarget;if(!im(e)||ic.has(r))return;ic.add(r);let o=t(r,e),s=(e,t)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),ic.has(r)&&ic.delete(r),im(e)&&"function"==typeof o&&o(e,{success:t})},a=e=>{s(e,r===window||r===document||n.useGlobalTarget||il(r,e.target))},l=e=>{s(e,!1)};window.addEventListener("pointerup",a,i),window.addEventListener("pointercancel",l,i)};return r.forEach(e=>{((n.useGlobalTarget?window:e).addEventListener("pointerdown",s,i),e instanceof HTMLElement)&&(e.addEventListener("focus",e=>ip(e,i)),iu.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),o}(e,(e,t)=>(ig(this.node,t,"Start"),(e,{success:t})=>ig(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let iy=new WeakMap,ix=new WeakMap,ib=e=>{let t=iy.get(e.target);t&&t(e)},iw=e=>{e.forEach(ib)},ik={some:0,all:1};class iE extends ns{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:n,threshold:"number"==typeof r?r:ik[r]};return function(e,t,n){let r=function({root:e,...t}){let n=e||document;ix.has(n)||ix.set(n,{});let r=ix.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(iw,{root:e,...t})),r[i]}(t);return iy.set(e,n),r.observe(e),()=>{iy.delete(e),r.unobserve(e)}}(this.node.current,o,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),o=t?n:r;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}(e,t))&&this.startObserver()}unmount(){}}let iS=(0,n8.createContext)({strict:!1});var iT=n(51508);let iP=(0,n8.createContext)({});function iC(e){return i(e.animate)||ne.some(t=>t9(e[t]))}function iA(e){return!!(iC(e)||e.variants)}function iM(e){return Array.isArray(e)?e.join(" "):e}var iI=n(68972);let iL={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iD={};for(let e in iL)iD[e]={isEnabled:t=>iL[e].some(e=>!!t[e])};let ij=Symbol.for("motionComponentSymbol");var i_=n(80845),iR=n(97494);function iO(e,{layout:t,layoutId:n}){return x.has(e)||e.startsWith("origin")||(t||void 0!==n)&&(!!ri[e]||"opacity"===e)}let iN=(e,t)=>t&&"number"==typeof e?t.transform(e):e,iV={...G,transform:Math.round},iF={borderWidth:eu,borderTopWidth:eu,borderRightWidth:eu,borderBottomWidth:eu,borderLeftWidth:eu,borderRadius:eu,radius:eu,borderTopLeftRadius:eu,borderTopRightRadius:eu,borderBottomRightRadius:eu,borderBottomLeftRadius:eu,width:eu,maxWidth:eu,height:eu,maxHeight:eu,top:eu,right:eu,bottom:eu,left:eu,padding:eu,paddingTop:eu,paddingRight:eu,paddingBottom:eu,paddingLeft:eu,margin:eu,marginTop:eu,marginRight:eu,marginBottom:eu,marginLeft:eu,backgroundPositionX:eu,backgroundPositionY:eu,rotate:ea,rotateX:ea,rotateY:ea,rotateZ:ea,scale:K,scaleX:K,scaleY:K,scaleZ:K,skew:ea,skewX:ea,skewY:ea,distance:eu,translateX:eu,translateY:eu,translateZ:eu,x:eu,y:eu,z:eu,perspective:eu,transformPerspective:eu,opacity:X,originX:ed,originY:ed,originZ:eu,zIndex:iV,fillOpacity:X,strokeOpacity:X,numOctaves:iV},iB={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iz=y.length;function iU(e,t,n){let{style:r,vars:i,transformOrigin:o}=e,s=!1,a=!1;for(let e in t){let n=t[e];if(x.has(e)){s=!0;continue}if(W(e)){i[e]=n;continue}{let t=iN(n,iF[e]);e.startsWith("origin")?(a=!0,o[e]=t):r[e]=t}}if(!t.transform&&(s||n?r.transform=function(e,t,n){let r="",i=!0;for(let o=0;o<iz;o++){let s=y[o],a=e[s];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!s.startsWith("scale"):0===parseFloat(a))||n){let e=iN(a,iF[s]);if(!l){i=!1;let t=iB[s]||s;r+=`${t}(${e}) `}n&&(t[s]=e)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),a){let{originX:e="50%",originY:t="50%",originZ:n=0}=o;r.transformOrigin=`${e} ${t} ${n}`}}let iH=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function iW(e,t,n){for(let r in t)L(t[r])||iO(r,n)||(e[r]=t[r])}let iq=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function i$(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||iq.has(e)}let iY=e=>!i$(e);try{!function(e){e&&(iY=t=>t.startsWith("on")?!i$(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let iG=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function iX(e){if("string"!=typeof e||e.includes("-"));else if(iG.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}let iK={offset:"stroke-dashoffset",array:"stroke-dasharray"},iZ={offset:"strokeDashoffset",array:"strokeDasharray"};function iQ(e,{attrX:t,attrY:n,attrScale:r,pathLength:i,pathSpacing:o=1,pathOffset:s=0,...a},l,u,c){if(iU(e,a,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:h,style:d}=e;h.transform&&(d.transform=h.transform,delete h.transform),(d.transform||h.transformOrigin)&&(d.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),d.transform&&(d.transformBox=c?.transformBox??"fill-box",delete h.transformBox),void 0!==t&&(h.x=t),void 0!==n&&(h.y=n),void 0!==r&&(h.scale=r),void 0!==i&&function(e,t,n=1,r=0,i=!0){e.pathLength=1;let o=i?iK:iZ;e[o.offset]=eu.transform(-r);let s=eu.transform(t),a=eu.transform(n);e[o.array]=`${s} ${a}`}(h,i,o,s,!1)}let iJ=()=>({...iH(),attrs:{}}),i0=e=>"string"==typeof e&&"svg"===e.toLowerCase();var i1=n(82885);let i2=e=>(t,n)=>{let r=(0,n8.useContext)(iP),o=(0,n8.useContext)(i_.t),a=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,o){return{latestValues:function(e,t,n,r){let o={},a=r(e,{});for(let e in a)o[e]=rc(a[e]);let{initial:l,animate:u}=e,c=iC(e),h=iA(e);t&&h&&!c&&!1!==e.inherit&&(void 0===l&&(l=t.initial),void 0===u&&(u=t.animate));let d=!!n&&!1===n.initial,f=(d=d||!1===l)?u:l;if(f&&"boolean"!=typeof f&&!i(f)){let t=Array.isArray(f)?f:[f];for(let n=0;n<t.length;n++){let r=s(e,t[n]);if(r){let{transitionEnd:e,transition:t,...n}=r;for(let e in n){let t=n[e];if(Array.isArray(t)){let e=d?t.length-1:0;t=t[e]}null!==t&&(o[e]=t)}for(let t in e)o[t]=e[t]}}}return o}(n,r,o,e),renderState:t()}})(e,t,r,o);return n?a():(0,i1.M)(a)};function i5(e,t,n){let{style:r}=e,i={};for(let o in r)(L(r[o])||t.style&&L(t.style[o])||iO(o,e)||n?.getValue(o)?.liveStyle!==void 0)&&(i[o]=r[o]);return i}let i4={useVisualState:i2({scrapeMotionValuesFromProps:i5,createRenderState:iH})};function i3(e,t,n){let r=i5(e,t,n);for(let n in e)(L(e[n])||L(t[n]))&&(r[-1!==y.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}let i6={useVisualState:i2({scrapeMotionValuesFromProps:i3,createRenderState:iJ})},i8=e=>t=>t.test(e),i9=[G,eu,el,ea,eh,ec,{test:e=>"auto"===e,parse:e=>e}],i7=e=>i9.find(i8(e)),oe=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),ot=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,on=e=>/^0[^.\s]+$/u.test(e),or=new Set(["brightness","contrast","saturate","opacity"]);function oi(e){let[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=n.match(Q)||[];if(!r)return e;let i=n.replace(r,""),o=+!!or.has(t);return r!==n&&(o*=100),t+"("+o+i+")"}let oo=/\b([a-z-]*)\(.*?\)/gu,os={...eE,getAnimatableNone:e=>{let t=e.match(oo);return t?t.map(oi).join(" "):e}},oa={...iF,color:ep,backgroundColor:ep,outlineColor:ep,fill:ep,stroke:ep,borderColor:ep,borderTopColor:ep,borderRightColor:ep,borderBottomColor:ep,borderLeftColor:ep,filter:os,WebkitFilter:os},ol=e=>oa[e];function ou(e,t){let n=ol(e);return n!==os&&(n=eE),n.getAnimatableNone?n.getAnimatableNone(t):void 0}let oc=new Set(["auto","none","0"]);class oh extends tR{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let n=0;n<e.length;n++){let r=e[n];if("string"==typeof r&&$(r=r.trim())){let i=function e(t,n,r=1){U(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,o]=function(e){let t=ot.exec(e);if(!t)return[,];let[,n,r,i]=t;return[`--${n??r}`,i]}(t);if(!i)return;let s=window.getComputedStyle(n).getPropertyValue(i);if(s){let e=s.trim();return oe(e)?parseFloat(e):e}return $(o)?e(o,n,r+1):o}(r,t.current);void 0!==i&&(e[n]=i),n===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!b.has(n)||2!==e.length)return;let[r,i]=e,o=i7(r),s=i7(i);if(o!==s)if(tT(o)&&tT(s))for(let t=0;t<e.length;t++){let n=e[t];"string"==typeof n&&(e[t]=parseFloat(n))}else tA[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,n=[];for(let t=0;t<e.length;t++){var r;(null===e[t]||("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||on(r)))&&n.push(t)}n.length&&function(e,t,n){let r,i=0;for(;i<e.length&&!r;){let t=e[i];"string"==typeof t&&!oc.has(t)&&ex(t).values.length&&(r=e[i]),i++}if(r&&n)for(let i of t)e[i]=ou(n,r)}(e,n,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tA[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let r=e.getValue(t);r&&r.jump(this.measuredOrigin,!1);let i=n.length-1,o=n[i];n[i]=tA[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,n])=>{e.getValue(t).set(n)}),this.resolveNoneKeyframes()}}let od=[...i9,ep,eE],of=e=>od.find(i8(e)),op={current:null},om={current:!1},og=new WeakMap,ov=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class oy{scrapeMotionValuesFromProps(e,t,n){return{}}constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tR,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=T.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,p.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=s,this.blockInitialAnimation=!!i,this.isControllingVariants=iC(t),this.isVariantNode=iA(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==a[e]&&L(t)&&t.set(a[e],!1)}}mount(e){this.current=e,og.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),om.current||function(){if(om.current=!0,iI.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>op.current=e.matches;e.addListener(t),t()}else op.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||op.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let n;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=x.has(e);r&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&p.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),o(),n&&n(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in iD){let t=iD[e];if(!t)continue;let{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):nP()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<ov.length;t++){let n=ov[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let r=e["on"+n];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=function(e,t,n){for(let r in t){let i=t[r],o=n[r];if(L(i))e.addValue(r,i);else if(L(o))e.addValue(r,M(i,{owner:e}));else if(o!==i)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(r);e.addValue(r,M(void 0!==t?t:i,{owner:e}))}}for(let r in n)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=M(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){let n=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=n&&("string"==typeof n&&(oe(n)||on(n))?n=parseFloat(n):!of(n)&&eE.test(t)&&(n=ou(e,t)),this.setBaseTarget(e,L(n)?n.get():n)),L(n)?n.get():n}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let r=s(this.props,n,this.presenceContext?.custom);r&&(t=r[e])}if(n&&void 0!==t)return t;let r=this.getBaseTargetFromProps(this.props,e);return void 0===r||L(r)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:r}on(e,t){return this.events[e]||(this.events[e]=new E),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class ox extends oy{constructor(){super(...arguments),this.KeyframeResolver=oh}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;L(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function ob(e,{style:t,vars:n},r,i){for(let o in Object.assign(e.style,t,i&&i.getProjectionStyles(r)),n)e.style.setProperty(o,n[o])}class ow extends ox{constructor(){super(...arguments),this.type="html",this.renderInstance=ob}readValueFromInstance(e,t){if(x.has(t))return tE(e,t);{let n=window.getComputedStyle(e),r=(W(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return nV(e,t)}build(e,t,n){iU(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return i5(e,t,n)}}let ok=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class oE extends ox{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=nP}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(x.has(t)){let e=ol(t);return e&&e.default||0}return t=ok.has(t)?t:j(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return i3(e,t,n)}build(e,t,n){iQ(e,t,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(e,t,n,r){for(let n in ob(e,t,void 0,r),t.attrs)e.setAttribute(ok.has(n)?n:j(n),t.attrs[n])}mount(e){this.isSVGTag=i0(e.tagName),super.mount(e)}}let oS=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(n,r)=>"create"===r?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}((n5={animation:{Feature:na},exit:{Feature:nu},inView:{Feature:iE},tap:{Feature:iv},focus:{Feature:ia},hover:{Feature:is},pan:{Feature:n2},drag:{Feature:n0,ProjectionNode:it,MeasureLayout:rs},layout:{ProjectionNode:it,MeasureLayout:rs}},n4=(e,t)=>iX(e)?new oE(t):new ow(t,{allowProjection:e!==n8.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function(e){var t,n;let{preloadedFeatures:r,createVisualElement:i,useRender:o,useVisualState:s,Component:a}=e;function l(e,t){var n,r,l;let u,c={...(0,n8.useContext)(iT.Q),...e,layoutId:function(e){let{layoutId:t}=e,n=(0,n8.useContext)(n7.L).id;return n&&void 0!==t?n+"-"+t:t}(e)},{isStatic:h}=c,d=function(e){let{initial:t,animate:n}=function(e,t){if(iC(e)){let{initial:t,animate:n}=e;return{initial:!1===t||t9(t)?t:void 0,animate:t9(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,n8.useContext)(iP));return(0,n8.useMemo)(()=>({initial:t,animate:n}),[iM(t),iM(n)])}(e),f=s(e,h);if(!h&&iI.B){r=0,l=0,(0,n8.useContext)(iS).strict;let e=function(e){let{drag:t,layout:n}=iD;if(!t&&!n)return{};let r={...t,...n};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==n?void 0:n.isEnabled(e))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(c);u=e.MeasureLayout,d.visualElement=function(e,t,n,r,i){let{visualElement:o}=(0,n8.useContext)(iP),s=(0,n8.useContext)(iS),a=(0,n8.useContext)(i_.t),l=(0,n8.useContext)(iT.Q).reducedMotion,u=(0,n8.useRef)(null);r=r||s.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:o,props:n,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let c=u.current,h=(0,n8.useContext)(re);c&&!c.projection&&i&&("html"===c.type||"svg"===c.type)&&function(e,t,n,r){let{layoutId:i,layout:o,drag:s,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!s||a&&nB(a),visualElement:e,animationType:"string"==typeof o?o:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}(u.current,n,i,h);let d=(0,n8.useRef)(!1);(0,n8.useInsertionEffect)(()=>{c&&d.current&&c.update(n,a)});let f=n[_],p=(0,n8.useRef)(!!f&&!window.MotionHandoffIsComplete?.(f)&&window.MotionHasOptimisedAnimation?.(f));return(0,iR.E)(()=>{c&&(d.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),n6.render(c.render),p.current&&c.animationState&&c.animationState.animateChanges())}),(0,n8.useEffect)(()=>{c&&(!p.current&&c.animationState&&c.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(f)}),p.current=!1))}),c}(a,f,c,i,e.ProjectionNode)}return(0,n3.jsxs)(iP.Provider,{value:d,children:[u&&d.visualElement?(0,n3.jsx)(u,{visualElement:d.visualElement,...c}):null,o(a,e,(n=d.visualElement,(0,n8.useCallback)(e=>{e&&f.onMount&&f.onMount(e),n&&(e?n.mount(e):n.unmount()),t&&("function"==typeof t?t(e):nB(t)&&(t.current=e))},[n])),f,h,d.visualElement)]})}r&&function(e){for(let t in e)iD[t]={...iD[t],...e[t]}}(r),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!=(n=null!=(t=a.displayName)?t:a.name)?n:"",")"));let u=(0,n8.forwardRef)(l);return u[ij]=a,u}({...iX(e)?i6:i4,preloadedFeatures:n5,useRender:function(e=!1){return(t,n,r,{latestValues:i},o)=>{let s=(iX(t)?function(e,t,n,r){let i=(0,n8.useMemo)(()=>{let n=iJ();return iQ(n,t,i0(r),e.transformTemplate,e.style),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};iW(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let n={},r=function(e,t){let n=e.style||{},r={};return iW(r,n,e),Object.assign(r,function({transformTemplate:e},t){return(0,n8.useMemo)(()=>{let n=iH();return iU(n,t,e),Object.assign({},n.vars,n.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n})(n,i,o,t),a=function(e,t,n){let r={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(iY(i)||!0===n&&i$(i)||!t&&!i$(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(n,"string"==typeof t,e),l=t!==n8.Fragment?{...a,...s,ref:r}:{},{children:u}=n,c=(0,n8.useMemo)(()=>L(u)?u.get():u,[u]);return(0,n8.createElement)(t,{...l,children:c})}}(t),createVisualElement:n4,Component:e})}))},87924:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,i.default)(e),o="function"==typeof t;return r.forEach(function(e){if("declaration"===e.type){var r=e.property,i=e.value;o?t(r,i,e):i&&((n=n||{})[r]=i)}}),n};var i=r(n(36301))},88706:(e,t,n)=>{e.exports=n(96548)("length")},89377:(e,t,n)=>{var r=n(25016),i=n(18285),o=n(49382),s=n(25528);e.exports=function e(t,n,a,l){var u,c,h=t.children(n),d=t.node(n),f=d?d.borderLeft:void 0,p=d?d.borderRight:void 0,m={};f&&(h=r.filter(h,function(e){return e!==f&&e!==p}));var g=i(t,h);r.forEach(g,function(n){if(t.children(n.v).length){var i,o,s=e(t,n.v,a,l);m[n.v]=s,r.has(s,"barycenter")&&(i=n,o=s,r.isUndefined(i.barycenter)?(i.barycenter=o.barycenter,i.weight=o.weight):(i.barycenter=(i.barycenter*i.weight+o.barycenter*o.weight)/(i.weight+o.weight),i.weight+=o.weight))}});var v=o(g,a);u=v,c=m,r.forEach(u,function(e){e.vs=r.flatten(e.vs.map(function(e){return c[e]?c[e].vs:e}),!0)});var y=s(v,l);if(f&&(y.vs=r.flatten([f,y.vs,p],!0),t.predecessors(f).length)){var x=t.node(t.predecessors(f)[0]),b=t.node(t.predecessors(p)[0]);r.has(y,"barycenter")||(y.barycenter=0,y.weight=0),y.barycenter=(y.barycenter*y.weight+x.order+b.order)/(y.weight+2),y.weight+=2}return y}},89803:(e,t,n)=>{var r=n(57213);e.exports=function(e,t){return r(t,function(t){return e[t]})}},89865:(e,t,n)=>{var r=n(89803),i=n(35095);e.exports=function(e){return null==e?[]:r(e,i(e))}},90792:(e,t,n)=>{"use strict";n.d(t,{oz:()=>nI});var r={};n.r(r),n.d(r,{boolean:()=>g,booleanish:()=>v,commaOrSpaceSeparated:()=>k,commaSeparated:()=>w,number:()=>x,overloadedBoolean:()=>y,spaceSeparated:()=>b});var i={};n.r(i),n.d(i,{attentionMarkers:()=>tC,contentInitial:()=>tw,disable:()=>tA,document:()=>tb,flow:()=>tE,flowInitial:()=>tk,insideSpan:()=>tP,string:()=>tS,text:()=>tT});let o=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,s=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,a={};function l(e,t){return((t||a).jsx?s:o).test(e)}let u=/[ \t\n\f\r]/g;function c(e){return""===e.replace(u,"")}class h{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function d(e,t){let n={},r={};for(let t of e)Object.assign(n,t.property),Object.assign(r,t.normal);return new h(n,r,t)}function f(e){return e.toLowerCase()}h.prototype.normal={},h.prototype.property={},h.prototype.space=void 0;class p{constructor(e,t){this.attribute=t,this.property=e}}p.prototype.attribute="",p.prototype.booleanish=!1,p.prototype.boolean=!1,p.prototype.commaOrSpaceSeparated=!1,p.prototype.commaSeparated=!1,p.prototype.defined=!1,p.prototype.mustUseProperty=!1,p.prototype.number=!1,p.prototype.overloadedBoolean=!1,p.prototype.property="",p.prototype.spaceSeparated=!1,p.prototype.space=void 0;let m=0,g=E(),v=E(),y=E(),x=E(),b=E(),w=E(),k=E();function E(){return 2**++m}let S=Object.keys(r);class T extends p{constructor(e,t,n,i){let o=-1;if(super(e,t),function(e,t,n){n&&(e[t]=n)}(this,"space",i),"number"==typeof n)for(;++o<S.length;){let e=S[o];!function(e,t,n){n&&(e[t]=n)}(this,S[o],(n&r[e])===r[e])}}}function P(e){let t={},n={};for(let[r,i]of Object.entries(e.properties)){let o=new T(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(o.mustUseProperty=!0),t[r]=o,n[f(r)]=r,n[f(o.attribute)]=r}return new h(t,n,e.space)}T.prototype.defined=!0;let C=P({properties:{ariaActiveDescendant:null,ariaAtomic:v,ariaAutoComplete:null,ariaBusy:v,ariaChecked:v,ariaColCount:x,ariaColIndex:x,ariaColSpan:x,ariaControls:b,ariaCurrent:null,ariaDescribedBy:b,ariaDetails:null,ariaDisabled:v,ariaDropEffect:b,ariaErrorMessage:null,ariaExpanded:v,ariaFlowTo:b,ariaGrabbed:v,ariaHasPopup:null,ariaHidden:v,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:b,ariaLevel:x,ariaLive:null,ariaModal:v,ariaMultiLine:v,ariaMultiSelectable:v,ariaOrientation:null,ariaOwns:b,ariaPlaceholder:null,ariaPosInSet:x,ariaPressed:v,ariaReadOnly:v,ariaRelevant:null,ariaRequired:v,ariaRoleDescription:b,ariaRowCount:x,ariaRowIndex:x,ariaRowSpan:x,ariaSelected:v,ariaSetSize:x,ariaSort:null,ariaValueMax:x,ariaValueMin:x,ariaValueNow:x,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function A(e,t){return t in e?e[t]:t}function M(e,t){return A(e,t.toLowerCase())}let I=P({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:w,acceptCharset:b,accessKey:b,action:null,allow:null,allowFullScreen:g,allowPaymentRequest:g,allowUserMedia:g,alt:null,as:null,async:g,autoCapitalize:null,autoComplete:b,autoFocus:g,autoPlay:g,blocking:b,capture:null,charSet:null,checked:g,cite:null,className:b,cols:x,colSpan:null,content:null,contentEditable:v,controls:g,controlsList:b,coords:x|w,crossOrigin:null,data:null,dateTime:null,decoding:null,default:g,defer:g,dir:null,dirName:null,disabled:g,download:y,draggable:v,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:g,formTarget:null,headers:b,height:x,hidden:y,high:x,href:null,hrefLang:null,htmlFor:b,httpEquiv:b,id:null,imageSizes:null,imageSrcSet:null,inert:g,inputMode:null,integrity:null,is:null,isMap:g,itemId:null,itemProp:b,itemRef:b,itemScope:g,itemType:b,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:g,low:x,manifest:null,max:null,maxLength:x,media:null,method:null,min:null,minLength:x,multiple:g,muted:g,name:null,nonce:null,noModule:g,noValidate:g,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:g,optimum:x,pattern:null,ping:b,placeholder:null,playsInline:g,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:g,referrerPolicy:null,rel:b,required:g,reversed:g,rows:x,rowSpan:x,sandbox:b,scope:null,scoped:g,seamless:g,selected:g,shadowRootClonable:g,shadowRootDelegatesFocus:g,shadowRootMode:null,shape:null,size:x,sizes:null,slot:null,span:x,spellCheck:v,src:null,srcDoc:null,srcLang:null,srcSet:null,start:x,step:null,style:null,tabIndex:x,target:null,title:null,translate:null,type:null,typeMustMatch:g,useMap:null,value:v,width:x,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:b,axis:null,background:null,bgColor:null,border:x,borderColor:null,bottomMargin:x,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:g,declare:g,event:null,face:null,frame:null,frameBorder:null,hSpace:x,leftMargin:x,link:null,longDesc:null,lowSrc:null,marginHeight:x,marginWidth:x,noResize:g,noHref:g,noShade:g,noWrap:g,object:null,profile:null,prompt:null,rev:null,rightMargin:x,rules:null,scheme:null,scrolling:v,standby:null,summary:null,text:null,topMargin:x,valueType:null,version:null,vAlign:null,vLink:null,vSpace:x,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:g,disableRemotePlayback:g,prefix:null,property:null,results:x,security:null,unselectable:null},space:"html",transform:M}),L=P({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:k,accentHeight:x,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:x,amplitude:x,arabicForm:null,ascent:x,attributeName:null,attributeType:null,azimuth:x,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:x,by:null,calcMode:null,capHeight:x,className:b,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:x,diffuseConstant:x,direction:null,display:null,dur:null,divisor:x,dominantBaseline:null,download:g,dx:null,dy:null,edgeMode:null,editable:null,elevation:x,enableBackground:null,end:null,event:null,exponent:x,externalResourcesRequired:null,fill:null,fillOpacity:x,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:w,g2:w,glyphName:w,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:x,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:x,horizOriginX:x,horizOriginY:x,id:null,ideographic:x,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:x,k:x,k1:x,k2:x,k3:x,k4:x,kernelMatrix:k,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:x,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:x,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:x,overlineThickness:x,paintOrder:null,panose1:null,path:null,pathLength:x,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:b,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:x,pointsAtY:x,pointsAtZ:x,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:k,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:k,rev:k,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:k,requiredFeatures:k,requiredFonts:k,requiredFormats:k,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:x,specularExponent:x,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:x,strikethroughThickness:x,string:null,stroke:null,strokeDashArray:k,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:x,strokeOpacity:x,strokeWidth:null,style:null,surfaceScale:x,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:k,tabIndex:x,tableValues:null,target:null,targetX:x,targetY:x,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:k,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:x,underlineThickness:x,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:x,values:null,vAlphabetic:x,vMathematical:x,vectorEffect:null,vHanging:x,vIdeographic:x,version:null,vertAdvY:x,vertOriginX:x,vertOriginY:x,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:x,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:A}),D=P({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),j=P({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:M}),_=P({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),R=d([C,I,D,j,_],"html"),O=d([C,L,D,j,_],"svg"),N=/[A-Z]/g,V=/-[a-z]/g,F=/^data[-\w.:]+$/i;function B(e){return"-"+e.toLowerCase()}function z(e){return e.charAt(1).toUpperCase()}let U={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var H=n(53724);let W=$("end"),q=$("start");function $(e){return function(t){let n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function Y(e){return e&&"object"==typeof e?"position"in e||"type"in e?X(e.position):"start"in e||"end"in e?X(e):"line"in e||"column"in e?G(e):"":""}function G(e){return K(e&&e.line)+":"+K(e&&e.column)}function X(e){return G(e&&e.start)+"-"+G(e&&e.end)}function K(e){return e&&"number"==typeof e?e:1}class Z extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",i={},o=!1;if(t&&(i="line"in t&&"column"in t||"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!i.cause&&e&&(o=!0,r=e.message,i.cause=e),!i.ruleId&&!i.source&&"string"==typeof n){let e=n.indexOf(":");-1===e?i.ruleId=n:(i.source=n.slice(0,e),i.ruleId=n.slice(e+1))}if(!i.place&&i.ancestors&&i.ancestors){let e=i.ancestors[i.ancestors.length-1];e&&(i.place=e.position)}let s=i.place&&"start"in i.place?i.place.start:i.place;this.ancestors=i.ancestors||void 0,this.cause=i.cause||void 0,this.column=s?s.column:void 0,this.fatal=void 0,this.file,this.message=r,this.line=s?s.line:void 0,this.name=Y(i.place)||"1:1",this.place=i.place||void 0,this.reason=this.message,this.ruleId=i.ruleId||void 0,this.source=i.source||void 0,this.stack=o&&i.cause&&"string"==typeof i.cause.stack?i.cause.stack:"",this.actual,this.expected,this.note,this.url}}Z.prototype.file="",Z.prototype.name="",Z.prototype.reason="",Z.prototype.message="",Z.prototype.stack="",Z.prototype.column=void 0,Z.prototype.line=void 0,Z.prototype.ancestors=void 0,Z.prototype.cause=void 0,Z.prototype.fatal=void 0,Z.prototype.place=void 0,Z.prototype.ruleId=void 0,Z.prototype.source=void 0;let Q={}.hasOwnProperty,J=new Map,ee=/[A-Z]/g,et=new Set(["table","tbody","thead","tfoot","tr"]),en=new Set(["td","th"]),er="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function ei(e,t,n){var r;return"element"===t.type?function(e,t,n){let r=e.schema,i=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(e.schema=O),e.ancestors.push(t);let o=el(e,t.tagName,!1),s=function(e,t){let n,r,i={};for(r in t.properties)if("children"!==r&&Q.call(t.properties,r)){let o=function(e,t,n){let r=function(e,t){let n=f(t),r=t,i=p;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&F.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(V,z);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!V.test(e)){let n=e.replace(N,B);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}i=T}return new i(r,t)}(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?function(e,t){let n={};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}(n):n.join(" ").trim()),"style"===r.property){let t="object"==typeof n?n:function(e,t){try{return H(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};let t=new Z("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:n,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw t.file=e.filePath||void 0,t.url=er+"#cannot-parse-style-attribute",t}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){let t,n={};for(t in e)Q.call(e,t)&&(n[function(e){let t=e.replace(ee,ec);return"ms-"===t.slice(0,3)&&(t="-"+t),t}(t)]=e[t]);return n}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?U[r.property]||r.property:r.attribute,n]}}(e,r,t.properties[r]);if(o){let[r,s]=o;e.tableCellAlignToStyle&&"align"===r&&"string"==typeof s&&en.has(t.tagName)?n=s:i[r]=s}}return n&&((i.style||(i.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=n),i}(e,t),a=ea(e,t);return et.has(t.tagName)&&(a=a.filter(function(e){return"string"!=typeof e||!("object"==typeof e?"text"===e.type&&c(e.value):c(e))})),eo(e,s,o,t),es(s,a),e.ancestors.pop(),e.schema=r,e.create(t,o,s,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){let n=t.data.estree.body[0];return n.type,e.evaluater.evaluateExpression(n.expression)}eu(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){let r=e.schema,i=r;"svg"===t.name&&"html"===r.space&&(e.schema=O),e.ancestors.push(t);let o=null===t.name?e.Fragment:el(e,t.name,!0),s=function(e,t){let n={};for(let r of t.attributes)if("mdxJsxExpressionAttribute"===r.type)if(r.data&&r.data.estree&&e.evaluater){let t=r.data.estree.body[0];t.type;let i=t.expression;i.type;let o=i.properties[0];o.type,Object.assign(n,e.evaluater.evaluateExpression(o.argument))}else eu(e,t.position);else{let i,o=r.name;if(r.value&&"object"==typeof r.value)if(r.value.data&&r.value.data.estree&&e.evaluater){let t=r.value.data.estree.body[0];t.type,i=e.evaluater.evaluateExpression(t.expression)}else eu(e,t.position);else i=null===r.value||r.value;n[o]=i}return n}(e,t),a=ea(e,t);return eo(e,s,o,t),es(s,a),e.ancestors.pop(),e.schema=r,e.create(t,o,s,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);eu(e,t.position)}(e,t):"root"===t.type?function(e,t,n){let r={};return es(r,ea(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?(r=0,t.value):void 0}function eo(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function es(e,t){if(t.length>0){let n=t.length>1?t:t[0];n&&(e.children=n)}}function ea(e,t){let n=[],r=-1,i=e.passKeys?new Map:J;for(;++r<t.children.length;){let o,s=t.children[r];if(e.passKeys){let e="element"===s.type?s.tagName:"mdxJsxFlowElement"===s.type||"mdxJsxTextElement"===s.type?s.name:void 0;if(e){let t=i.get(e)||0;o=e+"-"+t,i.set(e,t+1)}}let a=ei(e,s,o);void 0!==a&&n.push(a)}return n}function el(e,t,n){let r;if(n)if(t.includes(".")){let e,n=t.split("."),i=-1;for(;++i<n.length;){let t=l(n[i])?{type:"Identifier",name:n[i]}:{type:"Literal",value:n[i]};e=e?{type:"MemberExpression",object:e,property:t,computed:!!(i&&"Literal"===t.type),optional:!1}:t}r=e}else r=l(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};else r={type:"Literal",value:t};if("Literal"===r.type){let t=r.value;return Q.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);eu(e)}function eu(e,t){let n=new Z("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=er+"#cannot-handle-mdx-estrees-without-createevaluater",n}function ec(e){return"-"+e.toLowerCase()}let eh={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};var ed=n(95155);n(12115);let ef={};function ep(e,t,n){var r;if((r=e)&&"object"==typeof r){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return em(e.children,t,n)}return Array.isArray(e)?em(e,t,n):""}function em(e,t,n){let r=[],i=-1;for(;++i<e.length;)r[i]=ep(e[i],t,n);return r.join("")}function eg(e,t,n,r){let i,o=e.length,s=0;if(t=t<0?-t>o?0:o+t:t>o?o:t,n=n>0?n:0,r.length<1e4)(i=Array.from(r)).unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);s<r.length;)(i=r.slice(s,s+1e4)).unshift(t,0),e.splice(...i),s+=1e4,t+=1e4}function ev(e,t){return e.length>0?(eg(e,e.length,0,t),e):t}class ey{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){let n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-(t||0),Number.POSITIVE_INFINITY);return n&&ex(this.left,n),r.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),ex(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),ex(this.right,e.reverse())}setCursor(e){if(e!==this.left.length&&(!(e>this.left.length)||0!==this.right.length)&&(!(e<0)||0!==this.left.length))if(e<this.left.length){let t=this.left.splice(e,Number.POSITIVE_INFINITY);ex(this.right,t.reverse())}else{let t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);ex(this.left,t.reverse())}}}function ex(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function eb(e){let t,n,r,i,o,s,a,l={},u=-1,c=new ey(e);for(;++u<c.length;){for(;u in l;)u=l[u];if(t=c.get(u),u&&"chunkFlow"===t[1].type&&"listItemPrefix"===c.get(u-1)[1].type&&((r=0)<(s=t[1]._tokenizer.events).length&&"lineEndingBlank"===s[r][1].type&&(r+=2),r<s.length&&"content"===s[r][1].type))for(;++r<s.length&&"content"!==s[r][1].type;)"chunkText"===s[r][1].type&&(s[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===t[0])t[1].contentType&&(Object.assign(l,function(e,t){let n,r,i=e.get(t)[1],o=e.get(t)[2],s=t-1,a=[],l=i._tokenizer;!l&&(l=o.parser[i.contentType](i.start),i._contentTypeTextTrailing&&(l._contentTypeTextTrailing=!0));let u=l.events,c=[],h={},d=-1,f=i,p=0,m=0,g=[0];for(;f;){for(;e.get(++s)[1]!==f;);a.push(s),!f._tokenizer&&(n=o.sliceStream(f),f.next||n.push(null),r&&l.defineSkip(f.start),f._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=!0),l.write(n),f._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=void 0)),r=f,f=f.next}for(f=i;++d<u.length;)"exit"===u[d][0]&&"enter"===u[d-1][0]&&u[d][1].type===u[d-1][1].type&&u[d][1].start.line!==u[d][1].end.line&&(m=d+1,g.push(m),f._tokenizer=void 0,f.previous=void 0,f=f.next);for(l.events=[],f?(f._tokenizer=void 0,f.previous=void 0):g.pop(),d=g.length;d--;){let t=u.slice(g[d],g[d+1]),n=a.pop();c.push([n,n+t.length-1]),e.splice(n,2,t)}for(c.reverse(),d=-1;++d<c.length;)h[p+c[d][0]]=p+c[d][1],p+=c[d][1]-c[d][0]-1;return h}(c,u)),u=l[u],a=!0);else if(t[1]._container){for(r=u,n=void 0;r--;)if("lineEnding"===(i=c.get(r))[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(n&&(c.get(n)[1].type="lineEndingBlank"),i[1].type="lineEnding",n=r);else if("linePrefix"===i[1].type||"listItemIndent"===i[1].type);else break;n&&(t[1].end={...c.get(n)[1].start},(o=c.slice(n,u)).unshift(t),c.splice(n,u-n+1,o))}}return eg(e,0,Number.POSITIVE_INFINITY,c.slice(0)),!a}let ew={}.hasOwnProperty,ek=e_(/[A-Za-z]/),eE=e_(/[\dA-Za-z]/),eS=e_(/[#-'*+\--9=?A-Z^-~]/);function eT(e){return null!==e&&(e<32||127===e)}let eP=e_(/\d/),eC=e_(/[\dA-Fa-f]/),eA=e_(/[!-/:-@[-`{-~]/);function eM(e){return null!==e&&e<-2}function eI(e){return null!==e&&(e<0||32===e)}function eL(e){return -2===e||-1===e||32===e}let eD=e_(/\p{P}|\p{S}/u),ej=e_(/\s/);function e_(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function eR(e,t,n,r){let i=r?r-1:Number.POSITIVE_INFINITY,o=0;return function(r){return eL(r)?(e.enter(n),function r(s){return eL(s)&&o++<i?(e.consume(s),r):(e.exit(n),t(s))}(r)):t(r)}}let eO={tokenize:function(e){let t,n=e.attempt(this.parser.constructs.contentInitial,function(t){return null===t?void e.consume(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),eR(e,n,"linePrefix"))},function(n){return e.enter("paragraph"),function n(r){let i=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=i),t=i,function t(r){if(null===r){e.exit("chunkText"),e.exit("paragraph"),e.consume(r);return}return eM(r)?(e.consume(r),e.exit("chunkText"),n):(e.consume(r),t)}(r)}(n)});return n}},eN={tokenize:function(e){let t,n,r,i=this,o=[],s=0;return a;function a(t){if(s<o.length){let n=o[s];return i.containerState=n[1],e.attempt(n[0].continuation,l,u)(t)}return u(t)}function l(e){if(s++,i.containerState._closeFlow){let n;i.containerState._closeFlow=void 0,t&&v();let r=i.events.length,o=r;for(;o--;)if("exit"===i.events[o][0]&&"chunkFlow"===i.events[o][1].type){n=i.events[o][1].end;break}g(s);let a=r;for(;a<i.events.length;)i.events[a][1].end={...n},a++;return eg(i.events,o+1,0,i.events.slice(r)),i.events.length=a,u(e)}return a(e)}function u(n){if(s===o.length){if(!t)return d(n);if(t.currentConstruct&&t.currentConstruct.concrete)return p(n);i.interrupt=!!(t.currentConstruct&&!t._gfmTableDynamicInterruptHack)}return i.containerState={},e.check(eV,c,h)(n)}function c(e){return t&&v(),g(s),d(e)}function h(e){return i.parser.lazy[i.now().line]=s!==o.length,r=i.now().offset,p(e)}function d(t){return i.containerState={},e.attempt(eV,f,p)(t)}function f(e){return s++,o.push([i.currentConstruct,i.containerState]),d(e)}function p(r){if(null===r){t&&v(),g(0),e.consume(r);return}return t=t||i.parser.flow(i.now()),e.enter("chunkFlow",{_tokenizer:t,contentType:"flow",previous:n}),function t(n){if(null===n){m(e.exit("chunkFlow"),!0),g(0),e.consume(n);return}return eM(n)?(e.consume(n),m(e.exit("chunkFlow")),s=0,i.interrupt=void 0,a):(e.consume(n),t)}(r)}function m(e,o){let a=i.sliceStream(e);if(o&&a.push(null),e.previous=n,n&&(n.next=e),n=e,t.defineSkip(e.start),t.write(a),i.parser.lazy[e.start.line]){let e,n,o=t.events.length;for(;o--;)if(t.events[o][1].start.offset<r&&(!t.events[o][1].end||t.events[o][1].end.offset>r))return;let a=i.events.length,l=a;for(;l--;)if("exit"===i.events[l][0]&&"chunkFlow"===i.events[l][1].type){if(e){n=i.events[l][1].end;break}e=!0}for(g(s),o=a;o<i.events.length;)i.events[o][1].end={...n},o++;eg(i.events,l+1,0,i.events.slice(a)),i.events.length=o}}function g(t){let n=o.length;for(;n-- >t;){let t=o[n];i.containerState=t[1],t[0].exit.call(i,e)}o.length=t}function v(){t.write([null]),n=void 0,t=void 0,i.containerState._closeFlow=void 0}}},eV={tokenize:function(e,t,n){return eR(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}},eF={partial:!0,tokenize:function(e,t,n){return function(t){return eL(t)?eR(e,r,"linePrefix")(t):r(t)};function r(e){return null===e||eM(e)?t(e):n(e)}}},eB={resolve:function(e){return eb(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?i(t):eM(t)?e.check(ez,o,i)(t):(e.consume(t),r)}function i(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function o(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},ez={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),eR(e,i,"linePrefix")};function i(i){if(null===i||eM(i))return n(i);let o=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&o&&"linePrefix"===o[1].type&&o[2].sliceSerialize(o[1],!0).length>=4?t(i):e.interrupt(r.parser.constructs.flow,n,t)(i)}}},eU={tokenize:function(e){let t=this,n=e.attempt(eF,function(r){return null===r?void e.consume(r):(e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n)},e.attempt(this.parser.constructs.flowInitial,r,eR(e,e.attempt(this.parser.constructs.flow,r,e.attempt(eB,r)),"linePrefix")));return n;function r(r){return null===r?void e.consume(r):(e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n)}}},eH={resolveAll:eY()},eW=e$("string"),eq=e$("text");function e$(e){return{resolveAll:eY("text"===e?eG:void 0),tokenize:function(t){let n=this,r=this.parser.constructs[e],i=t.attempt(r,o,s);return o;function o(e){return l(e)?i(e):s(e)}function s(e){return null===e?void t.consume(e):(t.enter("data"),t.consume(e),a)}function a(e){return l(e)?(t.exit("data"),i(e)):(t.consume(e),a)}function l(e){if(null===e)return!0;let t=r[e],i=-1;if(t)for(;++i<t.length;){let e=t[i];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function eY(e){return function(t,n){let r,i=-1;for(;++i<=t.length;)void 0===r?t[i]&&"data"===t[i][1].type&&(r=i,i++):t[i]&&"data"===t[i][1].type||(i!==r+2&&(t[r][1].end=t[i-1][1].end,t.splice(r+2,i-r-2),i=r+2),r=void 0);return e?e(t,n):t}}function eG(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){let r,i=e[n-1][1],o=t.sliceStream(i),s=o.length,a=-1,l=0;for(;s--;){let e=o[s];if("string"==typeof e){for(a=e.length;32===e.charCodeAt(a-1);)l++,a--;if(a)break;a=-1}else if(-2===e)r=!0,l++;else if(-1===e);else{s++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(l=0),l){let o={type:n===e.length||r||l<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:s?a:i.start._bufferIndex+a,_index:i.start._index+s,line:i.end.line,column:i.end.column-l,offset:i.end.offset-l},end:{...i.end}};i.end={...o.start},i.start.offset===i.end.offset?Object.assign(i,o):(e.splice(n,0,["enter",o,t],["exit",o,t]),n+=2)}n++}return e}let eX={name:"thematicBreak",tokenize:function(e,t,n){let r,i=0;return function(o){var s;return e.enter("thematicBreak"),r=s=o,function o(s){return s===r?(e.enter("thematicBreakSequence"),function t(n){return n===r?(e.consume(n),i++,t):(e.exit("thematicBreakSequence"),eL(n)?eR(e,o,"whitespace")(n):o(n))}(s)):i>=3&&(null===s||eM(s))?(e.exit("thematicBreak"),t(s)):n(s)}(s)}}},eK={continuation:{tokenize:function(e,t,n){let r=this;return r.containerState._closeFlow=void 0,e.check(eF,function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,eR(e,t,"listItemIndent",r.containerState.size+1)(n)},function(n){return r.containerState.furtherBlankLines||!eL(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,i(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(eQ,t,i)(n))});function i(i){return r.containerState._closeFlow=!0,r.interrupt=void 0,eR(e,e.attempt(eK,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(i)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){let r=this,i=r.events[r.events.length-1],o=i&&"linePrefix"===i[1].type?i[2].sliceSerialize(i[1],!0).length:0,s=0;return function(t){let i=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===i?!r.containerState.marker||t===r.containerState.marker:eP(t)){if(r.containerState.type||(r.containerState.type=i,e.enter(i,{_container:!0})),"listUnordered"===i)return e.enter("listItemPrefix"),42===t||45===t?e.check(eX,n,a)(t):a(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),function t(i){return eP(i)&&++s<10?(e.consume(i),t):(!r.interrupt||s<2)&&(r.containerState.marker?i===r.containerState.marker:41===i||46===i)?(e.exit("listItemValue"),a(i)):n(i)}(t)}return n(t)};function a(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(eF,r.interrupt?n:l,e.attempt(eZ,c,u))}function l(e){return r.containerState.initialBlankLine=!0,o++,c(e)}function u(t){return eL(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),c):n(t)}function c(n){return r.containerState.size=o+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},eZ={partial:!0,tokenize:function(e,t,n){let r=this;return eR(e,function(e){let i=r.events[r.events.length-1];return!eL(e)&&i&&"listItemPrefixWhitespace"===i[1].type?t(e):n(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},eQ={partial:!0,tokenize:function(e,t,n){let r=this;return eR(e,function(e){let i=r.events[r.events.length-1];return i&&"listItemIndent"===i[1].type&&i[2].sliceSerialize(i[1],!0).length===r.containerState.size?t(e):n(e)},"listItemIndent",r.containerState.size+1)}},eJ={continuation:{tokenize:function(e,t,n){let r=this;return function(t){return eL(t)?eR(e,i,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):i(t)};function i(r){return e.attempt(eJ,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){let r=this;return function(t){if(62===t){let n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),i}return n(t)};function i(n){return eL(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};function e0(e,t,n,r,i,o,s,a,l){let u=l||Number.POSITIVE_INFINITY,c=0;return function(t){return 60===t?(e.enter(r),e.enter(i),e.enter(o),e.consume(t),e.exit(o),h):null===t||32===t||41===t||eT(t)?n(t):(e.enter(r),e.enter(s),e.enter(a),e.enter("chunkString",{contentType:"string"}),p(t))};function h(n){return 62===n?(e.enter(o),e.consume(n),e.exit(o),e.exit(i),e.exit(r),t):(e.enter(a),e.enter("chunkString",{contentType:"string"}),d(n))}function d(t){return 62===t?(e.exit("chunkString"),e.exit(a),h(t)):null===t||60===t||eM(t)?n(t):(e.consume(t),92===t?f:d)}function f(t){return 60===t||62===t||92===t?(e.consume(t),d):d(t)}function p(i){return!c&&(null===i||41===i||eI(i))?(e.exit("chunkString"),e.exit(a),e.exit(s),e.exit(r),t(i)):c<u&&40===i?(e.consume(i),c++,p):41===i?(e.consume(i),c--,p):null===i||32===i||40===i||eT(i)?n(i):(e.consume(i),92===i?m:p)}function m(t){return 40===t||41===t||92===t?(e.consume(t),p):p(t)}}function e1(e,t,n,r,i,o){let s,a=this,l=0;return function(t){return e.enter(r),e.enter(i),e.consume(t),e.exit(i),e.enter(o),u};function u(h){return l>999||null===h||91===h||93===h&&!s||94===h&&!l&&"_hiddenFootnoteSupport"in a.parser.constructs?n(h):93===h?(e.exit(o),e.enter(i),e.consume(h),e.exit(i),e.exit(r),t):eM(h)?(e.enter("lineEnding"),e.consume(h),e.exit("lineEnding"),u):(e.enter("chunkString",{contentType:"string"}),c(h))}function c(t){return null===t||91===t||93===t||eM(t)||l++>999?(e.exit("chunkString"),u(t)):(e.consume(t),s||(s=!eL(t)),92===t?h:c)}function h(t){return 91===t||92===t||93===t?(e.consume(t),l++,c):c(t)}}function e2(e,t,n,r,i,o){let s;return function(t){return 34===t||39===t||40===t?(e.enter(r),e.enter(i),e.consume(t),e.exit(i),s=40===t?41:t,a):n(t)};function a(n){return n===s?(e.enter(i),e.consume(n),e.exit(i),e.exit(r),t):(e.enter(o),l(n))}function l(t){return t===s?(e.exit(o),a(s)):null===t?n(t):eM(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),eR(e,l,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),u(t))}function u(t){return t===s||null===t||eM(t)?(e.exit("chunkString"),l(t)):(e.consume(t),92===t?c:u)}function c(t){return t===s||92===t?(e.consume(t),u):u(t)}}function e5(e,t){let n;return function r(i){return eM(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),n=!0,r):eL(i)?eR(e,r,n?"linePrefix":"lineSuffix")(i):t(i)}}function e4(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}let e3={partial:!0,tokenize:function(e,t,n){return function(t){return eI(t)?e5(e,r)(t):n(t)};function r(t){return e2(e,i,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function i(t){return eL(t)?eR(e,o,"whitespace")(t):o(t)}function o(e){return null===e||eM(e)?t(e):n(e)}}},e6={name:"codeIndented",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("codeIndented"),eR(e,i,"linePrefix",5)(t)};function i(t){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?function t(n){return null===n?o(n):eM(n)?e.attempt(e8,t,o)(n):(e.enter("codeFlowValue"),function n(r){return null===r||eM(r)?(e.exit("codeFlowValue"),t(r)):(e.consume(r),n)}(n))}(t):n(t)}function o(n){return e.exit("codeIndented"),t(n)}}},e8={partial:!0,tokenize:function(e,t,n){let r=this;return i;function i(t){return r.parser.lazy[r.now().line]?n(t):eM(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):eR(e,o,"linePrefix",5)(t)}function o(e){let o=r.events[r.events.length-1];return o&&"linePrefix"===o[1].type&&o[2].sliceSerialize(o[1],!0).length>=4?t(e):eM(e)?i(e):n(e)}}},e9={name:"setextUnderline",resolveTo:function(e,t){let n,r,i,o=e.length;for(;o--;)if("enter"===e[o][0]){if("content"===e[o][1].type){n=o;break}"paragraph"===e[o][1].type&&(r=o)}else"content"===e[o][1].type&&e.splice(o,1),i||"definition"!==e[o][1].type||(i=o);let s={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};return e[r][1].type="setextHeadingText",i?(e.splice(r,0,["enter",s,t]),e.splice(i+1,0,["exit",e[n][1],t]),e[n][1].end={...e[i][1].end}):e[n][1]=s,e.push(["exit",s,t]),e},tokenize:function(e,t,n){let r,i=this;return function(t){var s;let a,l=i.events.length;for(;l--;)if("lineEnding"!==i.events[l][1].type&&"linePrefix"!==i.events[l][1].type&&"content"!==i.events[l][1].type){a="paragraph"===i.events[l][1].type;break}return!i.parser.lazy[i.now().line]&&(i.interrupt||a)?(e.enter("setextHeadingLine"),r=t,s=t,e.enter("setextHeadingLineSequence"),function t(n){return n===r?(e.consume(n),t):(e.exit("setextHeadingLineSequence"),eL(n)?eR(e,o,"lineSuffix")(n):o(n))}(s)):n(t)};function o(r){return null===r||eM(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}},e7=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],te=["pre","script","style","textarea"],tt={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(eF,t,n)}}},tn={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return eM(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):n(t)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},tr={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return null===t?n(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},ti={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){let r,i=this,o={partial:!0,tokenize:function(e,t,n){let o=0;return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),s};function s(t){return e.enter("codeFencedFence"),eL(t)?eR(e,l,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):l(t)}function l(t){return t===r?(e.enter("codeFencedFenceSequence"),function t(i){return i===r?(o++,e.consume(i),t):o>=a?(e.exit("codeFencedFenceSequence"),eL(i)?eR(e,u,"whitespace")(i):u(i)):n(i)}(t)):n(t)}function u(r){return null===r||eM(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}},s=0,a=0;return function(t){var o=t;let u=i.events[i.events.length-1];return s=u&&"linePrefix"===u[1].type?u[2].sliceSerialize(u[1],!0).length:0,r=o,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),function t(i){return i===r?(a++,e.consume(i),t):a<3?n(i):(e.exit("codeFencedFenceSequence"),eL(i)?eR(e,l,"whitespace")(i):l(i))}(o)};function l(o){return null===o||eM(o)?(e.exit("codeFencedFence"),i.interrupt?t(o):e.check(tr,c,p)(o)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||eM(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),l(i)):eL(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),eR(e,u,"whitespace")(i)):96===i&&i===r?n(i):(e.consume(i),t)}(o))}function u(t){return null===t||eM(t)?l(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||eM(i)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),l(i)):96===i&&i===r?n(i):(e.consume(i),t)}(t))}function c(t){return e.attempt(o,p,h)(t)}function h(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),d}function d(t){return s>0&&eL(t)?eR(e,f,"linePrefix",s+1)(t):f(t)}function f(t){return null===t||eM(t)?e.check(tr,c,p)(t):(e.enter("codeFlowValue"),function t(n){return null===n||eM(n)?(e.exit("codeFlowValue"),f(n)):(e.consume(n),t)}(t))}function p(n){return e.exit("codeFenced"),t(n)}}},to=document.createElement("i");function ts(e){let t="&"+e+";";to.innerHTML=t;let n=to.textContent;return(59!==n.charCodeAt(n.length-1)||"semi"===e)&&n!==t&&n}let ta={name:"characterReference",tokenize:function(e,t,n){let r,i,o=this,s=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),a};function a(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),l):(e.enter("characterReferenceValue"),r=31,i=eE,u(t))}function l(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,i=eC,u):(e.enter("characterReferenceValue"),r=7,i=eP,u(t))}function u(a){if(59===a&&s){let r=e.exit("characterReferenceValue");return i!==eE||ts(o.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(a),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(a)}return i(a)&&s++<r?(e.consume(a),u):n(a)}}},tl={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return eA(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}},tu={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),eR(e,t,"linePrefix")}}};function tc(e,t,n){let r=[],i=-1;for(;++i<e.length;){let o=e[i].resolveAll;o&&!r.includes(o)&&(t=o(t,n),r.push(o))}return t}let th={name:"labelEnd",resolveAll:function(e){let t=-1,n=[];for(;++t<e.length;){let r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){let e="labelImage"===r.type?4:2;r.type="data",t+=e}}return e.length!==n.length&&eg(e,0,e.length,n),e},resolveTo:function(e,t){let n,r,i,o,s=e.length,a=0;for(;s--;)if(n=e[s][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[s][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(i){if("enter"===e[s][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=s,"labelLink"!==n.type)){a=2;break}}else"labelEnd"===n.type&&(i=s);let l={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},u={type:"label",start:{...e[r][1].start},end:{...e[i][1].end}},c={type:"labelText",start:{...e[r+a+2][1].end},end:{...e[i-2][1].start}};return o=ev(o=[["enter",l,t],["enter",u,t]],e.slice(r+1,r+a+3)),o=ev(o,[["enter",c,t]]),o=ev(o,tc(t.parser.constructs.insideSpan.null,e.slice(r+a+4,i-3),t)),o=ev(o,[["exit",c,t],e[i-2],e[i-1],["exit",u,t]]),o=ev(o,e.slice(i+1)),o=ev(o,[["exit",l,t]]),eg(e,r,e.length,o),e},tokenize:function(e,t,n){let r,i,o=this,s=o.events.length;for(;s--;)if(("labelImage"===o.events[s][1].type||"labelLink"===o.events[s][1].type)&&!o.events[s][1]._balanced){r=o.events[s][1];break}return function(t){return r?r._inactive?c(t):(i=o.parser.defined.includes(e4(o.sliceSerialize({start:r.end,end:o.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),a):n(t)};function a(t){return 40===t?e.attempt(td,u,i?u:c)(t):91===t?e.attempt(tf,u,i?l:c)(t):i?u(t):c(t)}function l(t){return e.attempt(tp,u,c)(t)}function u(e){return t(e)}function c(e){return r._balanced=!0,n(e)}}},td={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return eI(t)?e5(e,i)(t):i(t)}function i(t){return 41===t?u(t):e0(e,o,s,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function o(t){return eI(t)?e5(e,a)(t):u(t)}function s(e){return n(e)}function a(t){return 34===t||39===t||40===t?e2(e,l,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):u(t)}function l(t){return eI(t)?e5(e,u)(t):u(t)}function u(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},tf={tokenize:function(e,t,n){let r=this;return function(t){return e1.call(r,e,i,o,"reference","referenceMarker","referenceString")(t)};function i(e){return r.parser.defined.includes(e4(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function o(e){return n(e)}}},tp={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}},tm={name:"labelStartImage",resolveAll:th.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),i};function i(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),o):n(t)}function o(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};function tg(e){return null===e||eI(e)||ej(e)?1:eD(e)?2:void 0}let tv={name:"attention",resolveAll:function(e,t){let n,r,i,o,s,a,l,u,c=-1;for(;++c<e.length;)if("enter"===e[c][0]&&"attentionSequence"===e[c][1].type&&e[c][1]._close){for(n=c;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[c][1]).charCodeAt(0)){if((e[n][1]._close||e[c][1]._open)&&(e[c][1].end.offset-e[c][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[c][1].end.offset-e[c][1].start.offset)%3))continue;a=e[n][1].end.offset-e[n][1].start.offset>1&&e[c][1].end.offset-e[c][1].start.offset>1?2:1;let h={...e[n][1].end},d={...e[c][1].start};ty(h,-a),ty(d,a),o={type:a>1?"strongSequence":"emphasisSequence",start:h,end:{...e[n][1].end}},s={type:a>1?"strongSequence":"emphasisSequence",start:{...e[c][1].start},end:d},i={type:a>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[c][1].start}},r={type:a>1?"strong":"emphasis",start:{...o.start},end:{...s.end}},e[n][1].end={...o.start},e[c][1].start={...s.end},l=[],e[n][1].end.offset-e[n][1].start.offset&&(l=ev(l,[["enter",e[n][1],t],["exit",e[n][1],t]])),l=ev(l,[["enter",r,t],["enter",o,t],["exit",o,t],["enter",i,t]]),l=ev(l,tc(t.parser.constructs.insideSpan.null,e.slice(n+1,c),t)),l=ev(l,[["exit",i,t],["enter",s,t],["exit",s,t],["exit",r,t]]),e[c][1].end.offset-e[c][1].start.offset?(u=2,l=ev(l,[["enter",e[c][1],t],["exit",e[c][1],t]])):u=0,eg(e,n-1,c-n+3,l),c=n+l.length-u-2;break}}for(c=-1;++c<e.length;)"attentionSequence"===e[c][1].type&&(e[c][1].type="data");return e},tokenize:function(e,t){let n,r=this.parser.constructs.attentionMarkers.null,i=this.previous,o=tg(i);return function(s){return n=s,e.enter("attentionSequence"),function s(a){if(a===n)return e.consume(a),s;let l=e.exit("attentionSequence"),u=tg(a),c=!u||2===u&&o||r.includes(a),h=!o||2===o&&u||r.includes(i);return l._open=!!(42===n?c:c&&(o||!h)),l._close=!!(42===n?h:h&&(u||!c)),t(a)}(s)}}};function ty(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}let tx={name:"labelStartLink",resolveAll:th.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),i};function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}},tb={42:eK,43:eK,45:eK,48:eK,49:eK,50:eK,51:eK,52:eK,53:eK,54:eK,55:eK,56:eK,57:eK,62:eJ},tw={91:{name:"definition",tokenize:function(e,t,n){let r,i=this;return function(t){var r;return e.enter("definition"),r=t,e1.call(i,e,o,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(r)};function o(t){return(r=e4(i.sliceSerialize(i.events[i.events.length-1][1]).slice(1,-1)),58===t)?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),s):n(t)}function s(t){return eI(t)?e5(e,a)(t):a(t)}function a(t){return e0(e,l,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function l(t){return e.attempt(e3,u,u)(t)}function u(t){return eL(t)?eR(e,c,"whitespace")(t):c(t)}function c(o){return null===o||eM(o)?(e.exit("definition"),i.parser.defined.push(r),t(o)):n(o)}}}},tk={[-2]:e6,[-1]:e6,32:e6},tE={35:{name:"headingAtx",resolve:function(e,t){let n,r,i=e.length-2,o=3;return"whitespace"===e[3][1].type&&(o+=2),i-2>o&&"whitespace"===e[i][1].type&&(i-=2),"atxHeadingSequence"===e[i][1].type&&(o===i-1||i-4>o&&"whitespace"===e[i-2][1].type)&&(i-=o+1===i?2:4),i>o&&(n={type:"atxHeadingText",start:e[o][1].start,end:e[i][1].end},r={type:"chunkText",start:e[o][1].start,end:e[i][1].end,contentType:"text"},eg(e,o,i-o+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e},tokenize:function(e,t,n){let r=0;return function(i){var o;return e.enter("atxHeading"),o=i,e.enter("atxHeadingSequence"),function i(o){return 35===o&&r++<6?(e.consume(o),i):null===o||eI(o)?(e.exit("atxHeadingSequence"),function n(r){return 35===r?(e.enter("atxHeadingSequence"),function t(r){return 35===r?(e.consume(r),t):(e.exit("atxHeadingSequence"),n(r))}(r)):null===r||eM(r)?(e.exit("atxHeading"),t(r)):eL(r)?eR(e,n,"whitespace")(r):(e.enter("atxHeadingText"),function t(r){return null===r||35===r||eI(r)?(e.exit("atxHeadingText"),n(r)):(e.consume(r),t)}(r))}(o)):n(o)}(o)}}},42:eX,45:[e9,eX],60:{concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e},tokenize:function(e,t,n){let r,i,o,s,a,l=this;return function(t){var n;return n=t,e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(n),u};function u(s){return 33===s?(e.consume(s),c):47===s?(e.consume(s),i=!0,f):63===s?(e.consume(s),r=3,l.interrupt?t:D):ek(s)?(e.consume(s),o=String.fromCharCode(s),p):n(s)}function c(i){return 45===i?(e.consume(i),r=2,h):91===i?(e.consume(i),r=5,s=0,d):ek(i)?(e.consume(i),r=4,l.interrupt?t:D):n(i)}function h(r){return 45===r?(e.consume(r),l.interrupt?t:D):n(r)}function d(r){let i="CDATA[";return r===i.charCodeAt(s++)?(e.consume(r),s===i.length)?l.interrupt?t:S:d:n(r)}function f(t){return ek(t)?(e.consume(t),o=String.fromCharCode(t),p):n(t)}function p(s){if(null===s||47===s||62===s||eI(s)){let a=47===s,u=o.toLowerCase();return!a&&!i&&te.includes(u)?(r=1,l.interrupt?t(s):S(s)):e7.includes(o.toLowerCase())?(r=6,a)?(e.consume(s),m):l.interrupt?t(s):S(s):(r=7,l.interrupt&&!l.parser.lazy[l.now().line]?n(s):i?function t(n){return eL(n)?(e.consume(n),t):k(n)}(s):g(s))}return 45===s||eE(s)?(e.consume(s),o+=String.fromCharCode(s),p):n(s)}function m(r){return 62===r?(e.consume(r),l.interrupt?t:S):n(r)}function g(t){return 47===t?(e.consume(t),k):58===t||95===t||ek(t)?(e.consume(t),v):eL(t)?(e.consume(t),g):k(t)}function v(t){return 45===t||46===t||58===t||95===t||eE(t)?(e.consume(t),v):y(t)}function y(t){return 61===t?(e.consume(t),x):eL(t)?(e.consume(t),y):g(t)}function x(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),a=t,b):eL(t)?(e.consume(t),x):function t(n){return null===n||34===n||39===n||47===n||60===n||61===n||62===n||96===n||eI(n)?y(n):(e.consume(n),t)}(t)}function b(t){return t===a?(e.consume(t),a=null,w):null===t||eM(t)?n(t):(e.consume(t),b)}function w(e){return 47===e||62===e||eL(e)?g(e):n(e)}function k(t){return 62===t?(e.consume(t),E):n(t)}function E(t){return null===t||eM(t)?S(t):eL(t)?(e.consume(t),E):n(t)}function S(t){return 45===t&&2===r?(e.consume(t),A):60===t&&1===r?(e.consume(t),M):62===t&&4===r?(e.consume(t),j):63===t&&3===r?(e.consume(t),D):93===t&&5===r?(e.consume(t),L):eM(t)&&(6===r||7===r)?(e.exit("htmlFlowData"),e.check(tt,_,T)(t)):null===t||eM(t)?(e.exit("htmlFlowData"),T(t)):(e.consume(t),S)}function T(t){return e.check(tn,P,_)(t)}function P(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),C}function C(t){return null===t||eM(t)?T(t):(e.enter("htmlFlowData"),S(t))}function A(t){return 45===t?(e.consume(t),D):S(t)}function M(t){return 47===t?(e.consume(t),o="",I):S(t)}function I(t){if(62===t){let n=o.toLowerCase();return te.includes(n)?(e.consume(t),j):S(t)}return ek(t)&&o.length<8?(e.consume(t),o+=String.fromCharCode(t),I):S(t)}function L(t){return 93===t?(e.consume(t),D):S(t)}function D(t){return 62===t?(e.consume(t),j):45===t&&2===r?(e.consume(t),D):S(t)}function j(t){return null===t||eM(t)?(e.exit("htmlFlowData"),_(t)):(e.consume(t),j)}function _(n){return e.exit("htmlFlow"),t(n)}}},61:e9,95:eX,96:ti,126:ti},tS={38:ta,92:tl},tT={[-5]:tu,[-4]:tu,[-3]:tu,33:tm,38:ta,42:tv,60:[{name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),i};function i(t){return ek(t)?(e.consume(t),o):64===t?n(t):a(t)}function o(t){return 43===t||45===t||46===t||eE(t)?(r=1,function t(n){return 58===n?(e.consume(n),r=0,s):(43===n||45===n||46===n||eE(n))&&r++<32?(e.consume(n),t):(r=0,a(n))}(t)):a(t)}function s(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||eT(r)?n(r):(e.consume(r),s)}function a(t){return 64===t?(e.consume(t),l):eS(t)?(e.consume(t),a):n(t)}function l(i){return eE(i)?function i(o){return 46===o?(e.consume(o),r=0,l):62===o?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(o),e.exit("autolinkMarker"),e.exit("autolink"),t):function t(o){if((45===o||eE(o))&&r++<63){let n=45===o?t:i;return e.consume(o),n}return n(o)}(o)}(i):n(i)}}},{name:"htmlText",tokenize:function(e,t,n){let r,i,o,s=this;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),a};function a(t){return 33===t?(e.consume(t),l):47===t?(e.consume(t),b):63===t?(e.consume(t),y):ek(t)?(e.consume(t),k):n(t)}function l(t){return 45===t?(e.consume(t),u):91===t?(e.consume(t),i=0,f):ek(t)?(e.consume(t),v):n(t)}function u(t){return 45===t?(e.consume(t),d):n(t)}function c(t){return null===t?n(t):45===t?(e.consume(t),h):eM(t)?(o=c,I(t)):(e.consume(t),c)}function h(t){return 45===t?(e.consume(t),d):c(t)}function d(e){return 62===e?M(e):45===e?h(e):c(e)}function f(t){let r="CDATA[";return t===r.charCodeAt(i++)?(e.consume(t),i===r.length?p:f):n(t)}function p(t){return null===t?n(t):93===t?(e.consume(t),m):eM(t)?(o=p,I(t)):(e.consume(t),p)}function m(t){return 93===t?(e.consume(t),g):p(t)}function g(t){return 62===t?M(t):93===t?(e.consume(t),g):p(t)}function v(t){return null===t||62===t?M(t):eM(t)?(o=v,I(t)):(e.consume(t),v)}function y(t){return null===t?n(t):63===t?(e.consume(t),x):eM(t)?(o=y,I(t)):(e.consume(t),y)}function x(e){return 62===e?M(e):y(e)}function b(t){return ek(t)?(e.consume(t),w):n(t)}function w(t){return 45===t||eE(t)?(e.consume(t),w):function t(n){return eM(n)?(o=t,I(n)):eL(n)?(e.consume(n),t):M(n)}(t)}function k(t){return 45===t||eE(t)?(e.consume(t),k):47===t||62===t||eI(t)?E(t):n(t)}function E(t){return 47===t?(e.consume(t),M):58===t||95===t||ek(t)?(e.consume(t),S):eM(t)?(o=E,I(t)):eL(t)?(e.consume(t),E):M(t)}function S(t){return 45===t||46===t||58===t||95===t||eE(t)?(e.consume(t),S):function t(n){return 61===n?(e.consume(n),T):eM(n)?(o=t,I(n)):eL(n)?(e.consume(n),t):E(n)}(t)}function T(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),r=t,P):eM(t)?(o=T,I(t)):eL(t)?(e.consume(t),T):(e.consume(t),C)}function P(t){return t===r?(e.consume(t),r=void 0,A):null===t?n(t):eM(t)?(o=P,I(t)):(e.consume(t),P)}function C(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||eI(t)?E(t):(e.consume(t),C)}function A(e){return 47===e||62===e||eI(e)?E(e):n(e)}function M(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function I(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),L}function L(t){return eL(t)?eR(e,D,"linePrefix",s.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):D(t)}function D(t){return e.enter("htmlTextData"),o(t)}}}],91:tx,92:[{name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return eM(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}},tl],93:th,95:tv,96:{name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,i=3;if(("lineEnding"===e[3][1].type||"space"===e[i][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(t=i;++t<r;)if("codeTextData"===e[t][1].type){e[i][1].type="codeTextPadding",e[r][1].type="codeTextPadding",i+=2,r-=2;break}}for(t=i-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):(t===r||"lineEnding"===e[t][1].type)&&(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,i,o=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),function t(n){return 96===n?(e.consume(n),o++,t):(e.exit("codeTextSequence"),s(n))}(t)};function s(l){return null===l?n(l):32===l?(e.enter("space"),e.consume(l),e.exit("space"),s):96===l?(i=e.enter("codeTextSequence"),r=0,function n(s){return 96===s?(e.consume(s),r++,n):r===o?(e.exit("codeTextSequence"),e.exit("codeText"),t(s)):(i.type="codeTextData",a(s))}(l)):eM(l)?(e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),s):(e.enter("codeTextData"),a(l))}function a(t){return null===t||32===t||96===t||eM(t)?(e.exit("codeTextData"),s(t)):(e.consume(t),a)}}}},tP={null:[tv,eH]},tC={null:[42,95]},tA={null:[]},tM=/[\0\t\n\r]/g;function tI(e,t){let n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(65535&n)==65535||(65535&n)==65534||n>1114111?"�":String.fromCodePoint(n)}let tL=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function tD(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){let e=n.charCodeAt(1),t=120===e||88===e;return tI(n.slice(t?2:1),t?16:10)}return ts(n)||e}let tj={}.hasOwnProperty;function t_(e){return{line:e.line,column:e.column,offset:e.offset}}function tR(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+Y({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+Y({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+Y({start:t.start,end:t.end})+") is still open")}function tO(e){let t=this;t.parser=function(n){var r,o;let s,a,l,u;return"string"!=typeof(r={...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})&&(o=r,r=void 0),(function(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(v),autolinkProtocol:u,autolinkEmail:u,atxHeading:r(p),blockQuote:r(function(){return{type:"blockquote",children:[]}}),characterEscape:u,characterReference:u,codeFenced:r(f),codeFencedFenceInfo:i,codeFencedFenceMeta:i,codeIndented:r(f,i),codeText:r(function(){return{type:"inlineCode",value:""}},i),codeTextData:u,data:u,codeFlowValue:u,definition:r(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:i,definitionLabelString:i,definitionTitleString:i,emphasis:r(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:r(m),hardBreakTrailing:r(m),htmlFlow:r(g,i),htmlFlowData:u,htmlText:r(g,i),htmlTextData:u,image:r(function(){return{type:"image",title:null,url:"",alt:null}}),label:i,link:r(v),listItem:r(function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}),listItemValue:function(e){this.data.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0)},listOrdered:r(y,function(){this.data.expectingFirstListItemValue=!0}),listUnordered:r(y),paragraph:r(function(){return{type:"paragraph",children:[]}}),reference:function(){this.data.referenceType="collapsed"},referenceString:i,resourceDestinationString:i,resourceTitleString:i,setextHeading:r(p),strong:r(function(){return{type:"strong",children:[]}}),thematicBreak:r(function(){return{type:"thematicBreak"}})},exit:{atxHeading:s(),atxHeadingSequence:function(e){let t=this.stack[this.stack.length-1];t.depth||(t.depth=this.sliceSerialize(e).length)},autolink:s(),autolinkEmail:function(e){c.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){c.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:s(),characterEscapeValue:c,characterReferenceMarkerHexadecimal:d,characterReferenceMarkerNumeric:d,characterReferenceValue:function(e){let t,n=this.sliceSerialize(e),r=this.data.characterReferenceType;r?(t=tI(n,"characterReferenceMarkerNumeric"===r?10:16),this.data.characterReferenceType=void 0):t=ts(n);let i=this.stack[this.stack.length-1];i.value+=t},characterReference:function(e){this.stack.pop().position.end=t_(e.end)},codeFenced:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){let e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){let e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:c,codeIndented:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}),codeText:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),codeTextData:c,data:c,definition:s(),definitionDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=e4(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:s(),hardBreakEscape:s(h),hardBreakTrailing:s(h),htmlFlow:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlFlowData:c,htmlText:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlTextData:c,image:s(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),label:function(){let e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];this.data.inReference=!0,"link"===n.type?n.children=e.children:n.alt=t},labelText:function(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=t.replace(tL,tD),n.identifier=e4(t).toLowerCase()},lineEnding:function(e){let n=this.stack[this.stack.length-1];if(this.data.atHardBreak){n.children[n.children.length-1].position.end=t_(e.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(u.call(this,e),c.call(this,e))},link:s(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),listItem:s(),listOrdered:s(),listUnordered:s(),paragraph:s(),referenceString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=e4(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){this.data.inReference=void 0},setextHeading:s(function(){this.data.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:s(),thematicBreak:s()}};!function e(t,n){let r=-1;for(;++r<n.length;){let i=n[r];Array.isArray(i)?e(t,i):function(e,t){let n;for(n in t)if(tj.call(t,n))switch(n){case"canContainEols":{let r=t[n];r&&e[n].push(...r);break}case"transforms":{let r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{let r=t[n];r&&Object.assign(e[n],r)}}}(t,i)}}(t,(e||{}).mdastExtensions||[]);let n={};return function(e){let r={type:"root",children:[]},s={stack:[r],tokenStack:[],config:t,enter:o,exit:a,buffer:i,resume:l,data:n},u=[],c=-1;for(;++c<e.length;)("listOrdered"===e[c][1].type||"listUnordered"===e[c][1].type)&&("enter"===e[c][0]?u.push(c):c=function(e,t,n){let r,i,o,s,a=t-1,l=-1,u=!1;for(;++a<=n;){let t=e[a];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?l++:l--,s=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||s||l||o||(o=a),s=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:s=void 0}if(!l&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===l&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let s=a;for(i=void 0;s--;){let t=e[s];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;i&&(e[i][1].type="lineEndingBlank",u=!0),t[1].type="lineEnding",i=s}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}o&&(!i||o<i)&&(r._spread=!0),r.end=Object.assign({},i?e[i][1].start:t[1].end),e.splice(i||a,0,["exit",r,t[2]]),a++,n++}if("listItemPrefix"===t[1].type){let i={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=i,e.splice(a,0,["enter",i,t[2]]),a++,n++,o=void 0,s=!0}}}return e[t][1]._spread=u,n}(e,u.pop(),c));for(c=-1;++c<e.length;){let n=t[e[c][0]];tj.call(n,e[c][1].type)&&n[e[c][1].type].call(Object.assign({sliceSerialize:e[c][2].sliceSerialize},s),e[c][1])}if(s.tokenStack.length>0){let e=s.tokenStack[s.tokenStack.length-1];(e[1]||tR).call(s,void 0,e[0])}for(r.position={start:t_(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:t_(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},c=-1;++c<t.transforms.length;)r=t.transforms[c](r)||r;return r};function r(e,t){return function(n){o.call(this,e(n),n),t&&t.call(this,n)}}function i(){this.stack.push({type:"fragment",children:[]})}function o(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:t_(t.start),end:void 0}}function s(e){return function(t){e&&e.call(this,t),a.call(this,t)}}function a(e,t){let n=this.stack.pop(),r=this.tokenStack.pop();if(r)r[0].type!==e.type&&(t?t.call(this,e,r[0]):(r[1]||tR).call(this,e,r[0]));else throw Error("Cannot close `"+e.type+"` ("+Y({start:e.start,end:e.end})+"): it’s not open");n.position.end=t_(e.end)}function l(){var e;return ep(this.stack.pop(),"boolean"!=typeof ef.includeImageAlt||ef.includeImageAlt,"boolean"!=typeof ef.includeHtml||ef.includeHtml)}function u(e){let t=this.stack[this.stack.length-1].children,n=t[t.length-1];n&&"text"===n.type||((n={type:"text",value:""}).position={start:t_(e.start),end:void 0},t.push(n)),this.stack.push(n)}function c(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=t_(e.end)}function h(){this.data.atHardBreak=!0}function d(e){this.data.characterReferenceType=e.type}function f(){return{type:"code",lang:null,meta:null,value:""}}function p(){return{type:"heading",depth:0,children:[]}}function m(){return{type:"break"}}function g(){return{type:"html",value:""}}function v(){return{type:"link",title:null,url:"",children:[]}}function y(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}})(o)(function(e){for(;!eb(e););return e}((function(e){let t={constructs:function(e){let t={},n=-1;for(;++n<e.length;)!function(e,t){let n;for(n in t){let r,i=(ew.call(e,n)?e[n]:void 0)||(e[n]={}),o=t[n];if(o)for(r in o){ew.call(i,r)||(i[r]=[]);let e=o[r];!function(e,t){let n=-1,r=[];for(;++n<t.length;)("after"===t[n].add?e:r).push(t[n]);eg(e,0,0,r)}(i[r],Array.isArray(e)?e:e?[e]:[])}}}(t,e[n]);return t}([i,...(e||{}).extensions||[]]),content:n(eO),defined:[],document:n(eN),flow:n(eU),lazy:{},string:n(eW),text:n(eq)};return t;function n(e){return function(n){return function(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},i={},o=[],s=[],a=[],l={attempt:p(function(e,t){m(e,t.from)}),check:p(f),consume:function(e){eM(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,g()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===s[r._index].length&&(r._bufferIndex=-1,r._index++)),u.previous=e},enter:function(e,t){let n=t||{};return n.type=e,n.start=d(),u.events.push(["enter",n,u]),a.push(n),n},exit:function(e){let t=a.pop();return t.end=d(),u.events.push(["exit",t,u]),t},interrupt:p(f,{interrupt:!0})},u={code:null,containerState:{},defineSkip:function(e){i[e.line]=e.column,g()},events:[],now:d,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n,r=-1,i=[];for(;++r<e.length;){let o,s=e[r];if("string"==typeof s)o=s;else switch(s){case -5:o="\r";break;case -4:o="\n";break;case -3:o="\r\n";break;case -2:o=t?" ":"	";break;case -1:if(!t&&n)continue;o=" ";break;default:o=String.fromCharCode(s)}n=-2===s,i.push(o)}return i.join("")}(h(e),t)},sliceStream:h,write:function(e){return(s=ev(s,e),function(){let e;for(;r._index<s.length;){let n=s[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;){var t;t=n.charCodeAt(r._bufferIndex),c=c(t)}else c=c(n)}}(),null!==s[s.length-1])?[]:(m(t,0),u.events=tc(o,u.events,u),u.events)}},c=t.tokenize.call(u,l);return t.resolveAll&&o.push(t),u;function h(e){return function(e,t){let n,r=t.start._index,i=t.start._bufferIndex,o=t.end._index,s=t.end._bufferIndex;if(r===o)n=[e[r].slice(i,s)];else{if(n=e.slice(r,o),i>-1){let e=n[0];"string"==typeof e?n[0]=e.slice(i):n.shift()}s>0&&n.push(e[o].slice(0,s))}return n}(s,e)}function d(){let{_bufferIndex:e,_index:t,line:n,column:i,offset:o}=r;return{_bufferIndex:e,_index:t,line:n,column:i,offset:o}}function f(e,t){t.restore()}function p(e,t){return function(n,i,o){var s;let c,h,f,p;return Array.isArray(n)?m(n):"tokenize"in n?m([n]):(s=n,function(e){let t=null!==e&&s[e],n=null!==e&&s.null;return m([...Array.isArray(t)?t:t?[t]:[],...Array.isArray(n)?n:n?[n]:[]])(e)});function m(e){return(c=e,h=0,0===e.length)?o:v(e[h])}function v(e){return function(n){return(p=function(){let e=d(),t=u.previous,n=u.currentConstruct,i=u.events.length,o=Array.from(a);return{from:i,restore:function(){r=e,u.previous=t,u.currentConstruct=n,u.events.length=i,a=o,g()}}}(),f=e,e.partial||(u.currentConstruct=e),e.name&&u.parser.constructs.disable.null.includes(e.name))?x(n):e.tokenize.call(t?Object.assign(Object.create(u),t):u,l,y,x)(n)}}function y(t){return e(f,p),i}function x(e){return(p.restore(),++h<c.length)?v(c[h]):o}}}function m(e,t){e.resolveAll&&!o.includes(e)&&o.push(e),e.resolve&&eg(u.events,t,u.events.length-t,e.resolve(u.events.slice(t),u)),e.resolveTo&&(u.events=e.resolveTo(u.events,u))}function g(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}(t,e,n)}}})(o).document().write((a=1,l="",u=!0,function(e,t,n){let r,i,o,c,h,d=[];for(e=l+("string"==typeof e?e.toString():new TextDecoder(t||void 0).decode(e)),o=0,l="",u&&(65279===e.charCodeAt(0)&&o++,u=void 0);o<e.length;){if(tM.lastIndex=o,c=(r=tM.exec(e))&&void 0!==r.index?r.index:e.length,h=e.charCodeAt(c),!r){l=e.slice(o);break}if(10===h&&o===c&&s)d.push(-3),s=void 0;else switch(s&&(d.push(-5),s=void 0),o<c&&(d.push(e.slice(o,c)),a+=c-o),h){case 0:d.push(65533),a++;break;case 9:for(i=4*Math.ceil(a/4),d.push(-2);a++<i;)d.push(-1);break;case 10:d.push(-4),a=1;break;default:s=!0,a=1}o=c+1}return n&&(s&&d.push(-5),l&&d.push(l),d.push(null)),d})(n,r,!0))))}}let tN="object"==typeof self?self:globalThis,tV=(e,t)=>{let n=(t,n)=>(e.set(n,t),t),r=i=>{if(e.has(i))return e.get(i);let[o,s]=t[i];switch(o){case 0:case -1:return n(s,i);case 1:{let e=n([],i);for(let t of s)e.push(r(t));return e}case 2:{let e=n({},i);for(let[t,n]of s)e[r(t)]=r(n);return e}case 3:return n(new Date(s),i);case 4:{let{source:e,flags:t}=s;return n(new RegExp(e,t),i)}case 5:{let e=n(new Map,i);for(let[t,n]of s)e.set(r(t),r(n));return e}case 6:{let e=n(new Set,i);for(let t of s)e.add(r(t));return e}case 7:{let{name:e,message:t}=s;return n(new tN[e](t),i)}case 8:return n(BigInt(s),i);case"BigInt":return n(Object(BigInt(s)),i);case"ArrayBuffer":return n(new Uint8Array(s).buffer,s);case"DataView":{let{buffer:e}=new Uint8Array(s);return n(new DataView(e),s)}}return n(new tN[o](s),i)};return r},tF=e=>tV(new Map,e)(0),{toString:tB}={},{keys:tz}=Object,tU=e=>{let t=typeof e;if("object"!==t||!e)return[0,t];let n=tB.call(e).slice(8,-1);switch(n){case"Array":return[1,""];case"Object":return[2,""];case"Date":return[3,""];case"RegExp":return[4,""];case"Map":return[5,""];case"Set":return[6,""];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},tH=([e,t])=>0===e&&("function"===t||"symbol"===t),tW=(e,t,n,r)=>{let i=(e,t)=>{let i=r.push(e)-1;return n.set(t,i),i},o=r=>{if(n.has(r))return n.get(r);let[s,a]=tU(r);switch(s){case 0:{let t=r;switch(a){case"bigint":s=8,t=r.toString();break;case"function":case"symbol":if(e)throw TypeError("unable to serialize "+a);t=null;break;case"undefined":return i([-1],r)}return i([s,t],r)}case 1:{if(a){let e=r;return"DataView"===a?e=new Uint8Array(r.buffer):"ArrayBuffer"===a&&(e=new Uint8Array(r)),i([a,[...e]],r)}let e=[],t=i([s,e],r);for(let t of r)e.push(o(t));return t}case 2:{if(a)switch(a){case"BigInt":return i([a,r.toString()],r);case"Boolean":case"Number":case"String":return i([a,r.valueOf()],r)}if(t&&"toJSON"in r)return o(r.toJSON());let n=[],l=i([s,n],r);for(let t of tz(r))(e||!tH(tU(r[t])))&&n.push([o(t),o(r[t])]);return l}case 3:return i([s,r.toISOString()],r);case 4:{let{source:e,flags:t}=r;return i([s,{source:e,flags:t}],r)}case 5:{let t=[],n=i([s,t],r);for(let[n,i]of r)(e||!(tH(tU(n))||tH(tU(i))))&&t.push([o(n),o(i)]);return n}case 6:{let t=[],n=i([s,t],r);for(let n of r)(e||!tH(tU(n)))&&t.push(o(n));return n}}let{message:l}=r;return i([s,{name:a,message:l}],r)};return o},tq=(e,{json:t,lossy:n}={})=>{let r=[];return tW(!(t||n),!!t,new Map,r)(e),r},t$="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?tF(tq(e,t)):structuredClone(e):(e,t)=>tF(tq(e,t));function tY(e){let t=[],n=-1,r=0,i=0;for(;++n<e.length;){let o=e.charCodeAt(n),s="";if(37===o&&eE(e.charCodeAt(n+1))&&eE(e.charCodeAt(n+2)))i=2;else if(o<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(o))||(s=String.fromCharCode(o));else if(o>55295&&o<57344){let t=e.charCodeAt(n+1);o<56320&&t>56319&&t<57344?(s=String.fromCharCode(o,t),i=1):s="�"}else s=String.fromCharCode(o);s&&(t.push(e.slice(r,n),encodeURIComponent(s)),r=n+i+1,s=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function tG(e,t){let n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function tX(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}let tK=function(e){var t,n;if(null==e)return tQ;if("function"==typeof e)return tZ(e);if("object"==typeof e){return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=tK(e[n]);return tZ(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):(t=e,tZ(function(e){let n;for(n in t)if(e[n]!==t[n])return!1;return!0}))}if("string"==typeof e){return n=e,tZ(function(e){return e&&e.type===n})}throw Error("Expected function, string, or object as test")};function tZ(e){return function(t,n,r){return!!(function(e){return null!==e&&"object"==typeof e&&"type"in e}(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function tQ(){return!0}let tJ=[];function t0(e,t,n,r){let i,o,s,a;"function"==typeof t&&"function"!=typeof n?(o=void 0,s=t,i=n):(o=t,s=n,i=r);var l=o,u=function(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return s(e,r,n)},c=i;"function"==typeof l&&"function"!=typeof u?(c=u,u=l):a=l;let h=tK(a),d=c?-1:1;(function e(t,n,r){let i=t&&"object"==typeof t?t:{};if("string"==typeof i.type){let e="string"==typeof i.tagName?i.tagName:"string"==typeof i.name?i.name:void 0;Object.defineProperty(o,"name",{value:"node ("+t.type+(e?"<"+e+">":"")+")"})}return o;function o(){var i;let o,s,a,f=tJ;if((!l||h(t,n,r[r.length-1]||void 0))&&!1===(f=Array.isArray(i=u(t,r))?i:"number"==typeof i?[!0,i]:null==i?tJ:[i])[0])return f;if("children"in t&&t.children&&t.children&&"skip"!==f[0])for(s=(c?t.children.length:-1)+d,a=r.concat(t);s>-1&&s<t.children.length;){if(!1===(o=e(t.children[s],s,a)())[0])return o;s="number"==typeof o[1]?o[1]:s+d}return f}})(e,void 0,[])()}function t1(e,t){let n=t.referenceType,r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];let i=e.all(t),o=i[0];o&&"text"===o.type?o.value="["+o.value:i.unshift({type:"text",value:"["});let s=i[i.length-1];return s&&"text"===s.type?s.value+=r:i.push({type:"text",value:r}),i}function t2(e){let t=e.spread;return null==t?e.children.length>1:t}function t5(e,t,n){let r=0,i=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(i-1);for(;9===t||32===t;)i--,t=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}let t4={blockquote:function(e,t){let n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){let n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){let n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(i.data={meta:t.meta}),e.patch(t,i),i={type:"element",tagName:"pre",properties:{},children:[i=e.applyData(t,i)]},e.patch(t,i),i},delete:function(e,t){let n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){let n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){let n,r="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",i=String(t.identifier).toUpperCase(),o=tY(i.toLowerCase()),s=e.footnoteOrder.indexOf(i),a=e.footnoteCounts.get(i);void 0===a?(a=0,e.footnoteOrder.push(i),n=e.footnoteOrder.length):n=s+1,a+=1,e.footnoteCounts.set(i,a);let l={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+o,id:r+"fnref-"+o+(a>1?"-"+a:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(n)}]};e.patch(t,l);let u={type:"element",tagName:"sup",properties:{},children:[l]};return e.patch(t,u),e.applyData(t,u)},heading:function(e,t){let n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){let n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return t1(e,t);let i={src:tY(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(i.title=r.title);let o={type:"element",tagName:"img",properties:i,children:[]};return e.patch(t,o),e.applyData(t,o)},image:function(e,t){let n={src:tY(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){let n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);let r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return t1(e,t);let i={href:tY(r.url||"")};null!==r.title&&void 0!==r.title&&(i.title=r.title);let o={type:"element",tagName:"a",properties:i,children:e.all(t)};return e.patch(t,o),e.applyData(t,o)},link:function(e,t){let n={href:tY(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){let r=e.all(t),i=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;let n=e.children,r=-1;for(;!t&&++r<n.length;)t=t2(n[r])}return t}(n):t2(t),o={},s=[];if("boolean"==typeof t.checked){let e,n=r[0];n&&"element"===n.type&&"p"===n.tagName?e=n:(e={type:"element",tagName:"p",properties:{},children:[]},r.unshift(e)),e.children.length>0&&e.children.unshift({type:"text",value:" "}),e.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),o.className=["task-list-item"]}let a=-1;for(;++a<r.length;){let e=r[a];(i||0!==a||"element"!==e.type||"p"!==e.tagName)&&s.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||i?s.push(e):s.push(...e.children)}let l=r[r.length-1];l&&(i||"element"!==l.type||"p"!==l.tagName)&&s.push({type:"text",value:"\n"});let u={type:"element",tagName:"li",properties:o,children:s};return e.patch(t,u),e.applyData(t,u)},list:function(e,t){let n={},r=e.all(t),i=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++i<r.length;){let e=r[i];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let o={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,o),e.applyData(t,o)},paragraph:function(e,t){let n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){let n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){let n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){let n=e.all(t),r=n.shift(),i=[];if(r){let n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),i.push(n)}if(n.length>0){let r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},o=q(t.children[1]),s=W(t.children[t.children.length-1]);o&&s&&(r.position={start:o,end:s}),i.push(r)}let o={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,o),e.applyData(t,o)},tableCell:function(e,t){let n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){let r=n?n.children:void 0,i=0===(r?r.indexOf(t):1)?"th":"td",o=n&&"table"===n.type?n.align:void 0,s=o?o.length:t.children.length,a=-1,l=[];for(;++a<s;){let n=t.children[a],r={},s=o?o[a]:void 0;s&&(r.align=s);let u={type:"element",tagName:i,properties:r,children:[]};n&&(u.children=e.all(n),e.patch(n,u),u=e.applyData(n,u)),l.push(u)}let u={type:"element",tagName:"tr",properties:{},children:e.wrap(l,!0)};return e.patch(t,u),e.applyData(t,u)},text:function(e,t){let n={type:"text",value:function(e){let t=String(e),n=/\r?\n|\r/g,r=n.exec(t),i=0,o=[];for(;r;)o.push(t5(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return o.push(t5(t.slice(i),i>0,!1)),o.join("")}(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){let n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:t3,yaml:t3,definition:t3,footnoteDefinition:t3};function t3(){}let t6={}.hasOwnProperty,t8={};function t9(e,t){e.position&&(t.position=function(e){let t=q(e),n=W(e);if(t&&n)return{start:t,end:n}}(e))}function t7(e,t){let n=t;if(e&&e.data){let t=e.data.hName,r=e.data.hChildren,i=e.data.hProperties;"string"==typeof t&&("element"===n.type?n.tagName=t:n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}),"element"===n.type&&i&&Object.assign(n.properties,t$(i)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function ne(e,t){let n=[],r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function nt(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function nn(e,t){let n=function(e,t){let n=t||t8,r=new Map,i=new Map,o={all:function(e){let t=[];if("children"in e){let n=e.children,r=-1;for(;++r<n.length;){let i=o.one(n[r],e);if(i){if(r&&"break"===n[r-1].type&&(Array.isArray(i)||"text"!==i.type||(i.value=nt(i.value)),!Array.isArray(i)&&"element"===i.type)){let e=i.children[0];e&&"text"===e.type&&(e.value=nt(e.value))}Array.isArray(i)?t.push(...i):t.push(i)}}}return t},applyData:t7,definitionById:r,footnoteById:i,footnoteCounts:new Map,footnoteOrder:[],handlers:{...t4,...n.handlers},one:function(e,t){let n=e.type,r=o.handlers[n];if(t6.call(o.handlers,n)&&r)return r(o,e,t);if(o.options.passThrough&&o.options.passThrough.includes(n)){if("children"in e){let{children:t,...n}=e,r=t$(n);return r.children=o.all(e),r}return t$(e)}return(o.options.unknownHandler||function(e,t){let n=t.data||{},r="value"in t&&!(t6.call(n,"hProperties")||t6.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)})(o,e,t)},options:n,patch:t9,wrap:ne};return t0(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){let t="definition"===e.type?r:i,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),o}(e,t),r=n.one(e,void 0),i=function(e){let t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||tG,r=e.options.footnoteBackLabel||tX,i=e.options.footnoteLabel||"Footnotes",o=e.options.footnoteLabelTagName||"h2",s=e.options.footnoteLabelProperties||{className:["sr-only"]},a=[],l=-1;for(;++l<e.footnoteOrder.length;){let i=e.footnoteById.get(e.footnoteOrder[l]);if(!i)continue;let o=e.all(i),s=String(i.identifier).toUpperCase(),u=tY(s.toLowerCase()),c=0,h=[],d=e.footnoteCounts.get(s);for(;void 0!==d&&++c<=d;){h.length>0&&h.push({type:"text",value:" "});let e="string"==typeof n?n:n(l,c);"string"==typeof e&&(e={type:"text",value:e}),h.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+u+(c>1?"-"+c:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(l,c),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}let f=o[o.length-1];if(f&&"element"===f.type&&"p"===f.tagName){let e=f.children[f.children.length-1];e&&"text"===e.type?e.value+=" ":f.children.push({type:"text",value:" "}),f.children.push(...h)}else o.push(...h);let p={type:"element",tagName:"li",properties:{id:t+"fn-"+u},children:e.wrap(o,!0)};e.patch(i,p),a.push(p)}if(0!==a.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:o,properties:{...t$(s),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(a,!0)},{type:"text",value:"\n"}]}}(n),o=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return i&&o.children.push({type:"text",value:"\n"},i),o}function nr(e,t){return e&&"run"in e?async function(n,r){let i=nn(n,{file:r,...t});await e.run(i,r)}:function(n,r){return nn(n,{file:r,...e||t})}}function ni(e){if(e)throw e}var no=n(53360);function ns(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}let na={basename:function(e,t){let n;if(void 0!==t&&"string"!=typeof t)throw TypeError('"ext" argument must be a string');nl(e);let r=0,i=-1,o=e.length;if(void 0===t||0===t.length||t.length>e.length){for(;o--;)if(47===e.codePointAt(o)){if(n){r=o+1;break}}else i<0&&(n=!0,i=o+1);return i<0?"":e.slice(r,i)}if(t===e)return"";let s=-1,a=t.length-1;for(;o--;)if(47===e.codePointAt(o)){if(n){r=o+1;break}}else s<0&&(n=!0,s=o+1),a>-1&&(e.codePointAt(o)===t.codePointAt(a--)?a<0&&(i=o):(a=-1,i=s));return r===i?i=s:i<0&&(i=e.length),e.slice(r,i)},dirname:function(e){let t;if(nl(e),0===e.length)return".";let n=-1,r=e.length;for(;--r;)if(47===e.codePointAt(r)){if(t){n=r;break}}else t||(t=!0);return n<0?47===e.codePointAt(0)?"/":".":1===n&&47===e.codePointAt(0)?"//":e.slice(0,n)},extname:function(e){let t;nl(e);let n=e.length,r=-1,i=0,o=-1,s=0;for(;n--;){let a=e.codePointAt(n);if(47===a){if(t){i=n+1;break}continue}r<0&&(t=!0,r=n+1),46===a?o<0?o=n:1!==s&&(s=1):o>-1&&(s=-1)}return o<0||r<0||0===s||1===s&&o===r-1&&o===i+1?"":e.slice(o,r)},join:function(...e){let t,n=-1;for(;++n<e.length;)nl(e[n]),e[n]&&(t=void 0===t?e[n]:t+"/"+e[n]);return void 0===t?".":function(e){nl(e);let t=47===e.codePointAt(0),n=function(e,t){let n,r,i="",o=0,s=-1,a=0,l=-1;for(;++l<=e.length;){if(l<e.length)n=e.codePointAt(l);else if(47===n)break;else n=47;if(47===n){if(s===l-1||1===a);else if(s!==l-1&&2===a){if(i.length<2||2!==o||46!==i.codePointAt(i.length-1)||46!==i.codePointAt(i.length-2)){if(i.length>2){if((r=i.lastIndexOf("/"))!==i.length-1){r<0?(i="",o=0):o=(i=i.slice(0,r)).length-1-i.lastIndexOf("/"),s=l,a=0;continue}}else if(i.length>0){i="",o=0,s=l,a=0;continue}}t&&(i=i.length>0?i+"/..":"..",o=2)}else i.length>0?i+="/"+e.slice(s+1,l):i=e.slice(s+1,l),o=l-s-1;s=l,a=0}else 46===n&&a>-1?a++:a=-1}return i}(e,!t);return 0!==n.length||t||(n="."),n.length>0&&47===e.codePointAt(e.length-1)&&(n+="/"),t?"/"+n:n}(t)},sep:"/"};function nl(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}let nu={cwd:function(){return"/"}};function nc(e){return!!(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}let nh=["history","path","basename","stem","extname","dirname"];class nd{constructor(e){let t,n;t=e?nc(e)?{path:e}:"string"==typeof e||function(e){return!!(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":nu.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<nh.length;){let e=nh[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)nh.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?na.basename(this.path):void 0}set basename(e){np(e,"basename"),nf(e,"basename"),this.path=na.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?na.dirname(this.path):void 0}set dirname(e){nm(this.basename,"dirname"),this.path=na.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?na.extname(this.path):void 0}set extname(e){if(nf(e,"extname"),nm(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=na.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){nc(e)&&(e=function(e){if("string"==typeof e)e=new URL(e);else if(!nc(e)){let t=TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if("file:"!==e.protocol){let e=TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){let e=TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}let t=e.pathname,n=-1;for(;++n<t.length;)if(37===t.codePointAt(n)&&50===t.codePointAt(n+1)){let e=t.codePointAt(n+2);if(70===e||102===e){let e=TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(t)}(e)}(e)),np(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?na.basename(this.path,this.extname):void 0}set stem(e){np(e,"stem"),nf(e,"stem"),this.path=na.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){let r=new Z(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){return void 0===this.value?"":"string"==typeof this.value?this.value:new TextDecoder(e||void 0).decode(this.value)}}function nf(e,t){if(e&&e.includes(na.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+na.sep+"`")}function np(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function nm(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}let ng=function(e){let t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},nv={}.hasOwnProperty;class ny extends ng{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=function(){let e=[],t={run:function(...t){let n=-1,r=t.pop();if("function"!=typeof r)throw TypeError("Expected function as last argument, not "+r);!function i(o,...s){let a=e[++n],l=-1;if(o)return void r(o);for(;++l<t.length;)(null===s[l]||void 0===s[l])&&(s[l]=t[l]);t=s,a?(function(e,t){let n;return function(...t){let o,s=e.length>t.length;s&&t.push(r);try{o=e.apply(this,t)}catch(e){if(s&&n)throw e;return r(e)}s||(o&&o.then&&"function"==typeof o.then?o.then(i,r):o instanceof Error?r(o):i(o))};function r(e,...i){n||(n=!0,t(e,...i))}function i(e){r(null,e)}})(a,i)(...s):r(null,...s)}(null,...t)},use:function(n){if("function"!=typeof n)throw TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}()}copy(){let e=new ny,t=-1;for(;++t<this.attachers.length;){let n=this.attachers[t];e.use(...n)}return e.data(no(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2==arguments.length?(nk("data",this.frozen),this.namespace[e]=t,this):nv.call(this.namespace,e)&&this.namespace[e]||void 0:e?(nk("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;for(;++this.freezeIndex<this.attachers.length;){let[e,...t]=this.attachers[this.freezeIndex];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);let n=e.call(this,...t);"function"==typeof n&&this.transformers.use(n)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let t=nT(e),n=this.parser||this.Parser;return nb("parse",n),n(String(t),t)}process(e,t){let n=this;return this.freeze(),nb("process",this.parser||this.Parser),nw("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,i){let o=nT(e),s=n.parse(o);function a(e,n){e||!n?i(e):r?r(n):t(void 0,n)}n.run(s,o,function(e,t,r){var i,o;if(e||!t||!r)return a(e);let s=n.stringify(t,r);"string"==typeof(i=s)||(o=i)&&"object"==typeof o&&"byteLength"in o&&"byteOffset"in o?r.value=s:r.result=s,a(e,r)})}}processSync(e){let t,n=!1;return this.freeze(),nb("processSync",this.parser||this.Parser),nw("processSync",this.compiler||this.Compiler),this.process(e,function(e,r){n=!0,ni(e),t=r}),nS("processSync","process",n),t}run(e,t,n){nE(e),this.freeze();let r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?i(void 0,n):new Promise(i);function i(i,o){let s=nT(t);r.run(e,s,function(t,r,s){let a=r||e;t?o(t):i?i(a):n(void 0,a,s)})}}runSync(e,t){let n,r=!1;return this.run(e,t,function(e,t){ni(e),n=t,r=!0}),nS("runSync","run",r),n}stringify(e,t){this.freeze();let n=nT(t),r=this.compiler||this.Compiler;return nw("stringify",r),nE(e),r(e,n)}use(e,...t){let n=this.attachers,r=this.namespace;if(nk("use",this.frozen),null==e);else if("function"==typeof e)s(e,t);else if("object"==typeof e)Array.isArray(e)?o(e):i(e);else throw TypeError("Expected usable value, not `"+e+"`");return this;function i(e){if(!("plugins"in e)&&!("settings"in e))throw Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");o(e.plugins),e.settings&&(r.settings=no(!0,r.settings,e.settings))}function o(e){let t=-1;if(null==e);else if(Array.isArray(e))for(;++t<e.length;){var n=e[t];if("function"==typeof n)s(n,[]);else if("object"==typeof n)if(Array.isArray(n)){let[e,...t]=n;s(e,t)}else i(n);else throw TypeError("Expected usable value, not `"+n+"`")}else throw TypeError("Expected a list of plugins, not `"+e+"`")}function s(e,t){let r=-1,i=-1;for(;++r<n.length;)if(n[r][0]===e){i=r;break}if(-1===i)n.push([e,...t]);else if(t.length>0){let[r,...o]=t,s=n[i][1];ns(s)&&ns(r)&&(r=no(!0,s,r)),n[i]=[e,r,...o]}}}}let nx=new ny().freeze();function nb(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `parser`")}function nw(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `compiler`")}function nk(e,t){if(t)throw Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function nE(e){if(!ns(e)||"string"!=typeof e.type)throw TypeError("Expected node, got `"+e+"`")}function nS(e,t,n){if(!n)throw Error("`"+e+"` finished async. Use `"+t+"` instead")}function nT(e){var t;return(t=e)&&"object"==typeof t&&"message"in t&&"messages"in t?e:new nd(e)}let nP=[],nC={allowDangerousHtml:!0},nA=/^(https?|ircs?|mailto|xmpp)$/i,nM=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function nI(e){let t=function(e){let t=e.rehypePlugins||nP,n=e.remarkPlugins||nP,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...nC}:nC;return nx().use(tO).use(n).use(nr,r).use(t)}(e),n=function(e){let t=e.children||"",n=new nd;return"string"==typeof t&&(n.value=t),n}(e);return function(e,t){let n=t.allowedElements,r=t.allowElement,i=t.components,o=t.disallowedElements,s=t.skipHtml,a=t.unwrapDisallowed,l=t.urlTransform||nL;for(let e of nM)Object.hasOwn(t,e.from)&&(e.from,e.to&&e.to,e.id);return t0(e,function(e,t,i){if("raw"===e.type&&i&&"number"==typeof t)return s?i.children.splice(t,1):i.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in eh)if(Object.hasOwn(eh,t)&&Object.hasOwn(e.properties,t)){let n=e.properties[t],r=eh[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=l(String(n||""),t,e))}}if("element"===e.type){let s=n?!n.includes(e.tagName):!!o&&o.includes(e.tagName);if(!s&&r&&"number"==typeof t&&(s=!r(e,t,i)),s&&i&&"number"==typeof t)return a&&e.children?i.children.splice(t,1,...e.children):i.children.splice(t,1),t}}),function(e,t){var n,r,i,o,s;let a;if(!t||void 0===t.Fragment)throw TypeError("Expected `Fragment` in options");let l=t.filePath||void 0;if(t.development){if("function"!=typeof t.jsxDEV)throw TypeError("Expected `jsxDEV` in options when `development: true`");n=l,r=t.jsxDEV,a=function(e,t,i,o){let s=Array.isArray(i.children),a=q(e);return r(t,i,o,s,{columnNumber:a?a.column-1:void 0,fileName:n,lineNumber:a?a.line:void 0},void 0)}}else{if("function"!=typeof t.jsx)throw TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw TypeError("Expected `jsxs` in production options");i=0,o=t.jsx,s=t.jsxs,a=function(e,t,n,r){let i=Array.isArray(n.children)?s:o;return r?i(t,n,r):i(t,n)}}let u={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:a,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:l,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?O:R,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},c=ei(u,e,void 0);return c&&"string"!=typeof c?c:u.create(e,u.Fragment,{children:c||void 0},void 0)}(e,{Fragment:ed.Fragment,components:i,ignoreInvalidStyle:!0,jsx:ed.jsx,jsxs:ed.jsxs,passKeys:!0,passNode:!0})}(t.runSync(t.parse(n),n),e)}function nL(e){let t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),i=e.indexOf("/");return -1===t||-1!==i&&t>i||-1!==n&&t>n||-1!==r&&t>r||nA.test(e.slice(0,t))?e:""}},90869:(e,t,n)=>{"use strict";n.d(t,{L:()=>r});let r=(0,n(12115).createContext)({})},91783:(e,t,n)=>{var r=n(53516);e.exports=function(e,t){var n=[];return r(e,function(e,r,i){t(e,r,i)&&n.push(e)}),n}},93557:(e,t,n)=>{var r=n(25016);e.exports=function(e){var t,n,i,o=(t=e,n={},i=0,r.forEach(t.children(),function e(o){var s=i;r.forEach(t.children(o),e),n[o]={low:s,lim:i++}}),n);r.forEach(e.graph().dummyChains,function(t){for(var n=e.node(t),r=n.edgeObj,i=function(e,t,n,r){var i,o,s=[],a=[],l=Math.min(t[n].low,t[r].low),u=Math.max(t[n].lim,t[r].lim);i=n;do s.push(i=e.parent(i));while(i&&(t[i].low>l||u>t[i].lim));for(o=i,i=r;(i=e.parent(i))!==o;)a.push(i);return{path:s.concat(a.reverse()),lca:o}}(e,o,r.v,r.w),s=i.path,a=i.lca,l=0,u=s[0],c=!0;t!==r.w;){if(n=e.node(t),c){for(;(u=s[l])!==a&&e.node(u).maxRank<n.rank;)l++;u===a&&(c=!1)}if(!c){for(;l<s.length-1&&e.node(u=s[l+1]).minRank<=n.rank;)l++;u=s[l]}e.setParent(t,u),t=e.successors(t)[0]}})}},97494:(e,t,n)=>{"use strict";n.d(t,{E:()=>i});var r=n(12115);let i=n(68972).B?r.useLayoutEffect:r.useEffect},98694:(e,t,n)=>{var r=n(25016),i=n(71814).Graph,o=n(24138);e.exports=function(e,t){if(1>=e.nodeCount())return[];var n,u,c,h,d,f,p,m=(n=e,u=t||s,c=new i,h=0,d=0,r.forEach(n.nodes(),function(e){c.setNode(e,{v:e,in:0,out:0})}),r.forEach(n.edges(),function(e){var t=c.edge(e.v,e.w)||0,n=u(e);c.setEdge(e.v,e.w,t+n),d=Math.max(d,c.node(e.v).out+=n),h=Math.max(h,c.node(e.w).in+=n)}),f=r.range(d+h+3).map(function(){return new o}),p=h+1,r.forEach(c.nodes(),function(e){l(f,p,c.node(e))}),{graph:c,buckets:f,zeroIdx:p}),g=function(e,t,n){for(var r,i=[],o=t[t.length-1],s=t[0];e.nodeCount();){for(;r=s.dequeue();)a(e,t,n,r);for(;r=o.dequeue();)a(e,t,n,r);if(e.nodeCount()){for(var l=t.length-2;l>0;--l)if(r=t[l].dequeue()){i=i.concat(a(e,t,n,r,!0));break}}}return i}(m.graph,m.buckets,m.zeroIdx);return r.flatten(r.map(g,function(t){return e.outEdges(t.v,t.w)}),!0)};var s=r.constant(1);function a(e,t,n,i,o){var s=o?[]:void 0;return r.forEach(e.inEdges(i.v),function(r){var i=e.edge(r),a=e.node(r.v);o&&s.push({v:r.v,w:r.w}),a.out-=i,l(t,n,a)}),r.forEach(e.outEdges(i.v),function(r){var i=e.edge(r),o=r.w,s=e.node(o);s.in-=i,l(t,n,s)}),e.removeNode(i.v),s}function l(e,t,n){n.out?n.in?e[n.out-n.in+t].enqueue(n):e[e.length-1].enqueue(n):e[0].enqueue(n)}},98862:(e,t,n)=>{var r=n(83172),i=n(11928),o=n(35776),s=n(4377),a=n(11368);e.exports=function(e,t,n){var l=e.constructor;switch(t){case"[object ArrayBuffer]":return r(e);case"[object Boolean]":case"[object Date]":return new l(+e);case"[object DataView]":return i(e,n);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return a(e,n);case"[object Map]":case"[object Set]":return new l;case"[object Number]":case"[object String]":return new l(e);case"[object RegExp]":return o(e);case"[object Symbol]":return s(e)}}},99753:(e,t,n)=>{"use strict";var r=n(25016),i=n(45285),o=n(55076).positionX;e.exports=function(e){var t,n,s,a;t=e=i.asNonCompoundGraph(e),n=i.buildLayerMatrix(t),s=t.graph().ranksep,a=0,r.forEach(n,function(e){var n=r.max(r.map(e,function(e){return t.node(e).height}));r.forEach(e,function(e){t.node(e).y=a+n/2}),a+=n+s}),r.forEach(o(e),function(t,n){e.node(n).x=t})}}}]);