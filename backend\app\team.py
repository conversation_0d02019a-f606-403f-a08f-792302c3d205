from typing import Dict, Any, List
from .agents import Planner<PERSON>gent, Retriever<PERSON><PERSON>, ForecastAgent, AnalyzerAgent
from .models import Message
from .utils.logging_utils import log_agent_interaction

class DynamicWorkflowTeam:
    """A team that uses the planner agent to dynamically determine the workflow."""
    
    def __init__(self):
        self.agents = [
            PlannerAgent(),
            RetrieverAgent(),
            ForecastAgent(),
            AnalyzerAgent()
        ]
        self.name = "dynamic_workflow_team"
        self.description = "Team with dynamic workflow orchestration"
    
    def get_agent(self, name: str):
        """Get an agent by name."""
        for agent in self.agents:
            if agent.name == name:
                return agent
        return None
        
    async def process(self, message: Message) -> Dict[str, Any]:
        """
        Process data through the team with dynamic workflow orchestration.
        
        Args:
            message: Initial input data
            
        Returns:
            Dictionary containing the final response
        """
        # Start with the planner agent
        print("Processing message:", message)
        # return {"ag1":{
        #     "message": 'hello',
        # }}
        current_agent = self.get_agent("planner")
        input_data = {
            "message": message.message,
            "file_path": message.file_path
        }
        
        # Log initial input
        log_agent_interaction(
            agent_name="team",
            input_data=input_data,
            output_data={"status": "starting"}
        )
        
        current_result = await current_agent.process(input_data)
        
        # Log planner agent interaction
        log_agent_interaction(
            agent_name="planner",
            input_data=input_data,
            output_data=current_result
        )
        
        # Keep track of processed results
        results = {
            "planner": current_result
        }
        
        # Continue processing until the planner indicates completion
        while True:
            # Get next agent recommendation from planner
            next_agent_name = current_result.get("next_agent")
            
            # If no next agent is specified, we're done
            if not next_agent_name:
                break
                
            # Get the next agent
            next_agent = self.get_agent(next_agent_name)
            if not next_agent:
                break
                
            # Process with the next agent
            current_agent = next_agent
            current_result = await current_agent.process(current_result)
            
            # Log each agent's interaction
            log_agent_interaction(
                agent_name=next_agent_name,
                input_data=results.get(next_agent_name, {}),
                output_data=current_result
            )
            
            results[next_agent_name] = current_result
            
        # Log final results
        log_agent_interaction(
            agent_name="team",
            input_data=input_data,
            output_data=results
        )

        return results

# Create a singleton instance of the team
team = DynamicWorkflowTeam() 