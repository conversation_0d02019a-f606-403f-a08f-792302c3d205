'use client';

import React from 'react';
import { motion } from 'motion/react';
import <PERSON>tie from 'lottie-react';
import animationData from '../../../public/animations/hero-animation.json';

export default function LottieHeroAnimation() {
  return (
    <motion.div
      className="relative w-full h-full min-h-[300px] sm:min-h-[400px] lg:min-h-[600px] flex items-center justify-center"
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 1, delay: 0.3 }}
    >
      <div className="w-full max-w-sm sm:max-w-lg lg:max-w-2xl">
        <Lottie
          animationData={animationData}
          loop={true}
          autoplay={true}
          style={{
            width: '100%',
            height: '100%',
            minHeight: '300px'
          }}
          className="sm:min-h-[400px] lg:min-h-[600px]"
        />
      </div>
    </motion.div>
  );
}
