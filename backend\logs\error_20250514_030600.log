Timestamp: 2025-05-14T03:06:00.516142
File: champion-banner-mobile.svg
Error: Error uploading file: [Errno 2] No such file or directory: 'uploads\\38f1a02f-9488-4f01-bd1a-47c495efb61e.svg'
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\KairosAI\Kairos_t0\backend\app\main.py", line 215, in upload_file
    with file_path.open("wb") as buffer:
         ^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\pathlib.py", line 1044, in open
    return io.open(self, mode, buffering, encoding, errors, newline)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'uploads\\38f1a02f-9488-4f01-bd1a-47c495efb61e.svg'

