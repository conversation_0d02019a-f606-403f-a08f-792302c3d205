import pandas as pd
from nixtla import NixtlaClient
from ..config import NIXTLA_API_KEY

client = NixtlaClient(api_key=NIXTLA_API_KEY)

async def generate_forecast(data: pd.DataFrame, 
                          target_column: str,
                          forecast_horizon: int = 10,
                          frequency: str = 'D') -> pd.DataFrame:
    """
    Generate time series forecast using Nixtla.
    
    Args:
        data (pd.DataFrame): Input time series data
        target_column (str): Name of the column to forecast
        forecast_horizon (int): Number of periods to forecast
        frequency (str): Frequency of the time series ('D' for daily, 'W' for weekly, etc.)
        
    Returns:
        pd.DataFrame: Forecast results
    """
    # Prepare data for Nixtla
    df = data.copy()
    df['ds'] = pd.date_range(start='2020-01-01', periods=len(df), freq=frequency)
    df['y'] = df[target_column]
    
    # Generate forecast
    forecast = client.forecast(
        df=df,
        h=forecast_horizon,
        freq=frequency
    )
    
    return forecast 