(()=>{var e={};e.id=166,e.ids=[166],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63172:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>c});var s={};t.r(s),t.d(s,{GET:()=>u,POST:()=>p});var n=t(96559),o=t(48088),a=t(37719),i=t(32190);async function u(){return i.NextResponse.json({message:"Hello from the API!"})}async function p(e){let r=await e.json();return i.NextResponse.json({message:"Data received successfully",data:r})}let d=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/hello/route",pathname:"/api/hello",filename:"route",bundlePath:"app/api/hello/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\KairosAI\\Kairos_t0\\frontend\\app\\api\\hello\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:c,serverHooks:x}=d;function v(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:c})}},78335:()=>{},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580],()=>t(63172));module.exports=s})();