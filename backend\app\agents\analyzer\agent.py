from typing import Dict, Any, List
import json
import pandas as pd
import numpy as np
from datetime import datetime
from pathlib import Path
import traceback
import logging
import os
from ..base_agent import Agent
from ...utils.llm_utils import get_llm_response
from ...utils.forecast_utils import generate_forecast

# Ensure logs directory exists
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)

# Set up logging with absolute path
log_file = log_dir / "analyzer_debug.log"
logging.basicConfig(
    filename=str(log_file.absolute()),
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    force=True  # Force reconfiguration of the root logger
)

# Create a file handler
file_handler = logging.FileHandler(str(log_file.absolute()))
file_handler.setLevel(logging.DEBUG)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

# Create a logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
logger.addHandler(file_handler)

# Test logging
logger.debug("=== Analyzer Agent Initialized ===")
logger.debug(f"Log file location: {log_file.absolute()}")

class ConversationContext:
    """Class to manage conversation context and history."""
    
    def __init__(self, max_history: int = 10):
        self.max_history = max_history
        self.messages: List[Dict[str, Any]] = []
        self.initial_forecast = None
        logger.debug("ConversationContext initialized")
        
    def add_message(self, role: str, content: str, context: Dict[str, Any] = None):
        """Add a message to the conversation history."""
        message = {
            "timestamp": datetime.now().isoformat(),
            "role": role,
            "content": content,
            "context": context or {}
        }
        self.messages.append(message)
        logger.debug(f"Added message from {role}")
        
        # Keep only the last max_history messages
        if len(self.messages) > self.max_history:
            self.messages = self.messages[-self.max_history:]
            logger.debug(f"Trimmed message history to {self.max_history} messages")
            
    def get_formatted_history(self) -> str:
        """Get formatted conversation history for LLM prompt."""
        formatted_history = []
        for msg in self.messages:
            formatted_history.append(f"{msg['role'].upper()}: {msg['content']}")
            if msg.get('context'):
                formatted_history.append(f"Context: {json.dumps(msg['context'], indent=2)}")
        logger.debug(f"Retrieved formatted history with {len(formatted_history)} entries")
        return "\n".join(formatted_history)

class AnalyzerAgent(Agent):
    """An analyzer agent that performs LLM analysis on the retrieved data."""
    
    def __init__(self):
        description = "An analyzer agent that performs LLM analysis on the retrieved data."
        super().__init__("analyzer", description)
        self.conversation_context = ConversationContext()
        logger.debug("AnalyzerAgent initialized")
        
    def _prepare_data_for_forecast(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare the data for forecasting by melting date columns into a time series format.
        
        Args:
            df: DataFrame with date columns (d_1, d_2, etc.)
            
        Returns:
            DataFrame in time series format
        """
        logger.debug("=== Starting Data Preparation ===")
        logger.debug(f"Input DataFrame shape: {df.shape}")
        logger.debug(f"Input DataFrame columns: {df.columns.tolist()}")
        
        try:
            # Get all date columns
            date_cols = [col for col in df.columns if col.startswith('d_')]
            logger.debug(f"Found date columns: {date_cols}")
            
            # Melt the dataframe to convert date columns into rows
            melted_df = pd.melt(
                df,
                id_vars=['id', 'item_id', 'dept_id', 'cat_id', 'store_id', 'state_id'],
                value_vars=date_cols,
                var_name='date',
                value_name='sales'
            )
            logger.debug(f"Melted DataFrame shape: {melted_df.shape}")
            
            # Convert date column to datetime
            # Extract the day number from d_1, d_2, etc. and add to base date
            base_date = pd.Timestamp('2011-01-29')
            melted_df['date'] = base_date + pd.to_timedelta(
                pd.to_numeric(melted_df['date'].str[2:]) - 1,  # Subtract 1 since d_1 is day 1
                unit='D'
            )
            
            # Group by date and sum sales for overall forecast
            ts_df = melted_df.groupby('date')['sales'].sum().reset_index()
            ts_df.columns = ['ds', 'y']  # Nixtla expects 'ds' for date and 'y' for target
            
            logger.debug(f"Final time series DataFrame shape: {ts_df.shape}")
            logger.debug("=== Data Preparation Complete ===")
            
            return ts_df
        except Exception as e:
            logger.error(f"Error in data preparation: {str(e)}")
            logger.error(traceback.format_exc())
            raise
        
    def _convert_to_serializable(self, obj):
        """Convert pandas/numpy types to JSON serializable format."""
        try:
            if isinstance(obj, pd.Timestamp):
                return obj.strftime('%Y-%m-%d %H:%M:%S')
            elif isinstance(obj, pd.DataFrame):
                # Convert DataFrame to list of dicts with serialized values
                return [
                    {k: self._convert_to_serializable(v) for k, v in record.items()}
                    for record in obj.to_dict(orient='records')
                ]
            elif isinstance(obj, dict):
                return {k: self._convert_to_serializable(v) for k, v in obj.items()}
            elif isinstance(obj, (list, tuple)):
                return [self._convert_to_serializable(item) for item in obj]
            elif isinstance(obj, (np.integer, np.int64)):
                return int(obj)
            elif isinstance(obj, (np.float64, np.float32)):
                return float(obj)
            return obj
        except Exception as e:
            logger.error(f"Error in serialization: {str(e)}")
            logger.error(traceback.format_exc())
            raise
        
    async def _get_llm_reforecast(self, historical_data: pd.DataFrame, previous_forecast: pd.DataFrame) -> pd.DataFrame:
        """Get reforecast from LLM based on historical data and previous forecast."""
        logger.debug("=== Starting LLM Reforecast ===")
        logger.debug(f"Historical data shape: {historical_data.shape}")
        logger.debug(f"Previous forecast shape: {previous_forecast.shape}")
        
        try:
            system_prompt = """You are a time series forecasting expert. Your task is to ONLY return a JSON array of forecast values.
            DO NOT include any explanations, analysis, or additional text.
            The response must be a valid JSON array of objects with 'ds' and 'TimeGPT' fields.
            Example format:
            [
                {"ds": "2020-05-14 00:00:00", "TimeGPT": 3.0849667},
                {"ds": "2020-05-15 00:00:00", "TimeGPT": 3.1652412}
            ]"""
            
            # Format historical data and previous forecast
            historical_str = historical_data.to_string()
            previous_forecast_str = previous_forecast.to_string()
            
            llm_prompt = f"""Based on this historical data and previous forecast, provide ONLY a JSON array of forecast values for the next 10 days.
            DO NOT include any other text or explanation.
            The response must be a valid JSON array starting with [ and ending with ].

Historical Data:
{historical_str}

Previous Forecast:
{previous_forecast_str}

Return ONLY the JSON array with 'ds' and 'TimeGPT' fields."""

            logger.debug("Sending prompt to LLM...")
            # Get LLM response
            forecast_json = await get_llm_response(llm_prompt, system_prompt)
            logger.debug(f"Received LLM response: {forecast_json[:200]}...")  # Log first 200 chars
            
            # Parse the forecast from LLM response
            forecast_data = json.loads(forecast_json)
            logger.debug(f"Parsed forecast data type: {type(forecast_data)}")
            logger.debug(f"Parsed forecast data: {forecast_data}")
            
            if not forecast_data:  # Check if forecast_data is empty
                logger.error("Empty forecast data received from LLM")
                raise ValueError("Empty forecast data received from LLM")
                
            forecast_df = pd.DataFrame(forecast_data)
            logger.debug(f"Created DataFrame shape: {forecast_df.shape}")
            logger.debug(f"DataFrame columns: {forecast_df.columns.tolist()}")
            
            if forecast_df.empty:  # Check if DataFrame is empty
                logger.error("Empty DataFrame created from forecast data")
                raise ValueError("Empty DataFrame created from forecast data")
                
            # Validate required columns
            if 'ds' not in forecast_df.columns or 'TimeGPT' not in forecast_df.columns:
                logger.error(f"Missing required columns. Available columns: {forecast_df.columns.tolist()}")
                raise ValueError("Missing required columns 'ds' or 'TimeGPT' in forecast data")
                
            # Convert and validate data types
            logger.debug("Converting data types...")
            forecast_df['ds'] = pd.to_datetime(forecast_df['ds'])
            forecast_df['y'] = pd.to_numeric(forecast_df['TimeGPT'], errors='coerce')
            
            # Check for any NaN values
            nan_count = forecast_df['y'].isna().sum()
            if nan_count > 0:
                logger.error(f"Found {nan_count} NaN values in forecast data")
                raise ValueError("Invalid numeric values in forecast data")
                
            logger.debug("Successfully created forecast DataFrame")
            return forecast_df[['ds', 'y']]
        except Exception as e:
            logger.error(f"Error in _get_llm_reforecast: {str(e)}")
            logger.error(f"Error type: {type(e)}")
            logger.error(f"Error traceback: {traceback.format_exc()}")
            # Return previous forecast if parsing fails
            if isinstance(previous_forecast, pd.DataFrame) and not previous_forecast.empty:
                logger.info("Returning previous forecast as fallback")
                return previous_forecast
            logger.error("No valid fallback forecast available")
            raise ValueError(f"Failed to generate forecast: {str(e)}")
        
    async def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the input data and perform LLM analysis.
        
        Args:
            input_data: Dictionary containing input data with retrieved data
            
        Returns:
            Dictionary containing the analysis results
        """
        logger.debug("=== Starting Analyzer Process ===")
        try:
            # Extract the original message and data
            original_message = input_data.get("message", "")
            data_summary = input_data.get("data", {}).get("data_summary", {})
            raw_data = input_data.get("data", {}).get("raw_data", [])
            
            logger.debug(f"Input data keys: {input_data.keys()}")
            logger.debug(f"Data summary keys: {data_summary.keys()}")
            logger.debug(f"Raw data length: {len(raw_data)}")
            
            if not raw_data:  # Check if raw_data is empty
                logger.error("No data received from retriever")
                raise ValueError("No data received from retriever")
            
            # Add user message to conversation context
            self.conversation_context.add_message(
                role="user",
                content=original_message,
                context={"data_summary": data_summary}
            )
            
            # Convert raw data to DataFrame
            logger.debug("Converting raw data to DataFrame...")
            df = pd.DataFrame(raw_data)
            logger.debug(f"DataFrame shape: {df.shape}")
            logger.debug(f"DataFrame columns: {df.columns.tolist()}")
            
            if df.empty:  # Check if DataFrame is empty
                logger.error("Empty DataFrame created from raw data")
                raise ValueError("Empty DataFrame created from raw data")
            
            # Prepare data for forecasting
            logger.debug("Preparing data for forecasting...")
            forecast_df = self._prepare_data_for_forecast(df)
            logger.debug(f"Forecast DataFrame shape: {forecast_df.shape}")
            logger.debug(f"Forecast DataFrame columns: {forecast_df.columns.tolist()}")
            
            if forecast_df.empty:  # Check if forecast DataFrame is empty
                logger.error("Empty forecast DataFrame created")
                raise ValueError("Empty forecast DataFrame created")
            
            # Generate forecast based on whether this is the first message
            logger.debug(f"Initial forecast exists: {self.conversation_context.initial_forecast is not None}")
            
            # Check if initial_forecast is None or empty DataFrame
            if (self.conversation_context.initial_forecast is None or 
                (isinstance(self.conversation_context.initial_forecast, pd.DataFrame) and 
                 self.conversation_context.initial_forecast.empty)):
                logger.debug("Generating initial forecast with Nixtla...")
                # First message: use Nixtla for initial forecast
                forecast_results = await generate_forecast(
                    data=forecast_df,
                    target_column='y',
                    forecast_horizon=30,
                    frequency='D'
                )
                logger.debug(f"Nixtla forecast shape: {forecast_results.shape if isinstance(forecast_results, pd.DataFrame) else 'Not a DataFrame'}")
                
                if not isinstance(forecast_results, pd.DataFrame) or forecast_results.empty:
                    logger.error("Invalid forecast results from Nixtla")
                    raise ValueError("Invalid forecast results from Nixtla")
                self.conversation_context.initial_forecast = forecast_results
            else:
                logger.debug("Generating reforecast with LLM...")
                # Subsequent messages: use LLM for reforecasting
                forecast_results = await self._get_llm_reforecast(
                    forecast_df,
                    self.conversation_context.initial_forecast
                )
                logger.debug(f"LLM forecast shape: {forecast_results.shape if isinstance(forecast_results, pd.DataFrame) else 'Not a DataFrame'}")
                
                if not isinstance(forecast_results, pd.DataFrame) or forecast_results.empty:
                    logger.error("Invalid forecast results from LLM")
                    raise ValueError("Invalid forecast results from LLM")
            
            # Create a prompt for the LLM that includes both historical data and forecast
            logger.debug("Preparing LLM analysis prompt...")
            system_prompt = """You are a data analysis expert. Analyze the provided sales data sample, forecast results, and the user's query.
            Consider the conversation history when providing insights. Maintain context from previous interactions.
            Provide concise, actionable insights based on both historical data and future predictions. Focus on key patterns, trends, and forecast implications."""
            
            # Format the sample data for better readability
            sample_data_str = "\n".join([
                f"Row {i+1}: {row}" for i, row in enumerate(raw_data[:10])  # Only show first 10 rows
            ])
            
            # Format forecast results
            forecast_str = forecast_results.to_string()
            
            # Get conversation history
            conversation_history = self.conversation_context.get_formatted_history()
            
            llm_prompt = f"""Conversation History:
{conversation_history}

Current Query: {original_message}

Data Overview:
- Total Rows in Dataset: {data_summary.get('total_rows', 'N/A')}
- Columns: {', '.join(data_summary.get('column_names', []))}

Historical Data Sample (First 10 rows):
{sample_data_str}

Forecast Results (Next 30 days):
{forecast_str}

Analyze both the historical data and forecast results, providing key insights relevant to the query. Consider the conversation history when providing your analysis."""

            logger.debug("Getting LLM analysis...")
            # Get LLM analysis
            analysis = await get_llm_response(llm_prompt, system_prompt)
            logger.debug(f"Received LLM analysis: {analysis[:200]}...")  # Log first 200 chars
            
            # Add assistant response to conversation context
            self.conversation_context.add_message(
                role="assistant",
                content=analysis,
                context={"forecast_results": self._convert_to_serializable(forecast_results)}
            )
            
            # Convert forecast results to serializable format
            serializable_forecast = self._convert_to_serializable(forecast_results)
            
            logger.debug("Logging interaction...")
            # Log to file
            log_data = {
                "timestamp": datetime.now().isoformat(),
                "system_prompt": system_prompt,
                "llm_prompt": llm_prompt,
                "analysis": analysis,
                "original_message": original_message,
                "forecast_results": serializable_forecast,
                "conversation_history": self.conversation_context.messages
            }
            
            # Write to log file
            log_file = log_dir / f"llm_interactions_{datetime.now().strftime('%Y%m%d')}.json"
            try:
                with open(log_file, 'a') as f:
                    f.write(json.dumps(log_data) + '\n')
                logger.debug("Successfully logged interaction")
            except Exception as e:
                logger.error(f"Error writing to log file: {str(e)}")
            
            logger.debug("=== Analyzer Process Complete ===")
            return {
                "message": original_message,  # Preserve original message
                "status": "success",
                "analysis": analysis,
                "forecast": serializable_forecast,  # Use serializable forecast
                "conversation_history": self.conversation_context.messages,  # Include conversation history
                "next_agent": "planner"  # Return to planner for final decision
            }
        except Exception as e:
            logger.error("=== Error in Analyzer Process ===")
            logger.error(f"Error type: {type(e)}")
            logger.error(f"Error message: {str(e)}")
            logger.error(f"Error traceback: {traceback.format_exc()}")
            
            error_data = {
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "original_message": original_message
            }
            
            # Log error to file
            try:
                log_file = log_dir / f"llm_errors_{datetime.now().strftime('%Y%m%d')}.json"
                with open(log_file, 'a') as f:
                    f.write(json.dumps(error_data) + '\n')
                logger.debug("Successfully logged error")
            except Exception as log_error:
                logger.error(f"Error writing to error log file: {str(log_error)}")
                
            return {
                "message": f"Error during analysis: {str(e)}",
                "status": "error",
                "next_agent": None
            } 