from typing import Dict, Any
import pandas as pd
import numpy as np
from pathlib import Path
import logging
import traceback
from datetime import datetime
from ..base_agent import Agent
from ...config import SALES_DATA_PATH

# Configure logging
logger = logging.getLogger(__name__)

class RetrieverAgent(Agent):
    """A retriever agent that handles data retrieval from the sales training evaluation CSV."""
    
    def __init__(self):
        description = "A retriever agent that handles data retrieval from the sales training evaluation CSV."
        super().__init__("retriever", description)
        self.static_data_path = SALES_DATA_PATH
        
    def _convert_to_serializable(self, obj):
        """Convert numpy/pandas types to Python native types."""
        if isinstance(obj, (np.integer, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.float64, np.float32)):
            return float(obj)
        elif isinstance(obj, (np.ndarray,)):
            return obj.tolist()
        elif isinstance(obj, pd.DataFrame):
            # Convert DataFrame to records, ensuring all values are serializable
            records = []
            for _, row in obj.iterrows():
                record = {}
                for col in obj.columns:
                    val = row[col]
                    if pd.isna(val):  # Handle NaN values
                        record[col] = None
                    elif isinstance(val, (np.integer, np.int64)):
                        record[col] = int(val)
                    elif isinstance(val, (np.float64, np.float32)):
                        record[col] = float(val)
                    else:
                        record[col] = str(val)  # Convert everything else to string
                records.append(record)
            return records
        elif isinstance(obj, pd.Series):
            return {str(k): self._convert_to_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, dict):
            return {str(k): self._convert_to_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._convert_to_serializable(item) for item in obj]
        elif pd.isna(obj):  # Handle NaN values
            return None
        return str(obj)  # Convert any remaining values to string
        
    async def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the input data and return a response.
        
        Args:
            input_data: Dictionary containing input data
            
        Returns:
            Dictionary containing the response
        """
        logger.info(f"Retriever Agent received input: {input_data}")

        try:
            # Verify file exists
            if not Path(self.static_data_path).exists():
                error_msg = f"Sales data file not found at: {self.static_data_path}"
                logger.error(error_msg)
                raise FileNotFoundError(error_msg)
            
            # Read the CSV file
            logger.info(f"Reading CSV file from: {self.static_data_path}")
            df = pd.read_csv(self.static_data_path)
            
            if df.empty:
                error_msg = "The sales data file is empty"
                logger.error(error_msg)
                raise ValueError(error_msg)
            
            # Basic data summary
            data_summary = {
                "total_rows": len(df),
                "total_columns": len(df.columns),
                "column_names": df.columns.tolist(),

                "first_few_rows": self._convert_to_serializable(df.head())

            }
            logger.info(f"Data summary: {data_summary}")

            # Get full dataset for analysis
            logger.info("Converting full dataset to dictionary format")

            full_data = self._convert_to_serializable(df)


            # Preserve the original message and status
            original_message = input_data.get("message", "")
            original_status = input_data.get("status", "success")

            result = {
                "message": original_message,
                "status": original_status,
                "data": {  # Wrap data in a 'data' key to match analyzer's expectations
                    "data_summary": data_summary,
                    "raw_data": full_data  # Now sending full dataset
                },
                "next_agent": "planner"  # Always go back to planner after retrieval
            }
            logger.info("Successfully processed data retrieval")
            return result

        except FileNotFoundError as e:
            error_msg = f"File not found error: {str(e)}"
            logger.error(error_msg)
            return {
                "message": error_msg,
                "status": "error",
                "error": str(e),
                "error_type": "FileNotFoundError",
                "next_agent": None
            }
        except pd.errors.EmptyDataError as e:
            error_msg = "The sales data file is empty"
            logger.error(error_msg)
            return {
                "message": error_msg,
                "status": "error",
                "error_type": "EmptyDataError",
                "next_agent": None
            }
        except Exception as e:
            error_msg = f"Unexpected error in retriever agent: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_msg)
            
            # Log detailed error to file
            error_log_path = Path("logs") / f"retriever_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            with open(error_log_path, 'w') as f:
                f.write(f"Timestamp: {datetime.now().isoformat()}\n")
                f.write(f"Input: {input_data}\n")
                f.write(f"Error: {error_msg}\n")
            
            return {
                "message": f"Error retrieving data: {str(e)}",
                "status": "error",
                "error_type": type(e).__name__,
                "error_log": str(error_log_path),
                "next_agent": None
            } 