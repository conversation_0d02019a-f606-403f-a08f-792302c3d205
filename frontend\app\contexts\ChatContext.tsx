'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import {
  WebSocketClient,
  websocketClient,
  ChatMessage,
  ConnectionStatus,
  WebSocketResponse,
  WebSocketMessage,
  uploadFile
} from '../utils/websocket';

// Define the context type
interface ChatContextType {
  messages: ChatMessage[];
  thinking: string;
  isThinking: boolean;
  connectionStatus: ConnectionStatus;
  chartData: any | null;
  causalityData: any | null;
  chartSelection: {
    point?: { name: string, value: number } | null;
    range?: { start: string, end: string, values: any[] } | null;
  };
  uploadedFiles: { name: string, path: string }[];
  externalContexts: any[];
  selectedExternalContexts: any[];
  insights: string;
  currentSlug: string | null;
  sendMessage: (message: string) => void;
  uploadFile: (file: File) => Promise<string>;
  removeUploadedFile: (fileName: string) => void;
  clearMessages: () => void;
  clearUploadedFiles: () => void;
  setChartSelection: (selection: { point?: any, range?: any }) => void;
  setSelectedExternalContexts: (contexts: any[]) => void;
  connectWithSlug: (slug: string) => void;
}

// Create the context with a default value
const ChatContext = createContext<ChatContextType | undefined>(undefined);

// Provider component
export function ChatProvider({ children }: { children: ReactNode }) {
  const router = useRouter();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [thinking, setThinking] = useState<string>('');
  const [isThinking, setIsThinking] = useState<boolean>(false);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const [chartData, setChartData] = useState<any | null>(null);
  const [causalityData, setCausalityData] = useState<any | null>(null);
  const [chartSelection, setChartSelection] = useState<{
    point?: { name: string, value: number } | null;
    range?: { start: string, end: string, values: any[] } | null;
  }>({});
  const [uploadedFiles, setUploadedFiles] = useState<{ name: string, path: string }[]>([]);
  const [externalContexts, setExternalContexts] = useState<any[]>([]);
  const [selectedExternalContexts, setSelectedExternalContexts] = useState<any[]>([]);
  const [insights, setInsights] = useState<string>("");
  const [currentSlug, setCurrentSlug] = useState<string | null>(null);

  // Connect to WebSocket on component mount
  useEffect(() => {
    console.log("[DEBUG] ChatContext mounted");

    // Set up WebSocket event handlers
    websocketClient.onStatusChange((status) => {
      console.log("[DEBUG] WebSocket status changed:", status);
      setConnectionStatus(status);
    });

    websocketClient.onMessage((data) => {
      console.log("[DEBUG] WebSocket message received in context:", data);
      handleWebSocketResponse(data);
    });

    // Connect to the WebSocket server - only if not already connected
    if (typeof window !== 'undefined') {
      // Extract slug from URL if present
      const pathParts = window.location.pathname.split('/');
      if (pathParts.length > 2 && pathParts[1] === 'chats') {
        const slugFromUrl = pathParts[2];
        console.log("[DEBUG] Found slug in URL:", slugFromUrl);
        if (slugFromUrl && slugFromUrl !== 'new-chat') {
          setCurrentSlug(slugFromUrl);
          websocketClient.connect(slugFromUrl);
        } else {
          websocketClient.connect();
        }
      } else {
        websocketClient.connect();
      }
    } else {
      websocketClient.connect();
    }

    // Clean up on unmount
    return () => {
      console.log("[DEBUG] ChatContext unmounting, disconnecting WebSocket");
      websocketClient.disconnect();
    };
  }, []);

  // Handle WebSocket responses
  const handleWebSocketResponse = (data: WebSocketResponse) => {
    console.log("[DEBUG] WebSocket response received:", data);

    // Log all fields for debugging
    console.log("[DEBUG] Response fields:", {
      hasMessage: !!data.message,
      hasThought: !!data.thought,
      hasChart: !!data.chart,
      hasCausality: !!data.causality,
      hasInsights: !!data.insights,
      hasExternalContexts: !!data.external_contexts,
      hasHistory: !!data.history
    });

    // Handle slug if present
    if (data.slug) {
      console.log("[DEBUG] Slug received:", data.slug);
      setCurrentSlug(data.slug);

      // Update the URL without full page reload
      if (typeof window !== 'undefined') {
        console.log("[DEBUG] Updating URL with slug:", data.slug);

        // Force navigation to the chat page if we're on the main chat page
        const currentPath = window.location.pathname;
        if (currentPath === '/' || currentPath === '/chat') {
          console.log("[DEBUG] Redirecting to chat page with slug:", data.slug);
          // Use Next.js router for navigation instead of direct window.location
          router.push(`/chats/${data.slug}`);
        } else {
          // Just update the URL without navigation for other pages
          window.history.pushState({}, '', `/chats/${data.slug}`);
        }
      }
    }

    // Handle chat history if present
    if (data.history && Array.isArray(data.history)) {
      console.log("[DEBUG] Chat history received:", data.history);

      // Log the structure of each message in history for debugging
      data.history.forEach((msg, idx) => {
        console.log(`[DEBUG] History message ${idx}:`, {
          type: msg.type,
          hasChart: !!msg.chart,
          hasCausality: !!msg.causality,
          hasInsights: !!msg.insights,
          hasExternalContexts: !!msg.external_contexts
        });
      });

      setMessages(data.history as ChatMessage[]);

      // Extract latest data from history for charts, causality, insights, etc.
      // This ensures we have the latest data even if it's not sent separately
      const aiMessages = data.history.filter(msg => msg.type === 'ai');
      if (aiMessages.length > 0) {
        const latestAiMsg = aiMessages[aiMessages.length - 1];
        console.log("[DEBUG] Latest AI message from history:", latestAiMsg);

        // Always update chart data from the latest message in history
        // regardless of whether data.chart exists
        if (latestAiMsg.chart) {
          console.log("[DEBUG] Setting chart data from history:", latestAiMsg.chart);
          setChartData(latestAiMsg.chart);
        }

        // Always update causality data from the latest message in history
        if (latestAiMsg.causality) {
          console.log("[DEBUG] Setting causality data from history:", latestAiMsg.causality);
          setCausalityData(latestAiMsg.causality);
        } else {
          console.log("[DEBUG] No causality data in latest message");
        }

        // Always update insights from the latest message in history
        if (latestAiMsg.insights) {
          console.log("[DEBUG] Setting insights from history:", latestAiMsg.insights);
          setInsights(latestAiMsg.insights);
        } else {
          console.log("[DEBUG] No insights data in latest message");
        }

        // Always update external contexts from the latest message in history
        if (latestAiMsg.external_contexts) {
          console.log("[DEBUG] Setting external contexts from history:", latestAiMsg.external_contexts);
          const enriched = latestAiMsg.external_contexts.map((ctx: any, idx: number) => ({
            ...ctx,
            id: ctx.url + '-' + idx,
            favicon: `https://www.google.com/s2/favicons?domain=${new URL(ctx.url).hostname}`,
            isSelected: true,
          }));
          setExternalContexts(enriched);
        } else {
          console.log("[DEBUG] No external contexts in latest message");
        }
      } else {
        console.log("[DEBUG] No AI messages found in history");
      }
    }

    if (data.thought) {
      console.log("[DEBUG] Processing thought:", data.thought);
      // Update thinking state
      setThinking(data.thought);
      setIsThinking(true);
    } else if (data.message) {
      console.log("[DEBUG] Processing message:", data.message);
      // Add AI message
      if (data.message) {
        // Create a complete message object with all available data
        const newMessage: ChatMessage = {
          type: 'ai',
          content: data.message,
          timestamp: new Date().toISOString(),
          status: data.status || 'success'
        };

        // Add other fields if they exist in the response
        if (data.chart) newMessage.chart = data.chart;
        if (data.causality) newMessage.causality = data.causality;
        if (data.insights) newMessage.insights = data.insights;
        if (data.external_contexts) newMessage.external_contexts = data.external_contexts;
        if (data.thought) newMessage.thought = data.thought;

        // Update the state with the new message
        setMessages(prev => [...prev, newMessage]);

        // Also update the individual state variables for each component
        // This ensures the UI components get the latest data
        if (data.chart) setChartData(data.chart);
        if (data.causality) setCausalityData(data.causality);
        if (data.insights) setInsights(data.insights || "");
        if (data.external_contexts) {
          const enriched = data.external_contexts.map((ctx: any, idx: number) => ({
            ...ctx,
            id: ctx.url + '-' + idx,
            favicon: `https://www.google.com/s2/favicons?domain=${new URL(ctx.url).hostname}`,
            isSelected: true,
          }));
          setExternalContexts(enriched);
        }
      }
      setIsThinking(false);
      setThinking('');
    } else {
      console.log("[DEBUG] No message or thought in response:", data);
    }

    // Handle chart data if present (even if null)
    if ('chart' in data) {
      console.log("[DEBUG] Chart data received:", data.chart);
      setChartData(data.chart);
    }

    // Handle causality data if present (even if null)
    if ('causality' in data) {
      console.log("[DEBUG] Causality data received:", data.causality);
      setCausalityData(data.causality);
    }

    // Handle external contexts if present (even if null)
    if ('external_contexts' in data && data.external_contexts) {
      console.log("[DEBUG] External contexts received:", data.external_contexts);
      const enriched = data.external_contexts.map((ctx: any, idx: number) => ({
        ...ctx,
        id: ctx.url + '-' + idx,
        favicon: `https://www.google.com/s2/favicons?domain=${new URL(ctx.url).hostname}`,
        isSelected: true,
      }));
      setExternalContexts(enriched);
    } else if ('external_contexts' in data && data.external_contexts === null) {
      // Clear external contexts if null
      setExternalContexts([]);
    }

    // Handle insights if present (even if null)
    if ('insights' in data) {
      console.log("[DEBUG] Insights received:", data.insights);
      setInsights(data.insights || "");
    }
  };

  // Connect to WebSocket with a specific slug
  const connectWithSlug = (slug: string) => {
    console.log("[DEBUG] Connecting with slug:", slug);

    // Only update if the slug has changed
    if (currentSlug !== slug) {
      setCurrentSlug(slug);

      // Connect with the slug - our improved WebSocket client will handle
      // updating the slug without creating a new connection if possible
      websocketClient.connect(slug);
    } else {
      console.log("[DEBUG] Already connected with slug:", slug);
    }
  };

  // Send the message to the WebSocket server
  const sendMessage = (message: string) => {
    // Add user message to the chat
    setMessages(prev => [...prev, { type: 'user', content: message }]);

    // Set thinking state
    setIsThinking(true);
    setThinking('Analyzing query...');

    // Create message payload
    const payload: WebSocketMessage = { message };

    // Add file paths if available
    if (uploadedFiles.length > 0) {
      payload.files_path = uploadedFiles.map(file => file.path);
    }

    // Add model context with any available context information
    const modelContext: any = {};

    // Add chart context if available
    if (chartData || chartSelection?.point || chartSelection?.range) {
      modelContext.chart = {
        data: chartData
      };

      // Only add selection if it exists
      if (chartSelection) {
        modelContext.chart.selection = chartSelection;
      }

      console.log("[DEBUG] Adding chart context to message:", modelContext.chart);
    }

    // Only add model_context if we have context to share
    if (Object.keys(modelContext).length > 0) {
      payload.model_context = modelContext;
    }

    // Add selected external contexts if any
    if (selectedExternalContexts.length > 0) {
      payload.external_contexts = selectedExternalContexts;
    }

    // Send the message to the WebSocket server
    websocketClient.sendMessage(payload);
  };

  // Upload a file
  const handleFileUpload = async (file: File): Promise<string> => {
    try {
      const result = await uploadFile(file);

      // Add to uploaded files
      if (result.filename) {
        setUploadedFiles(prev => [...prev, { name: file.name, path: result.filename }]);
      }

      return result.filename;
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  };

  // Remove an uploaded file
  const removeUploadedFile = (fileName: string) => {
    setUploadedFiles(prev => prev.filter(file => file.name !== fileName));
  };

  // Clear all uploaded files
  const clearUploadedFiles = () => {
    setUploadedFiles([]);
  };

  // Clear all messages
  const clearMessages = () => {
    setMessages([]);
    setThinking('');
    setIsThinking(false);
    setChartData(null);
  };

  // Create the context value
  const contextValue: ChatContextType = {
    messages,
    thinking,
    isThinking,
    connectionStatus,
    chartData,
    causalityData,
    chartSelection,
    uploadedFiles,
    externalContexts,
    selectedExternalContexts,
    insights,
    currentSlug,
    sendMessage,
    uploadFile: handleFileUpload,
    removeUploadedFile,
    clearMessages,
    clearUploadedFiles,
    setChartSelection,
    setSelectedExternalContexts,
    connectWithSlug,
  };

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  );
}

// Custom hook to use the chat context
export function useChat() {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
}







