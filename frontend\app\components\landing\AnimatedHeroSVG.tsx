'use client';

import React from 'react';
import { motion } from 'motion/react';
import { useTheme } from '../../contexts/ThemeContext';

export default function AnimatedHeroSVG() {
  const { theme } = useTheme();

  // Theme-aware colors
  const colors = theme === 'dark'
    ? {
        grid: 'rgba(255, 255, 255, 0.1)',
        line: '#f2e9e4',
        node: '#c9ada7',
        accent: '#9a8c98',
        neural: '#4a4e69'
      }
    : {
        grid: 'rgba(34, 34, 59, 0.1)',
        line: '#4a4e69',
        node: '#9a8c98',
        accent: '#c9ada7',
        neural: '#22223b'
      };

  return (
    <div className="relative w-full h-full min-h-[400px] flex items-center justify-center">
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 600 400"
        className="max-w-lg"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Background grid */}
        <defs>
          <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
            <path
              d="M 40 0 L 0 0 0 40"
              fill="none"
              stroke={colors.grid}
              strokeWidth="0.5"
              opacity="0.3"
            />
          </pattern>
          
          {/* Gradient definitions */}
          <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor={colors.line} />
            <stop offset="50%" stopColor={colors.node} />
            <stop offset="100%" stopColor={colors.accent} />
          </linearGradient>

          <linearGradient id="nodeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor={colors.line} />
            <stop offset="100%" stopColor={colors.neural} />
          </linearGradient>
        </defs>
        
        {/* Grid background */}
        <rect width="100%" height="100%" fill="url(#grid)" />
        
        {/* Animated forecast line */}
        <motion.path
          d="M 50 300 Q 150 250 250 200 T 450 150 T 550 100"
          fill="none"
          stroke="url(#lineGradient)"
          strokeWidth="3"
          initial={{ pathLength: 0, opacity: 0 }}
          animate={{ pathLength: 1, opacity: 1 }}
          transition={{ duration: 2, ease: "easeInOut", delay: 0.5 }}
        />
        
        {/* Data points */}
        {[
          { x: 50, y: 300, delay: 1 },
          { x: 150, y: 250, delay: 1.2 },
          { x: 250, y: 200, delay: 1.4 },
          { x: 350, y: 175, delay: 1.6 },
          { x: 450, y: 150, delay: 1.8 },
          { x: 550, y: 100, delay: 2 },
        ].map((point, index) => (
          <motion.circle
            key={index}
            cx={point.x}
            cy={point.y}
            r="6"
            fill="url(#nodeGradient)"
            stroke={theme === 'dark' ? '#ffffff' : '#ffffff'}
            strokeWidth="2"
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5, delay: point.delay }}
            whileHover={{ scale: 1.3 }}
          />
        ))}
        
        {/* Prediction area */}
        <motion.path
          d="M 350 175 Q 450 140 550 90 Q 450 160 350 185 Z"
          fill={colors.line}
          fillOpacity="0.2"
          stroke={colors.line}
          strokeWidth="1"
          strokeDasharray="5,5"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 2.5 }}
        />
        
        {/* Floating data nodes */}
        {[
          { x: 100, y: 80, size: 4, delay: 3 },
          { x: 200, y: 60, size: 3, delay: 3.2 },
          { x: 300, y: 90, size: 5, delay: 3.4 },
          { x: 400, y: 70, size: 4, delay: 3.6 },
          { x: 500, y: 50, size: 3, delay: 3.8 },
        ].map((node, index) => (
          <motion.circle
            key={`node-${index}`}
            cx={node.x}
            cy={node.y}
            r={node.size}
            fill={colors.accent}
            initial={{ opacity: 0, y: node.y + 20 }}
            animate={{ 
              opacity: [0, 1, 0.7, 1],
              y: [node.y + 20, node.y - 10, node.y + 5, node.y],
            }}
            transition={{ 
              duration: 2, 
              delay: node.delay,
              repeat: Infinity,
              repeatType: "reverse",
              repeatDelay: 1
            }}
          />
        ))}
        
        {/* AI Brain representation */}
        <motion.g
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1, delay: 4 }}
        >
          {/* Neural network connections */}
          {[
            { x1: 480, y1: 250, x2: 520, y2: 280 },
            { x1: 480, y1: 250, x2: 520, y2: 220 },
            { x1: 520, y1: 280, x2: 560, y2: 250 },
            { x1: 520, y1: 220, x2: 560, y2: 250 },
          ].map((line, index) => (
            <motion.line
              key={`connection-${index}`}
              x1={line.x1}
              y1={line.y1}
              x2={line.x2}
              y2={line.y2}
              stroke="#4a4e69"
              strokeWidth="2"
              opacity="0.6"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 0.5, delay: 4.2 + index * 0.1 }}
            />
          ))}
          
          {/* Neural nodes */}
          {[
            { x: 480, y: 250 },
            { x: 520, y: 280 },
            { x: 520, y: 220 },
            { x: 560, y: 250 },
          ].map((node, index) => (
            <motion.circle
              key={`neural-${index}`}
              cx={node.x}
              cy={node.y}
              r="8"
              fill="#22223b"
              stroke="#f2e9e4"
              strokeWidth="2"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.3, delay: 4.5 + index * 0.1 }}
            />
          ))}
        </motion.g>
        
        {/* Pulsing effect around AI brain */}
        <motion.circle
          cx="520"
          cy="250"
          r="40"
          fill="none"
          stroke="#f2e9e4"
          strokeWidth="1"
          opacity="0.3"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ 
            scale: [0.8, 1.2, 0.8],
            opacity: [0, 0.3, 0]
          }}
          transition={{ 
            duration: 3,
            repeat: Infinity,
            delay: 5
          }}
        />
      </svg>
    </div>
  );
}
