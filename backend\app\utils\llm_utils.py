from openai import OpenAI
from ..config import OPENAI_API_KEY, LLM_MODEL

client = OpenAI(api_key=OPENAI_API_KEY)

async def get_llm_response(prompt: str, system_prompt: str = None) -> str:
    """
    Get a response from the LLM model.
    
    Args:
        prompt (str): The user prompt
        system_prompt (str, optional): System prompt to guide the model's behavior
        
    Returns:
        str: The model's response
    """
    messages = []
    if system_prompt:
        messages.append({"role": "system", "content": system_prompt})
    messages.append({"role": "user", "content": prompt})
    
    response = client.chat.completions.create(
        model=LLM_MODEL,
        messages=messages,
        temperature=0.7,
        max_tokens=1000
    )
    
    return response.choices[0].message.content 