/**
 * Formats a date to a readable string
 */

import { ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date);
}

/**
 * Truncates a string to a specified length
 */
export function truncateString(str: string, length: number): string {
  if (str.length <= length) return str;
  return str.slice(0, length) + '...';
}

/**
 * Generates a random ID
 */
export function generateId(): string {
  return Math.random().toString(36).substring(2, 9);
}
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}