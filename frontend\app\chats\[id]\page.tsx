'use client';

import React, { useState, useRef, useEffect, use } from 'react';
import { Layout, Model, TabNode, IJsonModel } from 'flexlayout-react';
import 'flexlayout-react/style/dark.css';
import '../../styles/custom-flexlayout.css';
import ClientChatBox from '../../components/chat/ClientChatBox';
import InteractiveLineChart from '../../components/visualizations/InteractiveLineChart';
import CausalityGraph from '../../components/visualizations/CausalityGraph';
import { useChat } from '../../contexts/ChatContext';
import ReactMarkdown from 'react-markdown';
import ContextCard from '../../components/chat/ContextCard';
import InsightsCard from '../../components/chat/InsightsCard';
import TypingEffect from '../../components/chat/TypingEffect';
// Chat Panel Component - Using ChatContext
const ChatPanel = () => {
  const { messages, thinking, isThinking, sendMessage, connectionStatus } = useChat();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [completedTyping, setCompletedTyping] = useState<{ [key: number]: boolean }>({});

  // Scroll to bottom when messages or thinking state changes
  useEffect(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop = scrollContainerRef.current.scrollHeight;
    }
  }, [messages, isThinking]);

  return (
    // Removed padding from this outer container
    <div className="h-full flex flex-col">
      {/* Reduced margin-bottom */}
      {/* <h3 className="text-lg font-inknut text-kairosGreen px-2 pt-2 mb-2">Chat</h3> */}
      {/* Reduced padding on the glass-card */}
      <div className="glass-card flex-1 p-2 flex flex-col overflow-hidden m-2 mt-2"> {/* Added margin to compensate for outer padding removal */}
        {/* Message history - Reduced margin-bottom and spacing */}
        <div
          ref={scrollContainerRef}
          // Reduced mb and space-y, added small padding
          className="flex-1 overflow-y-auto mb-2 space-y-2 pr-1 p-1"
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: 'rgba(180, 180, 180, 0.1) rgba(0, 0, 0, 0.05)'
          }}
        >
          {messages.map((msg, index) => (
            <div key={index} className={`${msg.type === 'user' ? 'text-right' : 'text-left'}`}>
              {/* Reduced padding on messages */}
              <div className={`inline-block p-1.5 rounded-md max-w-[90%] break-words ${ // p-1.5 or p-2
                msg.type === 'user'
                  ? 'bg-gray-300/10 text-white'
                  : 'bg-transparent/10 text-white'
              }`}>
                {msg.type === 'user' ? (
                  <p className="text-sm">{msg.content}</p>
                ) : (
                  <div className="text-sm prose prose-invert ">
                    {completedTyping[index] ? (
                      <ReactMarkdown>{msg.content}</ReactMarkdown>
                    ) : (
                      <TypingEffect
                        text={msg.content}
                        speed={1}
                        onComplete={() => setCompletedTyping(prev => ({ ...prev, [index]: true }))}
                      />
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}

          {isThinking && (
            <div className="text-left">
               {/* Reduced padding */}
              <div className="inline-block p-1.5 rounded-md glass-card max-w-[90%] break-words">
                <p className="text-gray-300 text-sm">
                  <span className="inline-block mr-1">🧠</span>
                  {thinking}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Chat input - Reduced padding-top */}
        <div className="mt-auto border-t border-white/10 pt-2">
          {/* Connection status indicator */}
          <div className="flex items-center mb-1 px-1">
            <span className="text-xs text-gray-400 mr-1">Status:</span>
            {connectionStatus === 'connected' && <span className="text-green-500 text-xs">● Connected</span>}
            {connectionStatus === 'connecting' && <span className="text-yellow-500 text-xs">● Connecting...</span>}
            {(connectionStatus === 'disconnected' || connectionStatus === 'error') &&
              <span className="text-red-500 text-xs">● Disconnected</span>
            }
          </div>
          {/* Using ClientChatBox with sendMessage from ChatContext */}
          <ClientChatBox onSend={sendMessage} inSidebar={false} />
        </div>
      </div>
    </div>
  );
};

// Visualization Panel 1 - Graph - Now using Recharts
const GraphPanel = () => {
  return (
    <div className="h-full w-full flex flex-col">
      <InteractiveLineChart />
    </div>
  );
};

// Visualization Panel 2 - Charts - Now using Cytoscape with Dagre
const ChartsPanel = () => {
  return (
    <div className="h-full w-full flex flex-col">
      <CausalityGraph />
    </div>
  );
};

// Component factory (remains the same)
const factory = (node: TabNode) => {
  const component = node.getComponent();
  switch (component) {
    case 'chat': return <ChatPanel />;
    case 'graph': return <GraphPanel />;
    case 'charts': return <ChartsPanel />;
    case 'context': return <ContextCard />;
    case 'insights': {
      const { insights } = useChat();
      return <InsightsCard markdown={insights} />;
    }
    default: return <div>Unknown component: {component}</div>;
  }
};

export default function ChatPage({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap the params promise using React.use()
  const unwrappedParams = use(params);
  const id = unwrappedParams.id;

  const { connectWithSlug, currentSlug } = useChat();
  const layoutRef = useRef<Layout>(null);
  const [hasConnected, setHasConnected] = useState(false);

  // Connect to WebSocket with the slug when the component mounts
  useEffect(() => {
    console.log("Chat page mounted with id:", id);

    // Only connect if we have a valid slug and haven't connected yet
    if (id && id !== 'new-chat' && !hasConnected) {
      // Use a small delay to ensure the context is fully initialized
      const timer = setTimeout(() => {
        console.log("Connecting with slug (delayed):", id);
        connectWithSlug(id);
        setHasConnected(true);
      }, 500); // Increased delay to ensure context is ready

      return () => clearTimeout(timer);
    }
  }, [id, connectWithSlug, hasConnected]);

  // If the current slug changes and doesn't match our ID, update it
  useEffect(() => {
    if (currentSlug && currentSlug !== id && hasConnected) {
      console.log("Current slug changed, updating URL:", currentSlug);
      // Update the URL without full page reload
      if (typeof window !== 'undefined') {
        window.history.replaceState({}, '', `/chats/${currentSlug}`);
      }
    }
  }, [currentSlug, id, hasConnected]);

  // Default layout JSON with optimized settings
  const json: IJsonModel = {
    global: {
        tabEnableClose: false,
        tabEnablePopout: true,
        tabSetEnableMaximize: true,
        tabSetMinWidth: 100,
        tabSetMinHeight: 100,
        splitterSize: 2,
        splitterExtra: 2
    },
    borders: [],
    layout: {
      type: 'row',
      weight: 100,
      children: [
        {
          type: 'column',
          weight: 30,
          children: [
            {
              type: 'tabset',
              weight: 100,
              children: [
                { type: 'tab', name: 'Chat', component: 'chat', id: 'chat-tab' }
              ]
            }
          ]
        },
        {
          type: 'column',
          weight: 70,
          children: [
            {
              type: 'row',
              weight: 100,
              children: [
                {
                  type: 'column',
                  weight: 60,
                  children: [
                    {
                      type: 'tabset',
                      weight: 50,
                      children: [
                        { type: 'tab', name: 'Line Chart', component: 'graph', id: 'graph-tab' }
                      ]
                    },
                    {
                      type: 'tabset',
                      weight: 50,
                      children: [
                        { type: 'tab', name: 'Causality Graph', component: 'charts', id: 'charts-tab' }
                      ]
                    }
                  ]
                },
                {
                  type: 'column',
                  weight: 40,
                  children: [
                    {
                      type: 'tabset',
                      weight: 50,
                      children: [
                        { type: 'tab', name: 'Context', component: 'context', id: 'context-tab' }
                      ]
                    },
                    {
                      type: 'tabset',
                      weight: 50,
                      children: [
                        { type: 'tab', name: 'Insights', component: 'insights', id: 'insights-tab' }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  };

  const [model, setModel] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('chatLayoutV2'); // Use new key if layout changed significantly
      if (saved) {
        try {
          // Add validation if necessary before parsing
          return Model.fromJson(JSON.parse(saved));
        } catch (e) {
          console.error('Failed to parse saved layout:', e);
          localStorage.removeItem('chatLayoutV2'); // Clear invalid state
        }
      }
    }
    return Model.fromJson(json);
  });

  const onModelChange = () => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('chatLayoutV2', JSON.stringify(model.toJson()));
    }
  };

  const resetLayout = () => {
    setModel(Model.fromJson(json));
     if (typeof window !== 'undefined') {
        localStorage.removeItem('chatLayoutV2');
     }
  };

  return (
    // Removed padding/margin from Layout container if any existed
    <div className="h-screen w-full overflow-hidden bg-transparent">
      <button
        type="button"
        onClick={resetLayout}
        className="absolute top-2 right-2 z-10 bg-kairosBlue/80 text-white px-3 py-1 rounded text-xs hover:bg-opacity-100 transition-colors glass-card"
      >
        Reset Layout
      </button>

      <Layout
        ref={layoutRef}
        model={model}
        factory={factory}
        onModelChange={onModelChange}
        supportsPopout={true}
        popoutURL="/popout"
      />
    </div>
  );
}

// Make sure ClientChatBox component exists and is imported correctly
// Example placeholder:
// const ClientChatBox = ({ onSend }: { onSend: (msg: string) => void; inSidebar: boolean }) => {
//   const [input, setInput] = useState('');
//   const handleSend = () => {
//     if (input.trim()) {
//       onSend(input.trim());
//       setInput('');
//     }
//   };
//   return (
//     <div className="flex">
//       <input
//         type="text
