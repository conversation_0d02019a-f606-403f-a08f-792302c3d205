"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[110],{47110:(e,t,s)=>{s.d(t,{k:()=>g,Y:()=>u});var o=s(95155),n=s(12115),a=s(35695);let c="wss://localhost:8000/ws";class l{connect(e){var t;if((null==(t=this.socket)?void 0:t.readyState)===WebSocket.OPEN)return void console.log("WebSocket already connected");try{this.notifyStatusChange("connecting");let t=e?"".concat(c,"/").concat(e):c;console.log("Connecting WebSocket to",t),this.socket=new WebSocket(t),this.socket.onopen=this.handleOpen.bind(this),this.socket.onmessage=this.handleMessage.bind(this),this.socket.onclose=this.handleClose.bind(this),this.socket.onerror=this.handleError.bind(this)}catch(e){console.error("Failed to connect to WebSocket:",e),this.notifyStatusChange("error"),this.attemptReconnect()}}disconnect(){this.socket&&(this.socket.close(),this.socket=null),this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.notifyStatusChange("disconnected")}sendMessage(e){var t;console.log("[DEBUG] Sending WebSocket message:",e),(null==(t=this.socket)?void 0:t.readyState)===WebSocket.OPEN?this.socket.send(JSON.stringify(e)):(console.log("WebSocket not connected, queueing message"),this.messageQueue.push(e),this.connect())}onMessage(e){this.onMessageCallback=e}onStatusChange(e){this.onStatusChangeCallback=e}handleOpen(){for(console.log("WebSocket connected"),this.reconnectAttempts=0,this.notifyStatusChange("connected");this.messageQueue.length>0;){let e=this.messageQueue.shift();e&&this.sendMessage(e)}}handleMessage(e){try{console.log("[DEBUG] Raw WebSocket message received:",e.data);let t=JSON.parse(e.data);console.log("[DEBUG] Parsed WebSocket message:",t),this.onMessageCallback&&this.onMessageCallback(t)}catch(e){console.error("Error parsing WebSocket message:",e)}}handleClose(e){console.log("WebSocket closed: ".concat(e.code," ").concat(e.reason)),this.socket=null,this.notifyStatusChange("disconnected"),this.attemptReconnect()}handleError(e){var t;console.error("WebSocket error:",e),this.notifyStatusChange("error"),null==(t=this.socket)||t.close()}attemptReconnect(){if(this.reconnectAttempts>=this.maxReconnectAttempts)return void console.log("Max reconnect attempts reached");let e=Math.min(1e3*Math.pow(2,this.reconnectAttempts),3e4);console.log("Attempting to reconnect in ".concat(e,"ms")),this.reconnectTimeout=setTimeout(()=>{this.reconnectAttempts++,this.connect()},e)}notifyStatusChange(e){this.onStatusChangeCallback&&this.onStatusChangeCallback(e)}constructor(){this.socket=null,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectTimeout=null,this.messageQueue=[],this.onMessageCallback=null,this.onStatusChangeCallback=null,this.notifyStatusChange("disconnected")}}let i=new l;async function h(e){let t=new FormData;t.append("file",e);let s=await fetch("".concat("http://localhost:8000","/upload"),{method:"POST",body:t});if(!s.ok)throw Error("Upload failed: ".concat(s.statusText));return s.json()}let r=(0,n.createContext)(void 0);function g(e){let{children:t}=e,s=(0,a.useRouter)(),[c,l]=(0,n.useState)([]),[g,u]=(0,n.useState)(""),[d,m]=(0,n.useState)(!1),[y,S]=(0,n.useState)("disconnected"),[p,x]=(0,n.useState)(null),[f,E]=(0,n.useState)(null),[k,C]=(0,n.useState)({}),[U,w]=(0,n.useState)([]),[D,b]=(0,n.useState)([]),[B,G]=(0,n.useState)([]),[v,_]=(0,n.useState)(""),[W,M]=(0,n.useState)(null);(0,n.useEffect)(()=>{console.log("[DEBUG] ChatContext mounted"),i.onStatusChange(e=>{console.log("[DEBUG] WebSocket status changed:",e),S(e)}),i.onMessage(e=>{console.log("[DEBUG] WebSocket message received in context:",e),A(e)});{let e=window.location.pathname.split("/");if(e.length>2&&"chats"===e[1]){let t=e[2];console.log("[DEBUG] Found slug in URL:",t),t&&"new-chat"!==t?(M(t),i.connect(t)):i.connect()}else i.connect()}return()=>{console.log("[DEBUG] ChatContext unmounting, disconnecting WebSocket"),i.disconnect()}},[]);let A=e=>{if(console.log("[DEBUG] WebSocket response received:",e),console.log("[DEBUG] Response fields:",{hasMessage:!!e.message,hasThought:!!e.thought,hasChart:!!e.chart,hasCausality:!!e.causality,hasInsights:!!e.insights,hasExternalContexts:!!e.external_contexts,hasHistory:!!e.history}),e.slug){console.log("[DEBUG] Slug received:",e.slug),M(e.slug);{console.log("[DEBUG] Updating URL with slug:",e.slug);let t=window.location.pathname;"/"===t||"/chat"===t?(console.log("[DEBUG] Redirecting to chat page with slug:",e.slug),s.push("/chats/".concat(e.slug))):window.history.pushState({},"","/chats/".concat(e.slug))}}if(e.history&&Array.isArray(e.history)){console.log("[DEBUG] Chat history received:",e.history),e.history.forEach((e,t)=>{console.log("[DEBUG] History message ".concat(t,":"),{type:e.type,hasChart:!!e.chart,hasCausality:!!e.causality,hasInsights:!!e.insights,hasExternalContexts:!!e.external_contexts})}),l(e.history);let t=e.history.filter(e=>"ai"===e.type);if(t.length>0){let e=t[t.length-1];console.log("[DEBUG] Latest AI message from history:",e),e.chart&&(console.log("[DEBUG] Setting chart data from history:",e.chart),x(e.chart)),e.causality?(console.log("[DEBUG] Setting causality data from history:",e.causality),E(e.causality)):console.log("[DEBUG] No causality data in latest message"),e.insights?(console.log("[DEBUG] Setting insights from history:",e.insights),_(e.insights)):console.log("[DEBUG] No insights data in latest message"),e.external_contexts?(console.log("[DEBUG] Setting external contexts from history:",e.external_contexts),b(e.external_contexts.map((e,t)=>({...e,id:e.url+"-"+t,favicon:"https://www.google.com/s2/favicons?domain=".concat(new URL(e.url).hostname),isSelected:!0})))):console.log("[DEBUG] No external contexts in latest message")}else console.log("[DEBUG] No AI messages found in history")}if(e.thought)console.log("[DEBUG] Processing thought:",e.thought),u(e.thought),m(!0);else if(e.message){if(console.log("[DEBUG] Processing message:",e.message),e.message){let t={type:"ai",content:e.message,timestamp:new Date().toISOString(),status:e.status||"success"};e.chart&&(t.chart=e.chart),e.causality&&(t.causality=e.causality),e.insights&&(t.insights=e.insights),e.external_contexts&&(t.external_contexts=e.external_contexts),e.thought&&(t.thought=e.thought),l(e=>[...e,t]),e.chart&&x(e.chart),e.causality&&E(e.causality),e.insights&&_(e.insights||""),e.external_contexts&&b(e.external_contexts.map((e,t)=>({...e,id:e.url+"-"+t,favicon:"https://www.google.com/s2/favicons?domain=".concat(new URL(e.url).hostname),isSelected:!0})))}m(!1),u("")}else console.log("[DEBUG] No message or thought in response:",e);"chart"in e&&(console.log("[DEBUG] Chart data received:",e.chart),x(e.chart)),"causality"in e&&(console.log("[DEBUG] Causality data received:",e.causality),E(e.causality)),"external_contexts"in e&&e.external_contexts?(console.log("[DEBUG] External contexts received:",e.external_contexts),b(e.external_contexts.map((e,t)=>({...e,id:e.url+"-"+t,favicon:"https://www.google.com/s2/favicons?domain=".concat(new URL(e.url).hostname),isSelected:!0})))):"external_contexts"in e&&null===e.external_contexts&&b([]),"insights"in e&&(console.log("[DEBUG] Insights received:",e.insights),_(e.insights||""))},R=async e=>{try{let t=await h(e);return t.filename&&w(s=>[...s,{name:e.name,path:t.filename}]),t.filename}catch(e){throw console.error("Error uploading file:",e),e}};return(0,o.jsx)(r.Provider,{value:{messages:c,thinking:g,isThinking:d,connectionStatus:y,chartData:p,causalityData:f,chartSelection:k,uploadedFiles:U,externalContexts:D,selectedExternalContexts:B,insights:v,currentSlug:W,sendMessage:e=>{l(t=>[...t,{type:"user",content:e}]),m(!0),u("Analyzing query...");let t={message:e};U.length>0&&(t.files_path=U.map(e=>e.path));let s={};(p||(null==k?void 0:k.point)||(null==k?void 0:k.range))&&(s.chart={data:p},k&&(s.chart.selection=k),console.log("[DEBUG] Adding chart context to message:",s.chart)),Object.keys(s).length>0&&(t.model_context=s),B.length>0&&(t.external_contexts=B),i.sendMessage(t)},uploadFile:R,removeUploadedFile:e=>{w(t=>t.filter(t=>t.name!==e))},clearMessages:()=>{l([]),u(""),m(!1),x(null)},clearUploadedFiles:()=>{w([])},setChartSelection:C,setSelectedExternalContexts:G,connectWithSlug:e=>{console.log("[DEBUG] Connecting with slug:",e),W!==e?(M(e),i.connect(e)):console.log("[DEBUG] Already connected with slug:",e)}},children:t})}function u(){let e=(0,n.useContext)(r);if(void 0===e)throw Error("useChat must be used within a ChatProvider");return e}}}]);