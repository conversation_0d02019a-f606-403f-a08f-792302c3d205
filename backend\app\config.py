import os
from dotenv import load_dotenv
from pathlib import Path

load_dotenv()

# API Keys
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
NIXTLA_API_KEY = os.getenv("NIXTLA_API_KEY", "")

# Model Settings
LLM_MODEL = "gpt-4o-mini"  # Using gpt-4o-mini

# File Paths
BASE_DIR = Path(__file__).parent.parent  # Go up one level from app directory
SALES_DATA_PATH = str(BASE_DIR / "sales_train_evaluation.csv") 