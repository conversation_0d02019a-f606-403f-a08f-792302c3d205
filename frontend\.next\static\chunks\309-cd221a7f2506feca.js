"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[309],{6654:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"useMergedRef",{enumerable:!0,get:function(){return s}});let n=i(12115);function s(t,e){let i=(0,n.useRef)(null),s=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let t=i.current;t&&(i.current=null,t());let e=s.current;e&&(s.current=null,e())}else t&&(i.current=r(t,n)),e&&(s.current=r(e,n))},[t,e])}function r(t,e){if("function"!=typeof t)return t.current=e,()=>{t.current=null};{let i=t(e);return"function"==typeof i?i:()=>t(null)}}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},7471:(t,e,i)=>{i.d(e,{Q:()=>n});let n=(0,i(12115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},14905:(t,e,i)=>{i.d(e,{xQ:()=>r});var n=i(12115),s=i(50430);function r(t=!0){let e=(0,n.useContext)(s.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:a,register:o}=e,l=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return o(l)},[t]);let u=(0,n.useCallback)(()=>t&&a&&a(l),[l,a,t]);return!i&&a?[!1,u]:[!0]}},16997:(t,e,i)=>{let n;function s(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function r(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function a(t,e,i,n){if("function"==typeof e){let[s,a]=r(n);e=e(void 0!==i?i:t.custom,s,a)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[s,a]=r(n);e=e(void 0!==i?i:t.custom,s,a)}return e}function o(t,e,i){let n=t.getProps();return a(n,e,void 0!==i?i:n.custom,t)}i.d(e,{P:()=>rS});let l=t=>Array.isArray(t),u=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],h={value:null,addProjectionMetrics:null},d={};function c(t,e){let i=!1,n=!0,s={delta:0,timestamp:0,isProcessing:!1},r=()=>i=!0,a=u.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,s=!1,r=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){a.has(e)&&(d.schedule(e),t()),l++,e(o)}let d={schedule:(t,e=!1,r=!1)=>{let o=r&&s?i:n;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{n.delete(t),a.delete(t)},process:t=>{if(o=t,s){r=!0;return}s=!0,[i,n]=[n,i],i.forEach(u),e&&h.value&&h.value.frameloop[e].push(l),l=0,i.clear(),s=!1,r&&(r=!1,d.process(t))}};return d}(r,e?i:void 0),t),{}),{setup:o,read:l,resolveKeyframes:c,preUpdate:p,update:m,preRender:f,render:y,postRender:g}=a,v=()=>{let r=d.useManualTiming?s.timestamp:performance.now();i=!1,d.useManualTiming||(s.delta=n?1e3/60:Math.max(Math.min(r-s.timestamp,40),1)),s.timestamp=r,s.isProcessing=!0,o.process(s),l.process(s),c.process(s),p.process(s),m.process(s),f.process(s),y.process(s),g.process(s),s.isProcessing=!1,i&&e&&(n=!1,t(v))},x=()=>{i=!0,n=!0,s.isProcessing||t(v)};return{schedule:u.reduce((t,e)=>{let n=a[e];return t[e]=(t,e=!1,s=!1)=>(i||x(),n.schedule(t,e,s)),t},{}),cancel:t=>{for(let e=0;e<u.length;e++)a[u[e]].cancel(t)},state:s,steps:a}}let p=t=>t,{schedule:m,cancel:f,state:y,steps:g}=c("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:p,!0);function v(){n=void 0}let x={now:()=>(void 0===n&&x.set(y.isProcessing||d.useManualTiming?y.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(v)}};function T(t,e){-1===t.indexOf(e)&&t.push(e)}function w(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class b{constructor(){this.subscriptions=[]}add(t){return T(this.subscriptions,t),()=>w(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let s=0;s<n;s++){let n=this.subscriptions[s];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let P=t=>!isNaN(parseFloat(t)),S={current:void 0};class A{constructor(t,e={}){this.version="__VERSION__",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=x.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=x.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=P(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new b);let i=this.events[t].add(e);return"change"===t?()=>{i(),m.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return S.current&&S.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=x.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function M(t,e){return new A(t,e)}let V=t=>!!(t&&t.getVelocity);function E(t,e){let i=t.getValue("willChange");if(V(i)&&i.add)return i.add(e);if(!i&&d.WillChange){let i=new d.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let D=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),C="data-"+D("framerAppearId"),k=t=>null!==t,R=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],j=new Set(R),L={type:"spring",stiffness:500,damping:25,restSpeed:10},F=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),B={type:"keyframes",duration:.8},O={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},I=(t,{keyframes:e})=>e.length>2?B:j.has(t)?t.startsWith("scale")?F(e[1]):L:O;function U(t,e){return t?.[e]??t?.default??t}let N=t=>1e3*t,$=t=>t/1e3,W={layout:0,mainThread:0,waapi:0},Y=t=>e=>"string"==typeof e&&e.startsWith(t),z=Y("--"),H=Y("var(--"),X=t=>!!H(t)&&_.test(t.split("/*")[0].trim()),_=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,K=(t,e,i)=>i>e?e:i<t?t:i,q={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},G={...q,transform:t=>K(0,1,t)},Z={...q,default:1},Q=t=>Math.round(1e5*t)/1e5,J=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tt=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,te=(t,e)=>i=>!!("string"==typeof i&&tt.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),ti=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[s,r,a,o]=n.match(J);return{[t]:parseFloat(s),[e]:parseFloat(r),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},tn=t=>K(0,255,t),ts={...q,transform:t=>Math.round(tn(t))},tr={test:te("rgb","red"),parse:ti("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+ts.transform(t)+", "+ts.transform(e)+", "+ts.transform(i)+", "+Q(G.transform(n))+")"},ta={test:te("#"),parse:function(t){let e="",i="",n="",s="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),s=t.substring(4,5),e+=e,i+=i,n+=n,s+=s),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:s?parseInt(s,16)/255:1}},transform:tr.transform},to=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tl=to("deg"),tu=to("%"),th=to("px"),td=to("vh"),tc=to("vw"),tp={...tu,parse:t=>tu.parse(t)/100,transform:t=>tu.transform(100*t)},tm={test:te("hsl","hue"),parse:ti("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+tu.transform(Q(e))+", "+tu.transform(Q(i))+", "+Q(G.transform(n))+")"},tf={test:t=>tr.test(t)||ta.test(t)||tm.test(t),parse:t=>tr.test(t)?tr.parse(t):tm.test(t)?tm.parse(t):ta.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tr.transform(t):tm.transform(t)},ty=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tg="number",tv="color",tx=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tT(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},s=[],r=0,a=e.replace(tx,t=>(tf.test(t)?(n.color.push(r),s.push(tv),i.push(tf.parse(t))):t.startsWith("var(")?(n.var.push(r),s.push("var"),i.push(t)):(n.number.push(r),s.push(tg),i.push(parseFloat(t))),++r,"${}")).split("${}");return{values:i,split:a,indexes:n,types:s}}function tw(t){return tT(t).values}function tb(t){let{split:e,types:i}=tT(t),n=e.length;return t=>{let s="";for(let r=0;r<n;r++)if(s+=e[r],void 0!==t[r]){let e=i[r];e===tg?s+=Q(t[r]):e===tv?s+=tf.transform(t[r]):s+=t[r]}return s}}let tP=t=>"number"==typeof t?0:t,tS={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(J)?.length||0)+(t.match(ty)?.length||0)>0},parse:tw,createTransformer:tb,getAnimatableNone:function(t){let e=tw(t);return tb(t)(e.map(tP))}};function tA(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tM(t,e){return i=>i>0?e:t}let tV=(t,e,i)=>t+(e-t)*i,tE=()=>{},tD=()=>{},tC=(t,e,i)=>{let n=t*t,s=i*(e*e-n)+n;return s<0?0:Math.sqrt(s)},tk=[ta,tr,tm],tR=t=>tk.find(e=>e.test(t));function tj(t){let e=tR(t);if(tE(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tm&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let s=0,r=0,a=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,o=2*i-n;s=tA(o,n,t+1/3),r=tA(o,n,t),a=tA(o,n,t-1/3)}else s=r=a=i;return{red:Math.round(255*s),green:Math.round(255*r),blue:Math.round(255*a),alpha:n}}(i)),i}let tL=(t,e)=>{let i=tj(t),n=tj(e);if(!i||!n)return tM(t,e);let s={...i};return t=>(s.red=tC(i.red,n.red,t),s.green=tC(i.green,n.green,t),s.blue=tC(i.blue,n.blue,t),s.alpha=tV(i.alpha,n.alpha,t),tr.transform(s))},tF=new Set(["none","hidden"]),tB=(t,e)=>i=>e(t(i)),tO=(...t)=>t.reduce(tB);function tI(t,e){return i=>tV(t,e,i)}function tU(t){return"number"==typeof t?tI:"string"==typeof t?X(t)?tM:tf.test(t)?tL:tW:Array.isArray(t)?tN:"object"==typeof t?tf.test(t)?tL:t$:tM}function tN(t,e){let i=[...t],n=i.length,s=t.map((t,i)=>tU(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=s[e](t);return i}}function t$(t,e){let i={...t,...e},n={};for(let s in i)void 0!==t[s]&&void 0!==e[s]&&(n[s]=tU(t[s])(t[s],e[s]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let tW=(t,e)=>{let i=tS.createTransformer(e),n=tT(t),s=tT(e);return n.indexes.var.length===s.indexes.var.length&&n.indexes.color.length===s.indexes.color.length&&n.indexes.number.length>=s.indexes.number.length?tF.has(t)&&!s.values.length||tF.has(e)&&!n.values.length?function(t,e){return tF.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):tO(tN(function(t,e){let i=[],n={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){let r=e.types[s],a=t.indexes[r][n[r]],o=t.values[a]??0;i[s]=o,n[r]++}return i}(n,s),s.values),i):(tE(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tM(t,e))};function tY(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tV(t,e,i):tU(t)(t,e)}let tz=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>m.update(e,t),stop:()=>f(e),now:()=>y.isProcessing?y.timestamp:x.now()}},tH=(t,e,i=10)=>{let n="",s=Math.max(Math.round(e/i),2);for(let e=0;e<s;e++)n+=t(e/(s-1))+", ";return`linear(${n.substring(0,n.length-2)})`};function tX(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function t_(t,e,i){var n,s;let r=Math.max(e-5,0);return n=i-t(r),(s=e-r)?1e3/s*n:0}let tK={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tq(t,e){return t*Math.sqrt(1-e*e)}let tG=["duration","bounce"],tZ=["stiffness","damping","mass"];function tQ(t,e){return e.some(e=>void 0!==t[e])}function tJ(t=tK.visualDuration,e=tK.bounce){let i,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:s,restDelta:r}=n,a=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:tK.velocity,stiffness:tK.stiffness,damping:tK.damping,mass:tK.mass,isResolvedFromDuration:!1,...t};if(!tQ(t,tZ)&&tQ(t,tG))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,s=2*K(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:tK.mass,stiffness:n,damping:s}}else{let i=function({duration:t=tK.duration,bounce:e=tK.bounce,velocity:i=tK.velocity,mass:n=tK.mass}){let s,r;tE(t<=N(tK.maxDuration),"Spring duration must be 10 seconds or less");let a=1-e;a=K(tK.minDamping,tK.maxDamping,a),t=K(tK.minDuration,tK.maxDuration,$(t)),a<1?(s=e=>{let n=e*a,s=n*t;return .001-(n-i)/tq(e,a)*Math.exp(-s)},r=e=>{let n=e*a*t,r=Math.pow(a,2)*Math.pow(e,2)*t,o=Math.exp(-n),l=tq(Math.pow(e,2),a);return(n*i+i-r)*o*(-s(e)+.001>0?-1:1)/l}):(s=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let o=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(s,r,5/t);if(t=N(t),isNaN(o))return{stiffness:tK.stiffness,damping:tK.damping,duration:t};{let e=Math.pow(o,2)*n;return{stiffness:e,damping:2*a*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:tK.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-$(n.velocity||0)}),f=p||0,y=h/(2*Math.sqrt(u*d)),g=o-a,v=$(Math.sqrt(u/d)),x=5>Math.abs(g);if(s||(s=x?tK.restSpeed.granular:tK.restSpeed.default),r||(r=x?tK.restDelta.granular:tK.restDelta.default),y<1){let t=tq(v,y);i=e=>o-Math.exp(-y*v*e)*((f+y*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===y)i=t=>o-Math.exp(-v*t)*(g+(f+v*g)*t);else{let t=v*Math.sqrt(y*y-1);i=e=>{let i=Math.exp(-y*v*e),n=Math.min(t*e,300);return o-i*((f+y*v*g)*Math.sinh(n)+t*g*Math.cosh(n))/t}}let T={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let n=0===t?f:0;y<1&&(n=0===t?N(f):t_(i,t,e));let a=Math.abs(o-e)<=r;l.done=Math.abs(n)<=s&&a}return l.value=l.done?o:e,l},toString:()=>{let t=Math.min(tX(T),2e4),e=tH(e=>T.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return T}function t0({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:s=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:h}){let d,c,p=t[0],m={done:!1,value:p},f=t=>void 0!==o&&t<o||void 0!==l&&t>l,y=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l,g=i*e,v=p+g,x=void 0===a?v:a(v);x!==v&&(g=x-p);let T=t=>-g*Math.exp(-t/n),w=t=>x+T(t),b=t=>{let e=T(t),i=w(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},P=t=>{f(m.value)&&(d=t,c=tJ({keyframes:[m.value,y(m.value)],velocity:t_(w,t,m.value),damping:s,stiffness:r,restDelta:u,restSpeed:h}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,b(t),P(t)),void 0!==d&&t>=d)?c.next(t-d):(e||b(t),m)}}}tJ.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),s=Math.min(tX(n),2e4);return{type:"keyframes",ease:t=>n.next(s*t).value/e,duration:$(s)}}(t,100,tJ);return t.ease=e.ease,t.duration=N(e.duration),t.type="keyframes",t};let t1=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n},t5=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function t2(t,e,i,n){if(t===e&&i===n)return p;let s=e=>(function(t,e,i,n,s){let r,a,o=0;do(r=t5(a=e+(i-e)/2,n,s)-t)>0?i=a:e=a;while(Math.abs(r)>1e-7&&++o<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:t5(s(t),e,n)}let t3=t2(.42,0,1,1),t9=t2(0,0,.58,1),t4=t2(.42,0,.58,1),t6=t=>Array.isArray(t)&&"number"!=typeof t[0],t8=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t7=t=>e=>1-t(1-e),et=t2(.33,1.53,.69,.99),ee=t7(et),ei=t8(ee),en=t=>(t*=2)<1?.5*ee(t):.5*(2-Math.pow(2,-10*(t-1))),es=t=>1-Math.sin(Math.acos(t)),er=t7(es),ea=t8(es),eo=t=>Array.isArray(t)&&"number"==typeof t[0],el={linear:p,easeIn:t3,easeInOut:t4,easeOut:t9,circIn:es,circInOut:ea,circOut:er,backIn:ee,backInOut:ei,backOut:et,anticipate:en},eu=t=>"string"==typeof t,eh=t=>{if(eo(t)){tD(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,s]=t;return t2(e,i,n,s)}return eu(t)?(tD(void 0!==el[t],`Invalid easing type '${t}'`),el[t]):t};function ed({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){var s;let r=t6(n)?n.map(eh):eh(n),a={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:n,mixer:s}={}){let r=t.length;if(tD(r===e.length,"Both input and output ranges must be the same length"),1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];let a=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,i){let n=[],s=i||d.mix||tY,r=t.length-1;for(let i=0;i<r;i++){let r=s(t[i],t[i+1]);e&&(r=tO(Array.isArray(e)?e[i]||p:e,r)),n.push(r)}return n}(e,n,s),l=o.length,u=i=>{if(a&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let s=t1(t[n],t[n+1],i);return o[n](s)};return i?e=>u(K(t[0],t[r-1],e)):u}((s=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let s=t1(0,e,n);t.push(tV(i,1,s))}}(e,t.length-1),e}(e),s.map(e=>e*t)),e,{ease:Array.isArray(r)?r:e.map(()=>r||t4).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(a.value=o(e),a.done=e>=t,a)}}let ec=t=>null!==t;function ep(t,{repeat:e,repeatType:i="loop"},n,s=1){let r=t.filter(ec),a=s<0||e&&"loop"!==i&&e%2==1?0:r.length-1;return a&&void 0!==n?n:r[a]}let em={decay:t0,inertia:t0,tween:ed,keyframes:ed,spring:tJ};function ef(t){"string"==typeof t.type&&(t.type=em[t.type])}class ey{constructor(){this.count=0,this.updateFinished()}get finished(){return this._finished}updateFinished(){this.count++,this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let eg=t=>t/100;class ev extends ey{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(t=!0)=>{if(t){let{motionValue:t}=this.options;t&&t.updatedAt!==x.now()&&this.tick(x.now())}if(this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:e}=this.options;e&&e()},W.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;ef(t);let{type:e=ed,repeat:i=0,repeatDelay:n=0,repeatType:s,velocity:r=0}=t,{keyframes:a}=t,o=e||ed;o!==ed&&"number"!=typeof a[0]&&(this.mixKeyframes=tO(eg,tY(a[0],a[1])),a=[0,100]);let l=o({...t,keyframes:a});"mirror"===s&&(this.mirroredGenerator=o({...t,keyframes:[...a].reverse(),velocity:-r})),null===l.calculatedDuration&&(l.calculatedDuration=tX(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:s,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let y=this.currentTime-l*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?y<0:y>n;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,x=i;if(h){let t=Math.min(this.currentTime,n)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/a)):"mirror"===d&&(x=r)),v=K(0,1,i)*a}let T=g?{done:!1,value:u[0]}:x.next(v);s&&(T.value=s(T.value));let{done:w}=T;g||null===o||(w=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let b=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return b&&p!==t0&&(T.value=ep(u,this.options,f,this.speed)),m&&m(T.value),b&&this.finish(),T}then(t,e){return this.finished.then(t,e)}get duration(){return $(this.calculatedDuration)}get time(){return $(this.currentTime)}set time(t){t=N(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(x.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=$(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tz,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=i??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(x.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown()}teardown(){this.notifyFinished(),this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,W.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let ex=t=>180*t/Math.PI,eT=t=>eb(ex(Math.atan2(t[1],t[0]))),ew={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:eT,rotateZ:eT,skewX:t=>ex(Math.atan(t[1])),skewY:t=>ex(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},eb=t=>((t%=360)<0&&(t+=360),t),eP=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),eS=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),eA={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:eP,scaleY:eS,scale:t=>(eP(t)+eS(t))/2,rotateX:t=>eb(ex(Math.atan2(t[6],t[5]))),rotateY:t=>eb(ex(Math.atan2(-t[2],t[0]))),rotateZ:eT,rotate:eT,skewX:t=>ex(Math.atan(t[4])),skewY:t=>ex(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function eM(t){return+!!t.includes("scale")}function eV(t,e){let i,n;if(!t||"none"===t)return eM(e);let s=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(s)i=eA,n=s;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=ew,n=e}if(!n)return eM(e);let r=i[e],a=n[1].split(",").map(eD);return"function"==typeof r?r(a):a[r]}let eE=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return eV(i,e)};function eD(t){return parseFloat(t.trim())}let eC=t=>t===q||t===th,ek=new Set(["x","y","z"]),eR=R.filter(t=>!ek.has(t)),ej={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eV(e,"x"),y:(t,{transform:e})=>eV(e,"y")};ej.translateX=ej.x,ej.translateY=ej.y;let eL=new Set,eF=!1,eB=!1,eO=!1;function eI(){if(eB){let t=Array.from(eL).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return eR.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eB=!1,eF=!1,eL.forEach(t=>t.complete(eO)),eL.clear()}function eU(){eL.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eB=!0)})}class eN{constructor(t,e,i,n,s,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=s,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(eL.add(this),eF||(eF=!0,m.read(eU),m.resolveKeyframes(eI))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let s=n?.get(),r=t[t.length-1];if(void 0!==s)t[0]=s;else if(i&&e){let n=i.readValue(e,r);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=r),n&&void 0===s&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eL.delete(this)}cancel(){"scheduled"===this.state&&(eL.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let e$=t=>t.startsWith("--");function eW(t){let e;return()=>(void 0===e&&(e=t()),e)}let eY=eW(()=>void 0!==window.ScrollTimeline),ez={},eH=function(t,e){let i=eW(t);return()=>ez[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eX=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,e_={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eX([0,.65,.55,1]),circOut:eX([.55,0,1,.45]),backIn:eX([.31,.01,.66,-.59]),backOut:eX([.33,1.53,.69,.99])};function eK(t){return"function"==typeof t&&"applyToOptions"in t}class eq extends ey{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:s,allowFlatten:r=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=!!s,this.allowFlatten=r,this.options=t,tD("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return eK(t)&&eH()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:s=300,repeat:r=0,repeatType:a="loop",ease:o="easeOut",times:l}={},u){let d={[e]:i};l&&(d.offset=l);let c=function t(e,i){if(e)return"function"==typeof e?eH()?tH(e,i):"ease-out":eo(e)?eX(e):Array.isArray(e)?e.map(e=>t(e,i)||e_.easeOut):e_[e]}(o,s);Array.isArray(c)&&(d.easing=c),h.value&&W.waapi++;let p={delay:n,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:r+1,direction:"reverse"===a?"alternate":"normal"};u&&(p.pseudoElement=u);let m=t.animate(d,p);return h.value&&m.finished.finally(()=>{W.waapi--}),m}(e,i,n,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){let t=ep(n,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){e$(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}o?.(),this.notifyFinished()},this.animation.oncancel=()=>this.notifyFinished()}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return $(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return $(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=N(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eY())?(this.animation.timeline=t,p):e(this)}}let eG={anticipate:en,backInOut:ei,circInOut:ea};class eZ extends eq{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eG&&(t.ease=eG[t.ease])}(t),ef(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:s,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let a=new ev({...r,autoplay:!1}),o=N(this.finishedTime??this.time);e.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}let eQ=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tS.test(t)||"0"===t)&&!t.startsWith("url(")),eJ=new Set(["opacity","clipPath","filter","transform"]),e0=eW(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class e1 extends ey{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:r="loop",keyframes:a,name:o,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=x.now();let d={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:s,repeatType:r,name:o,motionValue:l,element:u,...h},c=u?.KeyframeResolver||eN;this.keyframeResolver=new c(a,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:s,type:r,velocity:a,delay:o,isHandoff:l,onUpdate:u}=i;this.resolvedAt=x.now(),!function(t,e,i,n){let s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],a=eQ(s,e),o=eQ(r,e);return tE(a===o,`You are trying to animate ${e} from "${s}" to "${r}". ${s} is not an animatable value - to enable this animation set ${s} to a value animatable to ${r} via the \`style\` property.`),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||eK(i))&&n)}(t,s,r,a)&&((d.instantAnimations||!o)&&u?.(ep(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let h={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},c=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:s,damping:r,type:a}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return e0()&&i&&eJ.has(i)&&("transform"!==i||!l)&&!o&&!n&&"mirror"!==s&&0!==r&&"inertia"!==a}(h)?new eZ({...h,element:h.motionValue.owner.current}):new ev(h);c.finished.then(()=>this.notifyFinished()).catch(p),this.pendingTimeline&&(this.stopTimeline=c.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=c}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eO=!0,eU(),eI(),eO=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e5=(t,e,i,n={},s,r)=>a=>{let o=U(n,t)||{},l=o.delay||n.delay||0,{elapsed:u=0}=n;u-=N(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...o,delay:-u,onUpdate:t=>{e.set(t),o.onUpdate&&o.onUpdate(t)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:t,motionValue:e,element:r?void 0:s};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:s,repeat:r,repeatType:a,repeatDelay:o,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(o)&&Object.assign(h,I(t,h)),h.duration&&(h.duration=N(h.duration)),h.repeatDelay&&(h.repeatDelay=N(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let c=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(c=!0)),(d.instantAnimations||d.skipAnimations)&&(c=!0,h.duration=0,h.delay=0),h.allowFlatten=!o.type&&!o.ease,c&&!r&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let s=t.filter(k),r=e&&"loop"!==i&&e%2==1?0:s.length-1;return s[r]}(h.keyframes,o);if(void 0!==t)return void m.update(()=>{h.onUpdate(t),h.onComplete()})}return o.isSync?new ev(h):new e1(h)},e2=new Set(["width","height","top","left","right","bottom",...R]);function e3(t,e,{delay:i=0,transitionOverride:n,type:s}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:a,...u}=e;n&&(r=n);let h=[],d=s&&t.animationState&&t.animationState.getState()[s];for(let e in u){let n=t.getValue(e,t.latestValues[e]??null),s=u[e];if(void 0===s||d&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(d,e))continue;let a={delay:i,...U(r||{},e)},o=n.get();if(void 0!==o&&!n.isAnimating&&!Array.isArray(s)&&s===o&&!a.velocity)continue;let l=!1;if(window.MotionHandoffAnimation){let i=t.props[C];if(i){let t=window.MotionHandoffAnimation(i,e,m);null!==t&&(a.startTime=t,l=!0)}}E(t,e),n.start(e5(e,n,s,t.shouldReduceMotion&&e2.has(e)?{type:!1}:a,t,l));let c=n.animation;c&&h.push(c)}return a&&Promise.all(h).then(()=>{m.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:n={},...s}=o(t,e)||{};for(let e in s={...s,...i}){var r;let i=l(r=s[e])?r[r.length-1]||0:r;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,M(i))}}(t,a)})}),h}function e9(t,e,i={}){let n=o(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(s=i.transitionOverride);let r=n?()=>Promise.all(e3(t,n,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:r=0,staggerChildren:a,staggerDirection:o}=s;return function(t,e,i=0,n=0,s=1,r){let a=[],o=(t.variantChildren.size-1)*n,l=1===s?(t=0)=>t*n:(t=0)=>o-t*n;return Array.from(t.variantChildren).sort(e4).forEach((t,n)=>{t.notify("AnimationStart",e),a.push(e9(t,e,{...r,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,r+n,a,o,i)}:()=>Promise.resolve(),{when:l}=s;if(!l)return Promise.all([r(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[r,a]:[a,r];return t().then(()=>e())}}function e4(t,e){return t.sortNodePosition(e)}function e6(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function e8(t){return"string"==typeof t||Array.isArray(t)}let e7=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],it=["initial",...e7],ie=it.length,ii=[...e7].reverse(),is=e7.length;function ir(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ia(){return{animate:ir(!0),whileInView:ir(),whileHover:ir(),whileTap:ir(),whileDrag:ir(),whileFocus:ir(),exit:ir()}}class io{constructor(t){this.isMounted=!1,this.node=t}update(){}}class il extends io{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>e9(t,e,i)));else if("string"==typeof e)n=e9(t,e,i);else{let s="function"==typeof e?o(t,e,i.custom):e;n=Promise.all(e3(t,s,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=ia(),n=!0,r=e=>(i,n)=>{let s=o(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(s){let{transition:t,transitionEnd:e,...n}=s;i={...i,...n,...e}}return i};function a(a){let{props:u}=t,h=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<ie;t++){let n=it[t],s=e.props[n];(e8(s)||!1===s)&&(i[n]=s)}return i}(t.parent)||{},d=[],c=new Set,p={},m=1/0;for(let e=0;e<is;e++){var f,y;let o=ii[e],g=i[o],v=void 0!==u[o]?u[o]:h[o],x=e8(v),T=o===a?g.isActive:null;!1===T&&(m=e);let w=v===h[o]&&v!==u[o]&&x;if(w&&n&&t.manuallyAnimateOnMount&&(w=!1),g.protectedKeys={...p},!g.isActive&&null===T||!v&&!g.prevProp||s(v)||"boolean"==typeof v)continue;let b=(f=g.prevProp,"string"==typeof(y=v)?y!==f:!!Array.isArray(y)&&!e6(y,f)),P=b||o===a&&g.isActive&&!w&&x||e>m&&x,S=!1,A=Array.isArray(v)?v:[v],M=A.reduce(r(o),{});!1===T&&(M={});let{prevResolvedValues:V={}}=g,E={...V,...M},D=e=>{P=!0,c.has(e)&&(S=!0,c.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in E){let e=M[t],i=V[t];if(p.hasOwnProperty(t))continue;let n=!1;(l(e)&&l(i)?e6(e,i):e===i)?void 0!==e&&c.has(t)?D(t):g.protectedKeys[t]=!0:null!=e?D(t):c.add(t)}g.prevProp=v,g.prevResolvedValues=M,g.isActive&&(p={...p,...M}),n&&t.blockInitialAnimation&&(P=!1);let C=!(w&&b)||S;P&&C&&d.push(...A.map(t=>({animation:t,options:{type:o}})))}if(c.size){let e={};if("boolean"!=typeof u.initial){let i=o(t,Array.isArray(u.initial)?u.initial[0]:u.initial);i&&i.transition&&(e.transition=i.transition)}c.forEach(i=>{let n=t.getBaseTarget(i),s=t.getValue(i);s&&(s.liveStyle=!0),e[i]=n??null}),d.push({animation:e})}let g=!!d.length;return n&&(!1===u.initial||u.initial===u.animate)&&!t.manuallyAnimateOnMount&&(g=!1),n=!1,g?e(d):Promise.resolve()}return{animateChanges:a,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let s=a(e);for(let t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=ia(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();s(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let iu=0;class ih extends io{constructor(){super(...arguments),this.id=iu++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}function id(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let ic=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function ip(t){return{point:{x:t.pageX,y:t.pageY}}}let im=t=>e=>ic(e)&&t(e,ip(e));function iy(t,e,i,n){return id(t,e,im(i),n)}function ig({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function iv(t){return t.max-t.min}function ix(t,e,i,n=.5){t.origin=n,t.originPoint=tV(e.min,e.max,t.origin),t.scale=iv(i)/iv(e),t.translate=tV(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iT(t,e,i,n){ix(t.x,e.x,i.x,n?n.originX:void 0),ix(t.y,e.y,i.y,n?n.originY:void 0)}function iw(t,e,i){t.min=i.min+e.min,t.max=t.min+iv(e)}function ib(t,e,i){t.min=e.min-i.min,t.max=t.min+iv(e)}function iP(t,e,i){ib(t.x,e.x,i.x),ib(t.y,e.y,i.y)}let iS=()=>({translate:0,scale:1,origin:0,originPoint:0}),iA=()=>({x:iS(),y:iS()}),iM=()=>({min:0,max:0}),iV=()=>({x:iM(),y:iM()});function iE(t){return[t("x"),t("y")]}function iD(t){return void 0===t||1===t}function iC({scale:t,scaleX:e,scaleY:i}){return!iD(t)||!iD(e)||!iD(i)}function ik(t){return iC(t)||iR(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iR(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function ij(t,e,i,n,s){return void 0!==s&&(t=n+s*(t-n)),n+i*(t-n)+e}function iL(t,e=0,i=1,n,s){t.min=ij(t.min,e,i,n,s),t.max=ij(t.max,e,i,n,s)}function iF(t,{x:e,y:i}){iL(t.x,e.translate,e.scale,e.originPoint),iL(t.y,i.translate,i.scale,i.originPoint)}function iB(t,e){t.min=t.min+e,t.max=t.max+e}function iO(t,e,i,n,s=.5){let r=tV(t.min,t.max,s);iL(t,e,i,r,n)}function iI(t,e){iO(t.x,e.x,e.scaleX,e.scale,e.originX),iO(t.y,e.y,e.scaleY,e.scale,e.originY)}function iU(t,e){return ig(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let iN=({current:t})=>t?t.ownerDocument.defaultView:null;function i$(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iW=(t,e)=>Math.abs(t-e);class iY{constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iX(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iW(t.x,e.x)**2+iW(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:n}=t,{timestamp:s}=y;this.history.push({...n,timestamp:s});let{onStart:r,onMove:a}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iz(e,this.transformPagePoint),m.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=iX("pointercancel"===t.type?this.lastMoveEventInfo:iz(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),n&&n(t,r)},!ic(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;let r=iz(ip(t),this.transformPagePoint),{point:a}=r,{timestamp:o}=y;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=e;l&&l(t,iX(r,this.history)),this.removeListeners=tO(iy(this.contextWindow,"pointermove",this.handlePointerMove),iy(this.contextWindow,"pointerup",this.handlePointerUp),iy(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),f(this.updatePoint)}}function iz(t,e){return e?{point:e(t.point)}:t}function iH(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iX({point:t},e){return{point:t,delta:iH(t,i_(e)),offset:iH(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,s=i_(t);for(;i>=0&&(n=t[i],!(s.timestamp-n.timestamp>N(.1)));)i--;if(!n)return{x:0,y:0};let r=$(s.timestamp-n.timestamp);if(0===r)return{x:0,y:0};let a={x:(s.x-n.x)/r,y:(s.y-n.y)/r};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function i_(t){return t[t.length-1]}function iK(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iq(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function iG(t,e,i){return{min:iZ(t,e),max:iZ(t,i)}}function iZ(t,e){return"number"==typeof t?t:t[e]||0}let iQ={x:!1,y:!1},iJ=new WeakMap;class i0{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iV(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new iY(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(ip(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:s}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(iQ[t])return null;else return iQ[t]=!0,()=>{iQ[t]=!1};return iQ.x||iQ.y?null:(iQ.x=iQ.y=!0,()=>{iQ.x=iQ.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iE(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tu.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=iv(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),s&&m.postRender(()=>s(t,e)),E(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:s,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(a),null!==this.currentDirection&&s&&s(this.currentDirection);return}this.updateAxis("x",e.point,a),this.updateAxis("y",e.point,a),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iE(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:iN(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:s}=this.getProps();s&&m.postRender(()=>s(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!i1(t,n,this.currentDirection))return;let s=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?tV(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?tV(i,t,n.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),s.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&i$(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:s}){return{x:iK(t.x,i,s),y:iK(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:iG(t,"left","right"),y:iG(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iE(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!i$(e))return!1;let n=e.current;tD(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:s}=this.visualElement;if(!s||!s.layout)return!1;let r=function(t,e,i){let n=iU(t,i),{scroll:s}=e;return s&&(iB(n.x,s.offset.x),iB(n.y,s.offset.y)),n}(n,s.root,this.visualElement.getTransformPagePoint()),a=(t=s.layout.layoutBox,{x:iq(t.x,r.x),y:iq(t.y,r.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(a));this.hasMutatedConstraints=!!t,t&&(a=ig(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:s,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(iE(a=>{if(!i1(a,e,this.currentDirection))return;let l=o&&o[a]||{};r&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[a]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return E(this.visualElement,t),i.start(e5(t,i,0,e,this.visualElement,!1))}stopAnimation(){iE(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iE(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iE(e=>{let{drag:i}=this.getProps();if(!i1(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,s=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:r}=n.layout.layoutBox[e];s.set(t[e]-tV(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!i$(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};iE(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=iv(t),s=iv(e);return s>n?i=t1(e.min,e.max-n,t.min):n>s&&(i=t1(t.min,t.max-s,e.min)),K(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iE(e=>{if(!i1(e,t,null))return;let i=this.getAxisMotionValue(e),{min:s,max:r}=this.constraints[e];i.set(tV(s,r,n[e]))})}addListeners(){if(!this.visualElement.current)return;iJ.set(this.visualElement,this);let t=iy(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();i$(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),m.read(e);let s=id(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iE(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),n(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:s=!1,dragElastic:r=.35,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:s,dragElastic:r,dragMomentum:a}}}function i1(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i5 extends io{constructor(t){super(t),this.removeGroupControls=p,this.removeListeners=p,this.controls=new i0(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||p}unmount(){this.removeGroupControls(),this.removeListeners()}}let i2=t=>(e,i)=>{t&&m.postRender(()=>t(e,i))};class i3 extends io{constructor(){super(...arguments),this.removePointerDownListener=p}onPointerDown(t){this.session=new iY(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iN(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:i2(t),onStart:i2(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&m.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=iy(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var i9,i4,i6=i(95155),i8=i(12115),i7=i(14905),nt=i(57728);let ne=(0,i8.createContext)({}),ni={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nn(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let ns={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!th.test(t))return t;else t=parseFloat(t);let i=nn(t,e.target.x),n=nn(t,e.target.y);return`${i}% ${n}%`}},nr={},{schedule:na}=c(queueMicrotask,!1);class no extends i8.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:s}=t;for(let t in nu)nr[t]=nu[t],z(t)&&(nr[t].isCSSVariable=!0);s&&(e.group&&e.group.add(s),i&&i.register&&n&&i.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),ni.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:s}=this.props,{projection:r}=i;return r&&(r.isPresent=s,n||t.layoutDependency!==e||void 0===e||t.isPresent!==s?r.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?r.promote():r.relegate()||m.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),na.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nl(t){let[e,i]=(0,i7.xQ)(),n=(0,i8.useContext)(nt.L);return(0,i6.jsx)(no,{...t,layoutGroup:n,switchLayoutGroup:(0,i8.useContext)(ne),isPresent:e,safeToRemove:i})}let nu={borderRadius:{...ns,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ns,borderTopRightRadius:ns,borderBottomLeftRadius:ns,borderBottomRightRadius:ns,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=tS.parse(t);if(n.length>5)return t;let s=tS.createTransformer(t),r=+("number"!=typeof n[0]),a=i.x.scale*e.x,o=i.y.scale*e.y;n[0+r]/=a,n[1+r]/=o;let l=tV(a,o,.5);return"number"==typeof n[2+r]&&(n[2+r]/=l),"number"==typeof n[3+r]&&(n[3+r]/=l),s(n)}}},nh=(t,e)=>t.depth-e.depth;class nd{constructor(){this.children=[],this.isDirty=!1}add(t){T(this.children,t),this.isDirty=!0}remove(t){w(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nh),this.isDirty=!1,this.children.forEach(t)}}function nc(t){return V(t)?t.get():t}let np=["TopLeft","TopRight","BottomLeft","BottomRight"],nm=np.length,nf=t=>"string"==typeof t?parseFloat(t):t,ny=t=>"number"==typeof t||th.test(t);function ng(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nv=nT(0,.5,er),nx=nT(.5,.95,p);function nT(t,e,i){return n=>n<t?0:n>e?1:i(t1(t,e,n))}function nw(t,e){t.min=e.min,t.max=e.max}function nb(t,e){nw(t.x,e.x),nw(t.y,e.y)}function nP(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nS(t,e,i,n,s){return t-=e,t=n+1/i*(t-n),void 0!==s&&(t=n+1/s*(t-n)),t}function nA(t,e,[i,n,s],r,a){!function(t,e=0,i=1,n=.5,s,r=t,a=t){if(tu.test(e)&&(e=parseFloat(e),e=tV(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let o=tV(r.min,r.max,n);t===r&&(o-=e),t.min=nS(t.min,e,i,o,s),t.max=nS(t.max,e,i,o,s)}(t,e[i],e[n],e[s],e.scale,r,a)}let nM=["x","scaleX","originX"],nV=["y","scaleY","originY"];function nE(t,e,i,n){nA(t.x,e,nM,i?i.x:void 0,n?n.x:void 0),nA(t.y,e,nV,i?i.y:void 0,n?n.y:void 0)}function nD(t){return 0===t.translate&&1===t.scale}function nC(t){return nD(t.x)&&nD(t.y)}function nk(t,e){return t.min===e.min&&t.max===e.max}function nR(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nj(t,e){return nR(t.x,e.x)&&nR(t.y,e.y)}function nL(t){return iv(t.x)/iv(t.y)}function nF(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nB{constructor(){this.members=[]}add(t){T(this.members,t),t.scheduleRender()}remove(t){if(w(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nO={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nI=["","X","Y","Z"],nU={visibility:"hidden"},nN=0;function n$(t,e,i,n){let{latestValues:s}=e;s[t]&&(i[t]=s[t],e.setStaticValue(t,0),n&&(n[t]=0))}function nW({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:s}){return class{constructor(t={},i=e?.()){this.id=nN++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,h.value&&(nO.nodes=nO.calculatedTargetDeltas=nO.calculatedProjections=0),this.nodes.forEach(nH),this.nodes.forEach(nQ),this.nodes.forEach(nJ),this.nodes.forEach(nX),h.addProjectionMetrics&&h.addProjectionMetrics(nO)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new nd)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new b),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:i,layout:n,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i,n=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=x.now(),n=({timestamp:s})=>{let r=s-i;r>=250&&(f(n),t(r-e))};return m.setup(n,!0),()=>f(n)}(n,250),ni.hasAnimatedSinceResize&&(ni.hasAnimatedSinceResize=!1,this.nodes.forEach(nZ))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&s&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||s.getDefaultTransition()||n9,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=s.getProps(),l=!this.targetLayout||!nj(this.targetLayout,n),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...U(r,"layout"),onPlay:a,onComplete:o};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||nZ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),f(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(n0),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[C];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",m,!(t||i))}let{parent:s}=e;s&&!s.hasCheckedOptimisedAppear&&t(s)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nK);return}this.isUpdating||this.nodes.forEach(nq),this.isUpdating=!1,this.nodes.forEach(nG),this.nodes.forEach(nY),this.nodes.forEach(nz),this.clearAllSnapshots();let t=x.now();y.delta=K(0,1e3/60,t-y.timestamp),y.timestamp=t,y.isProcessing=!0,g.update.process(y),g.preRender.process(y),g.render.process(y),y.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,na.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(n_),this.sharedNodes.forEach(n1)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,m.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){m.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iv(this.snapshot.measuredBox.x)||iv(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iV(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nC(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,r=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||ik(this.latestValues)||r)&&(s(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),n8((e=n).x),n8(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iV();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(st))){let{scroll:t}=this.root;t&&(iB(e.x,t.offset.x),iB(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iV();if(nb(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:s,options:r}=n;n!==this.root&&s&&r.layoutScroll&&(s.wasRoot&&nb(e,t),iB(e.x,s.offset.x),iB(e.y,s.offset.y))}return e}applyTransform(t,e=!1){let i=iV();nb(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iI(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),ik(n.latestValues)&&iI(i,n.latestValues)}return ik(this.latestValues)&&iI(i,this.latestValues),i}removeTransform(t){let e=iV();nb(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!ik(i.latestValues))continue;iC(i.latestValues)&&i.updateSnapshot();let n=iV();nb(n,i.measurePageBox()),nE(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return ik(this.latestValues)&&nE(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==y.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:s}=this.options;if(this.layout&&(n||s)){if(this.resolvedRelativeTargetAt=y.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iV(),this.relativeTargetOrigin=iV(),iP(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nb(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iV(),this.targetWithTransforms=iV()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var r,a,o;this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,o=this.relativeParent.target,iw(r.x,a.x,o.x),iw(r.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nb(this.target,this.layout.layoutBox),iF(this.target,this.targetDelta)):nb(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iV(),this.relativeTargetOrigin=iV(),iP(this.relativeTargetOrigin,this.target,t.target),nb(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}h.value&&nO.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iC(this.parent.latestValues)||iR(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===y.timestamp&&(i=!1),i)return;let{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||s))return;nb(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,n=!1){let s,r,a=i.length;if(a){e.x=e.y=1;for(let o=0;o<a;o++){r=(s=i[o]).projectionDelta;let{visualElement:a}=s.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(n&&s.options.layoutScroll&&s.scroll&&s!==s.root&&iI(t,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,iF(t,r)),n&&ik(s.latestValues)&&iI(t,s.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iV());let{target:o}=t;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nP(this.prevProjectionDelta.x,this.projectionDelta.x),nP(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iT(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&nF(this.projectionDelta.x,this.prevProjectionDelta.x)&&nF(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),h.value&&nO.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iA(),this.projectionDelta=iA(),this.projectionDeltaWithTransform=iA()}setAnimationOrigin(t,e=!1){let i,n=this.snapshot,s=n?n.latestValues:{},r={...this.latestValues},a=iA();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let o=iV(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(n3));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(n5(a.x,t.x,n),n5(a.y,t.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,y;iP(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,y=n,n2(p.x,m.x,f.x,y),n2(p.y,m.y,f.y,y),i&&(u=this.relativeTarget,c=i,nk(u.x,c.x)&&nk(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=iV()),nb(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,n,s,r){s?(t.opacity=tV(0,i.opacity??1,nv(n)),t.opacityExit=tV(e.opacity??1,0,nx(n))):r&&(t.opacity=tV(e.opacity??1,i.opacity??1,n));for(let s=0;s<nm;s++){let r=`border${np[s]}Radius`,a=ng(e,r),o=ng(i,r);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||ny(a)===ny(o)?(t[r]=Math.max(tV(nf(a),nf(o),n),0),(tu.test(o)||tu.test(a))&&(t[r]+="%")):t[r]=o)}(e.rotate||i.rotate)&&(t.rotate=tV(e.rotate||0,i.rotate||0,n))}(r,s,this.latestValues,n,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(!1),this.resumingFrom?.currentAnimation?.stop(!1),this.pendingAnimation&&(f(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=m.update(()=>{ni.hasAnimatedSinceResize=!0,W.layout++,this.motionValue||(this.motionValue=M(0)),this.currentAnimation=function(t,e,i){let n=V(t)?t:M(t);return n.start(e5("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{W.layout--},onComplete:()=>{W.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop(!1)),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:s}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&n7(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||iV();let e=iv(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=iv(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}nb(e,i),iI(e,s),iT(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nB),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&n$("z",t,n,this.animationValues);for(let e=0;e<nI.length;e++)n$(`rotate${nI[e]}`,t,n,this.animationValues),n$(`skew${nI[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return nU;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=nc(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=nc(t?.pointerEvents)||""),this.hasProjected&&!ik(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let s=n.animationValues||n.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let n="",s=t.x.translate/e.x,r=t.y.translate/e.y,a=i?.z||0;if((s||r||a)&&(n=`translate3d(${s}px, ${r}px, ${a}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:s,rotateY:r,skewX:a,skewY:o}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),s&&(n+=`rotateX(${s}deg) `),r&&(n+=`rotateY(${r}deg) `),a&&(n+=`skewX(${a}deg) `),o&&(n+=`skewY(${o}deg) `)}let o=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==o||1!==l)&&(n+=`scale(${o}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),i&&(e.transform=i(s,e.transform));let{x:r,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*r.origin}% ${100*a.origin}% 0`,n.animationValues?e.opacity=n===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:e.opacity=n===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,nr){if(void 0===s[t])continue;let{correct:i,applyTo:r,isCSSVariable:a}=nr[t],o="none"===e.transform?s[t]:i(s[t],n);if(r){let t=r.length;for(let i=0;i<t;i++)e[r[i]]=o}else a?this.options.visualElement.renderState.vars[t]=o:e[t]=o}return this.options.layoutId&&(e.pointerEvents=n===this?nc(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop(!1)),this.root.nodes.forEach(nK),this.root.sharedNodes.clear()}}}function nY(t){t.updateLayout()}function nz(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:s}=t.options,r=e.source!==t.layout.source;"size"===s?iE(t=>{let n=r?e.measuredBox[t]:e.layoutBox[t],s=iv(n);n.min=i[t].min,n.max=n.min+s}):n7(s,e.layoutBox,i)&&iE(n=>{let s=r?e.measuredBox[n]:e.layoutBox[n],a=iv(i[n]);s.max=s.min+a,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+a)});let a=iA();iT(a,i,e.layoutBox);let o=iA();r?iT(o,t.applyTransform(n,!0),e.measuredBox):iT(o,i,e.layoutBox);let l=!nC(a),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:s,layout:r}=n;if(s&&r){let a=iV();iP(a,e.layoutBox,s.layoutBox);let o=iV();iP(o,i,r.layoutBox),nj(a,o)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=o,t.relativeTargetOrigin=a,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nH(t){h.value&&nO.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nX(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function n_(t){t.clearSnapshot()}function nK(t){t.clearMeasurements()}function nq(t){t.isLayoutDirty=!1}function nG(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function nZ(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function nQ(t){t.resolveTargetDelta()}function nJ(t){t.calcProjection()}function n0(t){t.resetSkewAndRotation()}function n1(t){t.removeLeadSnapshot()}function n5(t,e,i){t.translate=tV(e.translate,0,i),t.scale=tV(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function n2(t,e,i,n){t.min=tV(e.min,i.min,n),t.max=tV(e.max,i.max,n)}function n3(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let n9={duration:.45,ease:[.4,0,.1,1]},n4=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),n6=n4("applewebkit/")&&!n4("chrome/")?Math.round:p;function n8(t){t.min=n6(t.min),t.max=n6(t.max)}function n7(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nL(e)-nL(i)))}function st(t){return t!==t.root&&t.scroll?.wasRoot}let se=nW({attachResizeListener:(t,e)=>id(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),si={current:void 0},sn=nW({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!si.current){let t=new se({});t.mount(window),t.setOptions({layoutScroll:!0}),si.current=t}return si.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function ss(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function sr(t){return!("touch"===t.pointerType||iQ.x||iQ.y)}function sa(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let s=n["onHover"+i];s&&m.postRender(()=>s(e,ip(e)))}class so extends io{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=ss(t,i),a=t=>{if(!sr(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let r=t=>{sr(t)&&(n(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,s)};return n.forEach(t=>{t.addEventListener("pointerenter",a,s)}),r}(t,(t,e)=>(sa(this.node,e,"Start"),t=>sa(this.node,t,"End"))))}unmount(){}}class sl extends io{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tO(id(this.node.current,"focus",()=>this.onFocus()),id(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let su=(t,e)=>!!e&&(t===e||su(t,e.parentElement)),sh=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),sd=new WeakSet;function sc(t){return e=>{"Enter"===e.key&&t(e)}}function sp(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let sm=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=sc(()=>{if(sd.has(i))return;sp(i,"down");let t=sc(()=>{sp(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>sp(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function sf(t){return ic(t)&&!(iQ.x||iQ.y)}function sy(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let s=n["onTap"+("End"===i?"":i)];s&&m.postRender(()=>s(e,ip(e)))}class sg extends io{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=ss(t,i),a=t=>{let n=t.currentTarget;if(!sf(t)||sd.has(n))return;sd.add(n);let r=e(n,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),sd.has(n)&&sd.delete(n),sf(t)&&"function"==typeof r&&r(t,{success:e})},o=t=>{a(t,n===window||n===document||i.useGlobalTarget||su(n,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,s),window.addEventListener("pointercancel",l,s)};return n.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",a,s),t instanceof HTMLElement)&&(t.addEventListener("focus",t=>sm(t,s)),sh.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}(t,(t,e)=>(sy(this.node,e,"Start"),(t,{success:e})=>sy(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let sv=new WeakMap,sx=new WeakMap,sT=t=>{let e=sv.get(t.target);e&&e(t)},sw=t=>{t.forEach(sT)},sb={some:0,all:1};class sP extends io{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:s}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:sb[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;sx.has(i)||sx.set(i,{});let n=sx.get(i),s=JSON.stringify(e);return n[s]||(n[s]=new IntersectionObserver(sw,{root:t,...e})),n[s]}(e);return sv.set(t,i),n.observe(t),()=>{sv.delete(t),n.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,s&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),r=e?i:n;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let sS=(0,i8.createContext)({strict:!1});var sA=i(7471);let sM=(0,i8.createContext)({});function sV(t){return s(t.animate)||it.some(e=>e8(t[e]))}function sE(t){return!!(sV(t)||t.variants)}function sD(t){return Array.isArray(t)?t.join(" "):t}var sC=i(42801);let sk={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},sR={};for(let t in sk)sR[t]={isEnabled:e=>sk[t].some(t=>!!e[t])};let sj=Symbol.for("motionComponentSymbol");var sL=i(50430),sF=i(69025);function sB(t,{layout:e,layoutId:i}){return j.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!nr[t]||"opacity"===t)}let sO=(t,e)=>e&&"number"==typeof t?e.transform(t):t,sI={...q,transform:Math.round},sU={borderWidth:th,borderTopWidth:th,borderRightWidth:th,borderBottomWidth:th,borderLeftWidth:th,borderRadius:th,radius:th,borderTopLeftRadius:th,borderTopRightRadius:th,borderBottomRightRadius:th,borderBottomLeftRadius:th,width:th,maxWidth:th,height:th,maxHeight:th,top:th,right:th,bottom:th,left:th,padding:th,paddingTop:th,paddingRight:th,paddingBottom:th,paddingLeft:th,margin:th,marginTop:th,marginRight:th,marginBottom:th,marginLeft:th,backgroundPositionX:th,backgroundPositionY:th,rotate:tl,rotateX:tl,rotateY:tl,rotateZ:tl,scale:Z,scaleX:Z,scaleY:Z,scaleZ:Z,skew:tl,skewX:tl,skewY:tl,distance:th,translateX:th,translateY:th,translateZ:th,x:th,y:th,z:th,perspective:th,transformPerspective:th,opacity:G,originX:tp,originY:tp,originZ:th,zIndex:sI,fillOpacity:G,strokeOpacity:G,numOctaves:sI},sN={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},s$=R.length;function sW(t,e,i){let{style:n,vars:s,transformOrigin:r}=t,a=!1,o=!1;for(let t in e){let i=e[t];if(j.has(t)){a=!0;continue}if(z(t)){s[t]=i;continue}{let e=sO(i,sU[t]);t.startsWith("origin")?(o=!0,r[t]=e):n[t]=e}}if(!e.transform&&(a||i?n.transform=function(t,e,i){let n="",s=!0;for(let r=0;r<s$;r++){let a=R[r],o=t[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let t=sO(o,sU[a]);if(!l){s=!1;let e=sN[a]||a;n+=`${e}(${t}) `}i&&(e[a]=t)}}return n=n.trim(),i?n=i(e,s?"":n):s&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),o){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;n.transformOrigin=`${t} ${e} ${i}`}}let sY=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function sz(t,e,i){for(let n in e)V(e[n])||sB(n,i)||(t[n]=e[n])}let sH=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function sX(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||sH.has(t)}let s_=t=>!sX(t);try{!function(t){t&&(s_=e=>e.startsWith("on")?!sX(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let sK=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function sq(t){if("string"!=typeof t||t.includes("-"));else if(sK.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}let sG={offset:"stroke-dashoffset",array:"stroke-dasharray"},sZ={offset:"strokeDashoffset",array:"strokeDasharray"};function sQ(t,{attrX:e,attrY:i,attrScale:n,pathLength:s,pathSpacing:r=1,pathOffset:a=0,...o},l,u,h){if(sW(t,o,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=h?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==n&&(d.scale=n),void 0!==s&&function(t,e,i=1,n=0,s=!0){t.pathLength=1;let r=s?sG:sZ;t[r.offset]=th.transform(-n);let a=th.transform(e),o=th.transform(i);t[r.array]=`${a} ${o}`}(d,s,r,a,!1)}let sJ=()=>({...sY(),attrs:{}}),s0=t=>"string"==typeof t&&"svg"===t.toLowerCase();var s1=i(76168);let s5=t=>(e,i)=>{let n=(0,i8.useContext)(sM),r=(0,i8.useContext)(sL.t),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,r){return{latestValues:function(t,e,i,n){let r={},o=n(t,{});for(let t in o)r[t]=nc(o[t]);let{initial:l,animate:u}=t,h=sV(t),d=sE(t);e&&d&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!s(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let n=a(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(i,n,r,t),renderState:e()}})(t,e,n,r);return i?o():(0,s1.M)(o)};function s2(t,e,i){let{style:n}=t,s={};for(let r in n)(V(n[r])||e.style&&V(e.style[r])||sB(r,t)||i?.getValue(r)?.liveStyle!==void 0)&&(s[r]=n[r]);return s}let s3={useVisualState:s5({scrapeMotionValuesFromProps:s2,createRenderState:sY})};function s9(t,e,i){let n=s2(t,e,i);for(let i in t)(V(t[i])||V(e[i]))&&(n[-1!==R.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let s4={useVisualState:s5({scrapeMotionValuesFromProps:s9,createRenderState:sJ})},s6={current:null},s8={current:!1},s7=new WeakMap,rt=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),re=t=>/^0[^.\s]+$/u.test(t),ri=t=>e=>e.test(t),rn=[q,th,tu,tl,tc,td,{test:t=>"auto"===t,parse:t=>t}],rs=t=>rn.find(ri(t)),rr=[...rn,tf,tS],ra=t=>rr.find(ri(t)),ro=new Set(["brightness","contrast","saturate","opacity"]);function rl(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(J)||[];if(!n)return t;let s=i.replace(n,""),r=+!!ro.has(e);return n!==i&&(r*=100),e+"("+r+s+")"}let ru=/\b([a-z-]*)\(.*?\)/gu,rh={...tS,getAnimatableNone:t=>{let e=t.match(ru);return e?e.map(rl).join(" "):t}},rd={...sU,color:tf,backgroundColor:tf,outlineColor:tf,fill:tf,stroke:tf,borderColor:tf,borderTopColor:tf,borderRightColor:tf,borderBottomColor:tf,borderLeftColor:tf,filter:rh,WebkitFilter:rh},rc=t=>rd[t];function rp(t,e){let i=rc(t);return i!==rh&&(i=tS),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let rm=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class rf{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:s,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eN,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=x.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,m.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=r;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.blockInitialAnimation=!!s,this.isControllingVariants=sV(e),this.isVariantNode=sE(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==o[t]&&V(e)&&e.set(o[t],!1)}}mount(t){this.current=t,s7.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),s8.current||function(){if(s8.current=!0,sC.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>s6.current=t.matches;t.addListener(e),e()}else s6.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||s6.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),f(this.notifyUpdate),f(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=j.has(t);n&&this.onBindTransform&&this.onBindTransform();let s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&m.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in sR){let e=sR[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iV()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<rm.length;e++){let i=rm[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let s=e[n],r=i[n];if(V(s))t.addValue(n,s);else if(V(r))t.addValue(n,M(s,{owner:t}));else if(r!==s)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(n);t.addValue(n,M(void 0!==e?e:s,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=M(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(rt(i)||re(i))?i=parseFloat(i):!ra(i)&&tS.test(e)&&(i=rp(t,e)),this.setBaseTarget(t,V(i)?i.get():i)),V(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=a(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||V(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new b),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}let ry=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,rg=new Set(["auto","none","0"]);class rv extends eN{constructor(t,e,i,n,s){super(t,e,i,n,s,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&X(n=n.trim())){let s=function t(e,i,n=1){tD(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[s,r]=function(t){let e=ry.exec(t);if(!e)return[,];let[,i,n,s]=e;return[`--${i??n}`,s]}(e);if(!s)return;let a=window.getComputedStyle(i).getPropertyValue(s);if(a){let t=a.trim();return rt(t)?parseFloat(t):t}return X(r)?t(r,i,n+1):r}(n,e.current);void 0!==s&&(t[i]=s),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!e2.has(i)||2!==t.length)return;let[n,s]=t,r=rs(n),a=rs(s);if(r!==a)if(eC(r)&&eC(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else ej[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||re(n)))&&i.push(e)}i.length&&function(t,e,i){let n,s=0;for(;s<t.length&&!n;){let e=t[s];"string"==typeof e&&!rg.has(e)&&tT(e).values.length&&(n=t[s]),s++}if(n&&i)for(let s of e)t[s]=rp(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ej[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let s=i.length-1,r=i[s];i[s]=ej[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}class rx extends rf{constructor(){super(...arguments),this.KeyframeResolver=rv}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;V(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function rT(t,{style:e,vars:i},n,s){for(let r in Object.assign(t.style,e,s&&s.getProjectionStyles(n)),i)t.style.setProperty(r,i[r])}class rw extends rx{constructor(){super(...arguments),this.type="html",this.renderInstance=rT}readValueFromInstance(t,e){if(j.has(e))return eE(t,e);{let i=window.getComputedStyle(t),n=(z(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return iU(t,e)}build(t,e,i){sW(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return s2(t,e,i)}}let rb=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class rP extends rx{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iV}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(j.has(e)){let t=rc(e);return t&&t.default||0}return e=rb.has(e)?e:D(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return s9(t,e,i)}build(t,e,i){sQ(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){for(let i in rT(t,e,void 0,n),e.attrs)t.setAttribute(rb.has(i)?i:D(i),e.attrs[i])}mount(t){this.isSVGTag=s0(t.tagName),super.mount(t)}}let rS=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((i9={animation:{Feature:il},exit:{Feature:ih},inView:{Feature:sP},tap:{Feature:sg},focus:{Feature:sl},hover:{Feature:so},pan:{Feature:i3},drag:{Feature:i5,ProjectionNode:sn,MeasureLayout:nl},layout:{ProjectionNode:sn,MeasureLayout:nl}},i4=(t,e)=>sq(t)?new rP(e):new rw(e,{allowProjection:t!==i8.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:n,createVisualElement:s,useRender:r,useVisualState:a,Component:o}=t;function l(t,e){var i,n,l;let u,h={...(0,i8.useContext)(sA.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,i8.useContext)(nt.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:d}=h,c=function(t){let{initial:e,animate:i}=function(t,e){if(sV(t)){let{initial:e,animate:i}=t;return{initial:!1===e||e8(e)?e:void 0,animate:e8(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,i8.useContext)(sM));return(0,i8.useMemo)(()=>({initial:e,animate:i}),[sD(e),sD(i)])}(t),p=a(t,d);if(!d&&sC.B){n=0,l=0,(0,i8.useContext)(sS).strict;let t=function(t){let{drag:e,layout:i}=sR;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(h);u=t.MeasureLayout,c.visualElement=function(t,e,i,n,s){let{visualElement:r}=(0,i8.useContext)(sM),a=(0,i8.useContext)(sS),o=(0,i8.useContext)(sL.t),l=(0,i8.useContext)(sA.Q).reducedMotion,u=(0,i8.useRef)(null);n=n||a.renderer,!u.current&&n&&(u.current=n(t,{visualState:e,parent:r,props:i,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let h=u.current,d=(0,i8.useContext)(ne);h&&!h.projection&&s&&("html"===h.type||"svg"===h.type)&&function(t,e,i,n){let{layoutId:s,layout:r,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:s,layout:r,alwaysMeasureLayout:!!a||o&&i$(o),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,i,s,d);let c=(0,i8.useRef)(!1);(0,i8.useInsertionEffect)(()=>{h&&c.current&&h.update(i,o)});let p=i[C],m=(0,i8.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,sF.E)(()=>{h&&(c.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),na.render(h.render),m.current&&h.animationState&&h.animationState.animateChanges())}),(0,i8.useEffect)(()=>{h&&(!m.current&&h.animationState&&h.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),h}(o,p,h,s,t.ProjectionNode)}return(0,i6.jsxs)(sM.Provider,{value:c,children:[u&&c.visualElement?(0,i6.jsx)(u,{visualElement:c.visualElement,...h}):null,r(o,t,(i=c.visualElement,(0,i8.useCallback)(t=>{t&&p.onMount&&p.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):i$(e)&&(e.current=t))},[i])),p,d,c.visualElement)]})}n&&function(t){for(let e in t)sR[e]={...sR[e],...t[e]}}(n),l.displayName="motion.".concat("string"==typeof o?o:"create(".concat(null!=(i=null!=(e=o.displayName)?e:o.name)?i:"",")"));let u=(0,i8.forwardRef)(l);return u[sj]=o,u}({...sq(t)?s4:s3,preloadedFeatures:i9,useRender:function(t=!1){return(e,i,n,{latestValues:s},r)=>{let a=(sq(e)?function(t,e,i,n){let s=(0,i8.useMemo)(()=>{let i=sJ();return sQ(i,e,s0(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};sz(e,t.style,t),s.style={...e,...s.style}}return s}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return sz(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,i8.useMemo)(()=>{let i=sY();return sW(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,s,r,e),o=function(t,e,i){let n={};for(let s in t)("values"!==s||"object"!=typeof t.values)&&(s_(s)||!0===i&&sX(s)||!e&&!sX(s)||t.draggable&&s.startsWith("onDrag"))&&(n[s]=t[s]);return n}(i,"string"==typeof e,t),l=e!==i8.Fragment?{...o,...a,ref:n}:{},{children:u}=i,h=(0,i8.useMemo)(()=>V(u)?u.get():u,[u]);return(0,i8.createElement)(e,{...l,children:h})}}(e),createVisualElement:i4,Component:t})}))},35695:(t,e,i)=>{var n=i(18999);i.o(n,"usePathname")&&i.d(e,{usePathname:function(){return n.usePathname}}),i.o(n,"useRouter")&&i.d(e,{useRouter:function(){return n.useRouter}}),i.o(n,"useSearchParams")&&i.d(e,{useSearchParams:function(){return n.useSearchParams}})},42801:(t,e,i)=>{i.d(e,{B:()=>n});let n="undefined"!=typeof window},50430:(t,e,i)=>{i.d(e,{t:()=>n});let n=(0,i(12115).createContext)(null)},52596:(t,e,i)=>{function n(){for(var t,e,i=0,n="",s=arguments.length;i<s;i++)(t=arguments[i])&&(e=function t(e){var i,n,s="";if("string"==typeof e||"number"==typeof e)s+=e;else if("object"==typeof e)if(Array.isArray(e)){var r=e.length;for(i=0;i<r;i++)e[i]&&(n=t(e[i]))&&(s&&(s+=" "),s+=n)}else for(n in e)e[n]&&(s&&(s+=" "),s+=n);return s}(t))&&(n&&(n+=" "),n+=e);return n}i.d(e,{$:()=>n,A:()=>s});let s=n},57728:(t,e,i)=>{i.d(e,{L:()=>n});let n=(0,i(12115).createContext)({})},69025:(t,e,i)=>{i.d(e,{E:()=>s});var n=i(12115);let s=i(42801).B?n.useLayoutEffect:n.useEffect},76168:(t,e,i)=>{i.d(e,{M:()=>s});var n=i(12115);function s(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}}}]);