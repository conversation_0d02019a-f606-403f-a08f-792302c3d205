from typing import Dict, Any
from ..base_agent import Agent

class PlannerAgent(Agent):
    """A planner agent that determines the workflow sequence."""
    
    def __init__(self):
        description = "A planner agent that determines the workflow sequence."
        super().__init__("planner", description)
        
    async def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the input data and determine the next agent to call.
        
        Args:
            input_data: Dictionary containing input data
            
        Returns:
            Dictionary containing the response and next agent recommendation
        """
        # Analyze the input data to determine the next step
        # For now, we'll use a simple example logic
        if "retrieve" in input_data.get("type", "").lower():
            next_agent = "retriever"
        elif "forecast" in input_data.get("type", "").lower():
            next_agent = "forecast"
        else:
            # Default to retriever if no specific type is mentioned
            next_agent = "retriever"
            
        return {
            "message": "Planner has determined the next step",
            "status": "success",
            "next_agent": next_agent,
            "reason": f"Based on input type: {input_data.get('type', 'unknown')}"
        } 