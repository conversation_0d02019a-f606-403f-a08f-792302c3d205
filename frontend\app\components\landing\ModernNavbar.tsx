'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { motion } from 'motion/react';
import ThemeToggle from '../ThemeToggle';
import { useTheme } from '../../contexts/ThemeContext';

export default function ModernNavbar() {
  const pathname = usePathname();
  const { theme } = useTheme();
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Handle scroll effect for navbar
  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);

    // Enable smooth scrolling for anchor links
    const originalScrollBehavior = document.documentElement.style.scrollBehavior;
    document.documentElement.style.scrollBehavior = 'smooth';

    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.documentElement.style.scrollBehavior = originalScrollBehavior;
    };
  }, []);

  const isActive = (path: string) => {
    return pathname === path;
  };

  // Helper for smooth scroll with offset
  const handleSmoothScroll = (e: React.MouseEvent<HTMLAnchorElement>, id: string) => {
    e.preventDefault();
    if (id === 'top') {
      window.scrollTo({ top: 0, behavior: 'smooth' });
      return;
    }
    const el = document.getElementById(id);
    if (el) {
      const yOffset = -100; // Adjust this value to match your navbar height
      const y = el.getBoundingClientRect().top + window.pageYOffset + yOffset;
      window.scrollTo({ top: y, behavior: 'smooth' });
    }
  };

  return (
    <motion.nav
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`fixed w-[95vw] md:max-w-7xl left-1/2 -translate-x-1/2 z-60 transition-all duration-300 ${
        scrolled
          ? 'py-3 mx-auto my-3 bg-background/95 backdrop-blur-xl rounded-full border border-border/20'
          : 'py-5 mx-auto bg-background/95 backdrop-blur-xl md:bg-transparent md:backdrop-blur-none'
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="flex items-center space-x-3"
            >
              <div className="bg-white/10 backdrop-blur-md rounded-xl p-1.5 sm:p-2 shadow-lg border border-black/20 dark:border-white/20">
                <Image
                  src="/kairoslogo.png"
                  alt="Kairos AI Logo"
                  width={32}
                  height={32}
                  className="h-6 w-6 sm:h-8 sm:w-8"
                  priority
                />
              </div>
              <div className="flex items-center">
                <span className="text-lg sm:text-xl md:text-2xl font-bold text-foreground font-inknut">
                  Kairos
                </span>
                <span className="ml-1 text-kairosGreen text-lg sm:text-xl md:text-2xl">AI</span>
              </div>
            </motion.div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center">
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="flex space-x-6 ml-12"
            >
              {[
                { name: 'About', path: '#about' },
                { name: 'Features', path: '#features' },
                { name: 'Founders', path: '#founders' },
              ].map((item, index) => (
                <a
                  key={item.path}
                  href={item.path}
                  onClick={item.path.startsWith('#') ? (e) => handleSmoothScroll(e, item.path.substring(1)) : undefined}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                    isActive(item.path)
                      ? 'text-foreground bg-primary'
                      : 'text-muted hover:text-foreground hover:bg-secondary/20'
                  }`}
                >
                  {item.name}
                </a>
              ))}
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="ml-4 flex items-center space-x-3"
            >
              <ThemeToggle />
              <a
                href="#top"
                onClick={(e) => handleSmoothScroll(e, 'top')}
                className="px-4 py-2 bg-red-400 text-white border border-white dark:border-black rounded-full hover:bg-red-500 dark:hover:bg-red-300 transition-all duration-300 shadow-lg font-medium cursor-pointer"
              >
                Get Started
              </a>
            </motion.div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              type="button"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-full text-white hover:text-white focus:outline-none bg-red-400 hover:bg-red-500 border border-black dark:border-white transition-colors duration-200"
              aria-expanded="false"
            >
              <span className="sr-only">Open main menu</span>
              {/* Icon when menu is closed */}
              <svg
                className={`${mobileMenuOpen ? 'hidden' : 'block'} h-6 w-6`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
              {/* Icon when menu is open */}
              <svg
                className={`${mobileMenuOpen ? 'block' : 'hidden'} h-6 w-6`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu, show/hide based on menu state */}
      <motion.div
        initial={{ opacity: 0, y: -20, scale: 0.95 }}
        animate={{
          opacity: mobileMenuOpen ? 1 : 0,
          y: mobileMenuOpen ? 0 : -20,
          scale: mobileMenuOpen ? 1 : 0.95,
        }}
        transition={{
          duration: 0.2,
          ease: "easeOut",
          type: "spring",
          stiffness: 300,
          damping: 30
        }}
        className={`md:hidden fixed inset-x-4 top-20 z-50 ${mobileMenuOpen ? 'pointer-events-auto' : 'pointer-events-none'}`}
      >
        <div className="bg-background backdrop-blur-xl rounded-2xl border border-black/10 dark:border-white/10 shadow-2xl p-6 mx-auto max-w-sm">
          <div className="flex flex-col space-y-2 w-full">
            {[
              { name: 'About', path: '#about' },
              { name: 'Features', path: '#features' },
              { name: 'Founders', path: '#founders' },
            ].map((item, index) => (
              <motion.a
                key={item.path}
                href={item.path}
                onClick={item.path.startsWith('#') ? (e) => { handleSmoothScroll(e, item.path.substring(1)); setMobileMenuOpen(false); } : undefined}
                className={`block px-4 py-3 rounded-xl text-base font-medium transition-all duration-200 touch-manipulation ${
                  isActive(item.path)
                    ? 'text-foreground bg-primary border border-black/10 dark:border-white/10'
                    : 'text-muted hover:text-foreground hover:bg-secondary/50 active:bg-secondary/70'
                }`}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                whileTap={{ scale: 0.98 }}
              >
                {item.name}
              </motion.a>
            ))}

            <div className="pt-4 border-t border-border/50 space-y-3">
              <div className="flex justify-center">
                <ThemeToggle />
              </div>
              <motion.a
                href="#top"
                onClick={(e) => { handleSmoothScroll(e, 'top'); setMobileMenuOpen(false); }}
                className="block w-full px-4 py-3 bg-red-400 text-white rounded-xl hover:bg-red-500 active:bg-red-600 transition-colors text-center font-medium shadow-lg border border-black/10 dark:border-white/10 touch-manipulation"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                whileTap={{ scale: 0.98 }}
              >
                Get Started
              </motion.a>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.nav>
  );
}







