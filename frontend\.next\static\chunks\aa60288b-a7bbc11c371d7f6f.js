"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[843],{83760:(t,e,i)=>{i.d(e,{Kx:()=>H,PE:()=>tu});var s=i(95155),n=i(12115),o=i(47650),r=i(12669),a=Object.defineProperty,l=(t,e,i)=>e in t?a(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i,d=(t,e,i)=>l(t,"symbol"!=typeof e?e+"":e,i);let h=class t{constructor(t){d(this,"_name"),this._name=t}static flip(e){return e===t.HORZ?t.VERT:t.HORZ}getName(){return this._name}toString(){return this._name}};d(h,"HORZ",new h("horz")),d(h,"VERT",new h("vert"));class c{constructor(t,e,i,s){d(this,"x"),d(this,"y"),d(this,"width"),d(this,"height"),this.x=t,this.y=e,this.width=i,this.height=s}static empty(){return new c(0,0,0,0)}static fromJson(t){return new c(t.x,t.y,t.width,t.height)}toJson(){return{x:this.x,y:this.y,width:this.width,height:this.height}}snap(t){this.x=Math.round(this.x/t)*t,this.y=Math.round(this.y/t)*t,this.width=Math.round(this.width/t)*t,this.height=Math.round(this.height/t)*t}static getBoundingClientRect(t){let{x:e,y:i,width:s,height:n}=t.getBoundingClientRect();return new c(e,i,s,n)}static getContentRect(t){let e=t.getBoundingClientRect(),i=window.getComputedStyle(t),s=parseFloat(i.paddingLeft),n=parseFloat(i.paddingRight),o=parseFloat(i.paddingTop),r=parseFloat(i.paddingBottom),a=parseFloat(i.borderLeftWidth),l=parseFloat(i.borderRightWidth),d=parseFloat(i.borderTopWidth),h=parseFloat(i.borderBottomWidth),u=e.width-a-s-n-l,g=e.height-d-o-r-h;return new c(e.left+a+s,e.top+d+o,u,g)}static fromDomRect(t){return new c(t.x,t.y,t.width,t.height)}relativeTo(t){return new c(this.x-t.x,this.y-t.y,this.width,this.height)}clone(){return new c(this.x,this.y,this.width,this.height)}equals(t){return this.x===(null==t?void 0:t.x)&&this.y===(null==t?void 0:t.y)&&this.width===(null==t?void 0:t.width)&&this.height===(null==t?void 0:t.height)}equalSize(t){return this.width===(null==t?void 0:t.width)&&this.height===(null==t?void 0:t.height)}getBottom(){return this.y+this.height}getRight(){return this.x+this.width}get bottom(){return this.y+this.height}get right(){return this.x+this.width}getCenter(){return{x:this.x+this.width/2,y:this.y+this.height/2}}positionElement(t,e){this.styleWithPosition(t.style,e)}styleWithPosition(t,e="absolute"){return t.left=this.x+"px",t.top=this.y+"px",t.width=Math.max(0,this.width)+"px",t.height=Math.max(0,this.height)+"px",t.position=e,t}contains(t,e){return!!(this.x<=t&&t<=this.getRight()&&this.y<=e&&e<=this.getBottom())}removeInsets(t){return new c(this.x+t.left,this.y+t.top,Math.max(0,this.width-t.left-t.right),Math.max(0,this.height-t.top-t.bottom))}centerInRect(t){this.x=(t.width-this.width)/2,this.y=(t.height-this.height)/2}_getSize(t){let e=this.width;return t===h.VERT&&(e=this.height),e}toString(){return"(Rect: x="+this.x+", y="+this.y+", width="+this.width+", height="+this.height+")"}}let u=class t{constructor(e,i,s){d(this,"name"),d(this,"orientation"),d(this,"indexPlus"),this.name=e,this.orientation=i,this.indexPlus=s,t.values.set(this.name,this)}static getByName(e){return t.values.get(e)}static getLocation(e,i,s){if(i=(i-e.x)/e.width,s=(s-e.y)/e.height,i>=.25&&i<.75&&s>=.25&&s<.75)return t.CENTER;let n=s>=i,o=s>=1-i;return n?o?t.BOTTOM:t.LEFT:o?t.RIGHT:t.TOP}getName(){return this.name}getOrientation(){return this.orientation}getDockRect(e){return this===t.TOP?new c(e.x,e.y,e.width,e.height/2):this===t.BOTTOM?new c(e.x,e.getBottom()-e.height/2,e.width,e.height/2):this===t.LEFT?new c(e.x,e.y,e.width/2,e.height):this===t.RIGHT?new c(e.getRight()-e.width/2,e.y,e.width/2,e.height):e.clone()}split(e,i){return this===t.TOP?{start:new c(e.x,e.y,e.width,i),end:new c(e.x,e.y+i,e.width,e.height-i)}:this===t.LEFT?{start:new c(e.x,e.y,i,e.height),end:new c(e.x+i,e.y,e.width-i,e.height)}:this===t.RIGHT?{start:new c(e.getRight()-i,e.y,i,e.height),end:new c(e.x,e.y,e.width-i,e.height)}:{start:new c(e.x,e.getBottom()-i,e.width,i),end:new c(e.x,e.y,e.width,e.height-i)}}reflect(){return this===t.TOP?t.BOTTOM:this===t.LEFT?t.RIGHT:this===t.RIGHT?t.LEFT:t.TOP}toString(){return"(DockLocation: name="+this.name+", orientation="+this.orientation+")"}};d(u,"values",new Map),d(u,"TOP",new u("top",h.VERT,0)),d(u,"BOTTOM",new u("bottom",h.VERT,1)),d(u,"LEFT",new u("left",h.HORZ,0)),d(u,"RIGHT",new u("right",h.HORZ,1)),d(u,"CENTER",new u("center",h.VERT,0));var g=(t=>(t.Close_Tab="Close",t.Close_Tabset="Close tab set",t.Active_Tabset="Active tab set",t.Move_Tabset="Move tab set",t.Move_Tabs="Move tabs(?)",t.Maximize="Maximize tab set",t.Restore="Restore tab set",t.Popout_Tab="Popout selected tab",t.Overflow_Menu_Tooltip="Hidden tabs",t.Error_rendering_component="Error rendering component",t.Error_rendering_component_retry="Retry",t))(g||{}),T=(t=>(t.FLEXLAYOUT__BORDER="flexlayout__border",t.FLEXLAYOUT__BORDER_="flexlayout__border_",t.FLEXLAYOUT__BORDER_TAB_CONTENTS="flexlayout__border_tab_contents",t.FLEXLAYOUT__BORDER_BUTTON="flexlayout__border_button",t.FLEXLAYOUT__BORDER_BUTTON_="flexlayout__border_button_",t.FLEXLAYOUT__BORDER_BUTTON_CONTENT="flexlayout__border_button_content",t.FLEXLAYOUT__BORDER_BUTTON_LEADING="flexlayout__border_button_leading",t.FLEXLAYOUT__BORDER_BUTTON_TRAILING="flexlayout__border_button_trailing",t.FLEXLAYOUT__BORDER_BUTTON__SELECTED="flexlayout__border_button--selected",t.FLEXLAYOUT__BORDER_BUTTON__UNSELECTED="flexlayout__border_button--unselected",t.FLEXLAYOUT__BORDER_TOOLBAR_BUTTON_OVERFLOW="flexlayout__border_toolbar_button_overflow",t.FLEXLAYOUT__BORDER_TOOLBAR_BUTTON_OVERFLOW_="flexlayout__border_toolbar_button_overflow_",t.FLEXLAYOUT__BORDER_INNER="flexlayout__border_inner",t.FLEXLAYOUT__BORDER_INNER_="flexlayout__border_inner_",t.FLEXLAYOUT__BORDER_INNER_TAB_CONTAINER="flexlayout__border_inner_tab_container",t.FLEXLAYOUT__BORDER_INNER_TAB_CONTAINER_="flexlayout__border_inner_tab_container_",t.FLEXLAYOUT__BORDER_TAB_DIVIDER="flexlayout__border_tab_divider",t.FLEXLAYOUT__BORDER_LEADING="flexlayout__border_leading",t.FLEXLAYOUT__BORDER_SIZER="flexlayout__border_sizer",t.FLEXLAYOUT__BORDER_TOOLBAR="flexlayout__border_toolbar",t.FLEXLAYOUT__BORDER_TOOLBAR_="flexlayout__border_toolbar_",t.FLEXLAYOUT__BORDER_TOOLBAR_BUTTON="flexlayout__border_toolbar_button",t.FLEXLAYOUT__BORDER_TOOLBAR_BUTTON_FLOAT="flexlayout__border_toolbar_button-float",t.FLEXLAYOUT__DRAG_RECT="flexlayout__drag_rect",t.FLEXLAYOUT__EDGE_RECT="flexlayout__edge_rect",t.FLEXLAYOUT__EDGE_RECT_TOP="flexlayout__edge_rect_top",t.FLEXLAYOUT__EDGE_RECT_LEFT="flexlayout__edge_rect_left",t.FLEXLAYOUT__EDGE_RECT_BOTTOM="flexlayout__edge_rect_bottom",t.FLEXLAYOUT__EDGE_RECT_RIGHT="flexlayout__edge_rect_right",t.FLEXLAYOUT__ERROR_BOUNDARY_CONTAINER="flexlayout__error_boundary_container",t.FLEXLAYOUT__ERROR_BOUNDARY_CONTENT="flexlayout__error_boundary_content",t.FLEXLAYOUT__FLOATING_WINDOW_CONTENT="flexlayout__floating_window_content",t.FLEXLAYOUT__LAYOUT="flexlayout__layout",t.FLEXLAYOUT__LAYOUT_MOVEABLES="flexlayout__layout_moveables",t.FLEXLAYOUT__LAYOUT_OVERLAY="flexlayout__layout_overlay",t.FLEXLAYOUT__LAYOUT_TAB_STAMPS="flexlayout__layout_tab_stamps",t.FLEXLAYOUT__LAYOUT_MAIN="flexlayout__layout_main",t.FLEXLAYOUT__LAYOUT_BORDER_CONTAINER="flexlayout__layout_border_container",t.FLEXLAYOUT__LAYOUT_BORDER_CONTAINER_INNER="flexlayout__layout_border_container_inner",t.FLEXLAYOUT__OUTLINE_RECT="flexlayout__outline_rect",t.FLEXLAYOUT__OUTLINE_RECT_EDGE="flexlayout__outline_rect_edge",t.FLEXLAYOUT__SPLITTER="flexlayout__splitter",t.FLEXLAYOUT__SPLITTER_EXTRA="flexlayout__splitter_extra",t.FLEXLAYOUT__SPLITTER_="flexlayout__splitter_",t.FLEXLAYOUT__SPLITTER_BORDER="flexlayout__splitter_border",t.FLEXLAYOUT__SPLITTER_DRAG="flexlayout__splitter_drag",t.FLEXLAYOUT__SPLITTER_HANDLE="flexlayout__splitter_handle",t.FLEXLAYOUT__SPLITTER_HANDLE_HORZ="flexlayout__splitter_handle_horz",t.FLEXLAYOUT__SPLITTER_HANDLE_VERT="flexlayout__splitter_handle_vert",t.FLEXLAYOUT__ROW="flexlayout__row",t.FLEXLAYOUT__TAB="flexlayout__tab",t.FLEXLAYOUT__TAB_POSITION="flexlayout__tab_position",t.FLEXLAYOUT__TAB_MOVEABLE="flexlayout__tab_moveable",t.FLEXLAYOUT__TAB_OVERLAY="flexlayout__tab_overlay",t.FLEXLAYOUT__TABSET="flexlayout__tabset",t.FLEXLAYOUT__TABSET_CONTAINER="flexlayout__tabset_container",t.FLEXLAYOUT__TABSET_HEADER="flexlayout__tabset_header",t.FLEXLAYOUT__TABSET_HEADER_CONTENT="flexlayout__tabset_header_content",t.FLEXLAYOUT__TABSET_MAXIMIZED="flexlayout__tabset-maximized",t.FLEXLAYOUT__TABSET_SELECTED="flexlayout__tabset-selected",t.FLEXLAYOUT__TABSET_TAB_DIVIDER="flexlayout__tabset_tab_divider",t.FLEXLAYOUT__TABSET_CONTENT="flexlayout__tabset_content",t.FLEXLAYOUT__TABSET_TABBAR_INNER="flexlayout__tabset_tabbar_inner",t.FLEXLAYOUT__TABSET_TABBAR_INNER_="flexlayout__tabset_tabbar_inner_",t.FLEXLAYOUT__TABSET_LEADING="flexlayout__tabset_leading",t.FLEXLAYOUT__TABSET_TABBAR_INNER_TAB_CONTAINER="flexlayout__tabset_tabbar_inner_tab_container",t.FLEXLAYOUT__TABSET_TABBAR_INNER_TAB_CONTAINER_="flexlayout__tabset_tabbar_inner_tab_container_",t.FLEXLAYOUT__TABSET_TABBAR_OUTER="flexlayout__tabset_tabbar_outer",t.FLEXLAYOUT__TABSET_TABBAR_OUTER_="flexlayout__tabset_tabbar_outer_",t.FLEXLAYOUT__TAB_BORDER="flexlayout__tab_border",t.FLEXLAYOUT__TAB_BORDER_="flexlayout__tab_border_",t.FLEXLAYOUT__TAB_BUTTON="flexlayout__tab_button",t.FLEXLAYOUT__TAB_BUTTON_STRETCH="flexlayout__tab_button_stretch",t.FLEXLAYOUT__TAB_BUTTON_CONTENT="flexlayout__tab_button_content",t.FLEXLAYOUT__TAB_BUTTON_LEADING="flexlayout__tab_button_leading",t.FLEXLAYOUT__TAB_BUTTON_OVERFLOW="flexlayout__tab_button_overflow",t.FLEXLAYOUT__TAB_BUTTON_OVERFLOW_COUNT="flexlayout__tab_button_overflow_count",t.FLEXLAYOUT__TAB_BUTTON_TEXTBOX="flexlayout__tab_button_textbox",t.FLEXLAYOUT__TAB_BUTTON_TRAILING="flexlayout__tab_button_trailing",t.FLEXLAYOUT__TAB_BUTTON_STAMP="flexlayout__tab_button_stamp",t.FLEXLAYOUT__TAB_TOOLBAR="flexlayout__tab_toolbar",t.FLEXLAYOUT__TAB_TOOLBAR_BUTTON="flexlayout__tab_toolbar_button",t.FLEXLAYOUT__TAB_TOOLBAR_ICON="flexlayout__tab_toolbar_icon",t.FLEXLAYOUT__TAB_TOOLBAR_BUTTON_="flexlayout__tab_toolbar_button-",t.FLEXLAYOUT__TAB_TOOLBAR_BUTTON_FLOAT="flexlayout__tab_toolbar_button-float",t.FLEXLAYOUT__TAB_TOOLBAR_STICKY_BUTTONS_CONTAINER="flexlayout__tab_toolbar_sticky_buttons_container",t.FLEXLAYOUT__TAB_TOOLBAR_BUTTON_CLOSE="flexlayout__tab_toolbar_button-close",t.FLEXLAYOUT__POPUP_MENU_CONTAINER="flexlayout__popup_menu_container",t.FLEXLAYOUT__POPUP_MENU_ITEM="flexlayout__popup_menu_item",t.FLEXLAYOUT__POPUP_MENU_ITEM__SELECTED="flexlayout__popup_menu_item--selected",t.FLEXLAYOUT__POPUP_MENU="flexlayout__popup_menu",t.FLEXLAYOUT__MINI_SCROLLBAR="flexlayout__mini_scrollbar",t.FLEXLAYOUT__MINI_SCROLLBAR_CONTAINER="flexlayout__mini_scrollbar_container",t))(T||{});class _{constructor(t,e){d(this,"type"),d(this,"data"),this.type=t,this.data=e}}let p=class t{static addNode(e,i,s,n,o){return new _(t.ADD_NODE,{json:e,toNode:i,location:s.getName(),index:n,select:o})}static moveNode(e,i,s,n,o){return new _(t.MOVE_NODE,{fromNode:e,toNode:i,location:s.getName(),index:n,select:o})}static deleteTab(e){return new _(t.DELETE_TAB,{node:e})}static deleteTabset(e){return new _(t.DELETE_TABSET,{node:e})}static renameTab(e,i){return new _(t.RENAME_TAB,{node:e,text:i})}static selectTab(e){return new _(t.SELECT_TAB,{tabNode:e})}static setActiveTabset(e,i){return new _(t.SET_ACTIVE_TABSET,{tabsetNode:e,windowId:i})}static adjustWeights(e,i){return new _(t.ADJUST_WEIGHTS,{nodeId:e,weights:i})}static adjustBorderSplit(e,i){return new _(t.ADJUST_BORDER_SPLIT,{node:e,pos:i})}static maximizeToggle(e,i){return new _(t.MAXIMIZE_TOGGLE,{node:e,windowId:i})}static updateModelAttributes(e){return new _(t.UPDATE_MODEL_ATTRIBUTES,{json:e})}static updateNodeAttributes(e,i){return new _(t.UPDATE_NODE_ATTRIBUTES,{node:e,json:i})}static popoutTab(e){return new _(t.POPOUT_TAB,{node:e})}static popoutTabset(e){return new _(t.POPOUT_TABSET,{node:e})}static closeWindow(e){return new _(t.CLOSE_WINDOW,{windowId:e})}static createWindow(e,i){return new _(t.CREATE_WINDOW,{layout:e,rect:i})}};d(p,"ADD_NODE","FlexLayout_AddNode"),d(p,"MOVE_NODE","FlexLayout_MoveNode"),d(p,"DELETE_TAB","FlexLayout_DeleteTab"),d(p,"DELETE_TABSET","FlexLayout_DeleteTabset"),d(p,"RENAME_TAB","FlexLayout_RenameTab"),d(p,"SELECT_TAB","FlexLayout_SelectTab"),d(p,"SET_ACTIVE_TABSET","FlexLayout_SetActiveTabset"),d(p,"ADJUST_WEIGHTS","FlexLayout_AdjustWeights"),d(p,"ADJUST_BORDER_SPLIT","FlexLayout_AdjustBorderSplit"),d(p,"MAXIMIZE_TOGGLE","FlexLayout_MaximizeToggle"),d(p,"UPDATE_MODEL_ATTRIBUTES","FlexLayout_UpdateModelAttributes"),d(p,"UPDATE_NODE_ATTRIBUTES","FlexLayout_UpdateNodeAttributes"),d(p,"POPOUT_TAB","FlexLayout_PopoutTab"),d(p,"POPOUT_TABSET","FlexLayout_PopoutTabset"),d(p,"CLOSE_WINDOW","FlexLayout_CloseWindow"),d(p,"CREATE_WINDOW","FlexLayout_CreateWindow");class b{constructor(t,e,i,s){d(this,"name"),d(this,"alias"),d(this,"modelName"),d(this,"pairedAttr"),d(this,"pairedType"),d(this,"defaultValue"),d(this,"alwaysWriteJson"),d(this,"type"),d(this,"required"),d(this,"fixed"),d(this,"description"),this.name=t,this.alias=void 0,this.modelName=e,this.defaultValue=i,this.alwaysWriteJson=s,this.required=!1,this.fixed=!1,this.type="any"}setType(t){return this.type=t,this}setAlias(t){return this.alias=t,this}setDescription(t){this.description=t}setRequired(){return this.required=!0,this}setFixed(){return this.fixed=!0,this}setpairedAttr(t){this.pairedAttr=t}setPairedType(t){this.pairedType=t}}d(b,"NUMBER","number"),d(b,"STRING","string"),d(b,"BOOLEAN","boolean");class E{constructor(){d(this,"attributes"),d(this,"nameToAttribute"),this.attributes=[],this.nameToAttribute=new Map}addWithAll(t,e,i,s){let n=new b(t,e,i,s);return this.attributes.push(n),this.nameToAttribute.set(t,n),n}addInherited(t,e){return this.addWithAll(t,e,void 0,!1)}add(t,e,i){return this.addWithAll(t,void 0,e,i)}getAttributes(){return this.attributes}getModelName(t){let e=this.nameToAttribute.get(t);if(void 0!==e)return e.modelName}toJson(t,e){for(let i of this.attributes){let s=e[i.name];(i.alwaysWriteJson||s!==i.defaultValue)&&(t[i.name]=s)}}fromJson(t,e){for(let i of this.attributes){let s=t[i.name];void 0===s&&i.alias&&(s=t[i.alias]),void 0===s?e[i.name]=i.defaultValue:e[i.name]=s}}update(t,e){for(let i of this.attributes)if(Object.prototype.hasOwnProperty.call(t,i.name)){let s=t[i.name];void 0===s?delete e[i.name]:e[i.name]=s}}setDefaults(t){for(let e of this.attributes)t[e.name]=e.defaultValue}pairAttributes(t,e){for(let i of e.attributes)if(i.modelName&&this.nameToAttribute.has(i.modelName)){let e=this.nameToAttribute.get(i.modelName);e.setpairedAttr(i),i.setpairedAttr(e),e.setPairedType(t)}}toTypescriptInterface(t,e){var i,s;let n=[],o=this.attributes.sort((t,e)=>t.name.localeCompare(e.name));n.push("export interface I"+t+"Attributes {");for(let t=0;t<o.length;t++){let r,a,l=o[t],d=l.type,h=l;void 0!==h.defaultValue?r=h.defaultValue:void 0!==h.modelName&&void 0!==e&&void 0!==e.nameToAttribute.get(h.modelName)&&(a=h.modelName,r=(h=e.nameToAttribute.get(a)).defaultValue,d=h.type);let c=JSON.stringify(r),u=h.required?"":"?",g="	/**\n	  ";l.description?g+=l.description:l.pairedType&&(null==(i=l.pairedAttr)?void 0:i.description)&&(g+=`Value for ${l.pairedType} attribute ${l.pairedAttr.name} if not overridden`,g+="\n\n	  ",g+=null==(s=l.pairedAttr)?void 0:s.description),g+="\n\n	  ",l.fixed?g+=`Fixed value: ${c}`:a?g+=`Default: inherited from Global attribute ${l.modelName} (default ${c})`:g+=`Default: ${c}`,g+="\n	 */",n.push(g),n.push("	"+l.name+u+": "+d+";\n")}return n.push("}"),n.join("\n")}}class m{constructor(t,e,i,s,n){d(this,"node"),d(this,"rect"),d(this,"location"),d(this,"index"),d(this,"className"),this.node=t,this.rect=e,this.location=i,this.index=s,this.className=n}}class f{constructor(t){d(this,"borders"),d(this,"borderMap"),d(this,"layoutHorizontal"),this.borders=[],this.borderMap=new Map,this.layoutHorizontal=!0}static fromJson(t,e){let i=new f(e);for(let s of(i.borders=t.map(t=>z.fromJson(t,e)),i.borders))i.borderMap.set(s.getLocation(),s);return i}toJson(){return this.borders.map(t=>t.toJson())}getLayoutHorizontal(){return this.layoutHorizontal}getBorders(){return this.borders}getBorderMap(){return this.borderMap}forEachNode(t){for(let e of this.borders)for(let i of(t(e,0),e.getChildren()))i.forEachNode(t,1)}setPaths(){for(let t of this.borders){let e="/border/"+t.getLocation().getName();t.setPath(e);let i=0;for(let s of t.getChildren())s.setPath(e+"/t"+i),i++}}findDropTargetNode(t,e,i){for(let s of this.borders)if(s.isShowing()){let n=s.canDrop(t,e,i);if(void 0!==n)return n}}}class O{constructor(t){d(this,"model"),d(this,"attributes"),d(this,"parent"),d(this,"children"),d(this,"rect"),d(this,"path"),d(this,"listeners"),this.model=t,this.attributes={},this.children=[],this.rect=c.empty(),this.listeners=new Map,this.path=""}getId(){let t=this.attributes.id;return void 0!==t||(t=this.model.nextUniqueId(),this.setId(t)),t}getModel(){return this.model}getType(){return this.attributes.type}getParent(){return this.parent}getChildren(){return this.children}getRect(){return this.rect}getPath(){return this.path}getOrientation(){return void 0===this.parent?this.model.isRootOrientationVertical()?h.VERT:h.HORZ:h.flip(this.parent.getOrientation())}setEventListener(t,e){this.listeners.set(t,e)}removeEventListener(t){this.listeners.delete(t)}setId(t){this.attributes.id=t}fireEvent(t,e){this.listeners.has(t)&&this.listeners.get(t)(e)}getAttr(t){let e=this.attributes[t];if(void 0===e){let i=this.getAttributeDefinitions().getModelName(t);void 0!==i&&(e=this.model.getAttribute(i))}return e}forEachNode(t,e){for(let i of(t(this,e),e++,this.children))i.forEachNode(t,e)}setPaths(t){let e=0;for(let i of this.children){let s=t;"row"===i.getType()?s+="/r"+e:"tabset"===i.getType()?s+="/ts"+e:"tab"===i.getType()&&(s+="/t"+e),i.path=s,i.setPaths(s),e++}}setParent(t){this.parent=t}setRect(t){this.rect=t}setPath(t){this.path=t}setWeight(t){this.attributes.weight=t}setSelected(t){this.attributes.selected=t}findDropTargetNode(t,e,i,s){let n;if(this.rect.contains(i,s)){if(void 0!==this.model.getMaximizedTabset(t))n=this.model.getMaximizedTabset(t).canDrop(e,i,s);else if(void 0===(n=this.canDrop(e,i,s))&&0!==this.children.length){for(let o of this.children)if(void 0!==(n=o.findDropTargetNode(t,e,i,s)))break}}return n}canDrop(t,e,i){}canDockInto(t,e){if(null!=e){if(e.location===u.CENTER&&!1===e.node.isEnableDrop()||e.location===u.CENTER&&"tabset"===t.getType()&&void 0!==t.getName()||e.location!==u.CENTER&&!1===e.node.isEnableDivide())return!1;if(this.model.getOnAllowDrop())return this.model.getOnAllowDrop()(t,e)}return!0}removeChild(t){let e=this.children.indexOf(t);return -1!==e&&this.children.splice(e,1),e}addChild(t,e){return null!=e?this.children.splice(e,0,t):(this.children.push(t),e=this.children.length-1),t.parent=this,e}removeAll(){this.children=[]}styleWithPosition(t){return null==t&&(t={}),this.rect.styleWithPosition(t)}isEnableDivide(){return!0}toAttributeString(){return JSON.stringify(this.attributes,void 0,"	")}}let A=class t extends O{constructor(e,i,s=!0){super(e),d(this,"tabRect",c.empty()),d(this,"moveableElement"),d(this,"tabStamp"),d(this,"renderedName"),d(this,"extra"),d(this,"visible"),d(this,"rendered"),d(this,"scrollTop"),d(this,"scrollLeft"),this.extra={},this.moveableElement=null,this.tabStamp=null,this.rendered=!1,this.visible=!1,t.attributeDefinitions.fromJson(i,this.attributes),!0===s&&e.addNode(this)}static fromJson(e,i,s=!0){return new t(i,e,s)}getName(){return this.getAttr("name")}getHelpText(){return this.getAttr("helpText")}getComponent(){return this.getAttr("component")}getWindowId(){return this.parent instanceof B?this.parent.getWindowId():H.MAIN_WINDOW_ID}getWindow(){let t=this.model.getwindowsMap().get(this.getWindowId());if(t)return t.window}getConfig(){return this.attributes.config}getExtraData(){return this.extra}isPoppedOut(){return this.getWindowId()!==H.MAIN_WINDOW_ID}isSelected(){return this.getParent().getSelectedNode()===this}getIcon(){return this.getAttr("icon")}isEnableClose(){return this.getAttr("enableClose")}getCloseType(){return this.getAttr("closeType")}isEnablePopout(){return this.getAttr("enablePopout")}isEnablePopoutIcon(){return this.getAttr("enablePopoutIcon")}isEnablePopoutOverlay(){return this.getAttr("enablePopoutOverlay")}isEnableDrag(){return this.getAttr("enableDrag")}isEnableRename(){return this.getAttr("enableRename")}isEnableWindowReMount(){return this.getAttr("enableWindowReMount")}getClassName(){return this.getAttr("className")}getContentClassName(){return this.getAttr("contentClassName")}getTabSetClassName(){return this.getAttr("tabsetClassName")}isEnableRenderOnDemand(){return this.getAttr("enableRenderOnDemand")}getMinWidth(){return this.getAttr("minWidth")}getMinHeight(){return this.getAttr("minHeight")}getMaxWidth(){return this.getAttr("maxWidth")}getMaxHeight(){return this.getAttr("maxHeight")}isVisible(){return this.visible}toJson(){let e={};return t.attributeDefinitions.toJson(e,this.attributes),e}saveScrollPosition(){this.moveableElement&&(this.scrollLeft=this.moveableElement.scrollLeft,this.scrollTop=this.moveableElement.scrollTop)}restoreScrollPosition(){this.scrollTop&&requestAnimationFrame(()=>{this.moveableElement&&this.scrollTop&&(this.moveableElement.scrollTop=this.scrollTop,this.moveableElement.scrollLeft=this.scrollLeft)})}setRect(t){t.equals(this.rect)||(this.fireEvent("resize",{rect:t}),this.rect=t)}setVisible(t){t!==this.visible&&(this.visible=t,this.fireEvent("visibility",{visible:t}))}getScrollTop(){return this.scrollTop}setScrollTop(t){this.scrollTop=t}getScrollLeft(){return this.scrollLeft}setScrollLeft(t){this.scrollLeft=t}isRendered(){return this.rendered}setRendered(t){this.rendered=t}getTabRect(){return this.tabRect}setTabRect(t){this.tabRect=t}getTabStamp(){return this.tabStamp}setTabStamp(t){this.tabStamp=t}getMoveableElement(){return this.moveableElement}setMoveableElement(t){this.moveableElement=t}setRenderedName(t){this.renderedName=t}getNameForOverflowMenu(){let t=this.getAttr("altName");return void 0!==t?t:this.renderedName}setName(t){this.attributes.name=t}delete(){this.parent.remove(this),this.fireEvent("close",{})}updateAttrs(e){t.attributeDefinitions.update(e,this.attributes)}getAttributeDefinitions(){return t.attributeDefinitions}setBorderWidth(t){this.attributes.borderWidth=t}setBorderHeight(t){this.attributes.borderHeight=t}static getAttributeDefinitions(){return t.attributeDefinitions}static createAttributeDefinitions(){let e=new E;return e.add("type",t.TYPE,!0).setType(b.STRING).setFixed(),e.add("id",void 0).setType(b.STRING).setDescription("the unique id of the tab, if left undefined a uuid will be assigned"),e.add("name","[Unnamed Tab]").setType(b.STRING).setDescription("name of tab to be displayed in the tab button"),e.add("altName",void 0).setType(b.STRING).setDescription("if there is no name specifed then this value will be used in the overflow menu"),e.add("helpText",void 0).setType(b.STRING).setDescription("An optional help text for the tab to be displayed upon tab hover."),e.add("component",void 0).setType(b.STRING).setDescription("string identifying which component to run (for factory)"),e.add("config",void 0).setType("any").setDescription("a place to hold json config for the hosted component"),e.add("tabsetClassName",void 0).setType(b.STRING).setDescription("class applied to parent tabset when this is the only tab and it is stretched to fill the tabset"),e.add("enableWindowReMount",!1).setType(b.BOOLEAN).setDescription("if enabled the tab will re-mount when popped out/in"),e.addInherited("enableClose","tabEnableClose").setType(b.BOOLEAN).setDescription("allow user to close tab via close button"),e.addInherited("closeType","tabCloseType").setType("ICloseType").setDescription("see values in ICloseType"),e.addInherited("enableDrag","tabEnableDrag").setType(b.BOOLEAN).setDescription("allow user to drag tab to new location"),e.addInherited("enableRename","tabEnableRename").setType(b.BOOLEAN).setDescription("allow user to rename tabs by double clicking"),e.addInherited("className","tabClassName").setType(b.STRING).setDescription("class applied to tab button"),e.addInherited("contentClassName","tabContentClassName").setType(b.STRING).setDescription("class applied to tab content"),e.addInherited("icon","tabIcon").setType(b.STRING).setDescription("the tab icon"),e.addInherited("enableRenderOnDemand","tabEnableRenderOnDemand").setType(b.BOOLEAN).setDescription("whether to avoid rendering component until tab is visible"),e.addInherited("enablePopout","tabEnablePopout").setType(b.BOOLEAN).setAlias("enableFloat").setDescription("enable popout (in popout capable browser)"),e.addInherited("enablePopoutIcon","tabEnablePopoutIcon").setType(b.BOOLEAN).setDescription("whether to show the popout icon in the tabset header if this tab enables popouts"),e.addInherited("enablePopoutOverlay","tabEnablePopoutOverlay").setType(b.BOOLEAN).setDescription(`if this tab will not work correctly in a popout window when the main window is backgrounded (inactive)
            then enabling this option will gray out this tab`),e.addInherited("borderWidth","tabBorderWidth").setType(b.NUMBER).setDescription("width when added to border, -1 will use border size"),e.addInherited("borderHeight","tabBorderHeight").setType(b.NUMBER).setDescription("height when added to border, -1 will use border size"),e.addInherited("minWidth","tabMinWidth").setType(b.NUMBER).setDescription("the min width of this tab"),e.addInherited("minHeight","tabMinHeight").setType(b.NUMBER).setDescription("the min height of this tab"),e.addInherited("maxWidth","tabMaxWidth").setType(b.NUMBER).setDescription("the max width of this tab"),e.addInherited("maxHeight","tabMaxHeight").setType(b.NUMBER).setDescription("the max height of this tab"),e}};function R(){return"undefined"!=typeof window&&window.matchMedia&&window.matchMedia("(hover: hover) and (pointer: fine)").matches}function L(t,e,i){let n,o=e.getName(),r=e.getName();void 0===i&&(i=0),void 0===n&&void 0!==e.getIcon()&&(n=0!==i?(0,s.jsx)("img",{style:{width:"1em",height:"1em",transform:"rotate("+i+"deg)"},src:e.getIcon(),alt:"leadingContent"}):(0,s.jsx)("img",{style:{width:"1em",height:"1em"},src:e.getIcon(),alt:"leadingContent"}));let a={leading:n,content:o,name:r,buttons:[]};return t.customizeTab(e,a),e.setRenderedName(a.name),a}function w(t){let e=!1;return t.nativeEvent instanceof MouseEvent&&(0!==t.nativeEvent.button||t.ctrlKey||t.altKey||t.metaKey||t.shiftKey)&&(e=!0),e}function N(t,e){for(let i of[...v("iframe",e),...v("webview",e)])i.style.pointerEvents=t?"auto":"none"}function v(t,e){return[...e.getElementsByTagName(t)]}function x(t,e,i,s,n){e.preventDefault();let o=t=>{t.preventDefault(),i(t.clientX,t.clientY)},r=t=>{t.preventDefault(),n()},a=()=>{t.removeEventListener("pointermove",o),t.removeEventListener("pointerup",a),t.removeEventListener("pointercancel",r),s()};t.addEventListener("pointermove",o),t.addEventListener("pointerup",a),t.addEventListener("pointercancel",r)}function y(t){if(t instanceof A)return t.isEnablePopout();if(t instanceof B){for(let e of t.getChildren())if(!1===e.isEnablePopout())return!1;return!0}return!1}function D(t,e){let i=t.getAttribute("style");return i!==e.getAttribute("style")&&(i?e.setAttribute("style",i):e.removeAttribute("style"),!0)}function S(t,e){if(void 0!==t&&(t instanceof B||t instanceof z)){let i=t.getSelected();-1!==i&&(e===i&&t.getChildren().length>0?e>=t.getChildren().length&&t.setSelected(t.getChildren().length-1):e<i?t.setSelected(i-1):e>i||t.setSelected(-1))}}function I(){return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,t=>(t^crypto.getRandomValues(new Uint8Array(1))[0]&15>>t/4).toString(16))}d(A,"TYPE","tab"),d(A,"attributeDefinitions",A.createAttributeDefinitions());let M=class t extends O{constructor(e,i){super(e),d(this,"tabStripRect",c.empty()),d(this,"contentRect",c.empty()),d(this,"calculatedMinHeight"),d(this,"calculatedMinWidth"),d(this,"calculatedMaxHeight"),d(this,"calculatedMaxWidth"),this.calculatedMinHeight=0,this.calculatedMinWidth=0,this.calculatedMaxHeight=0,this.calculatedMaxWidth=0,t.attributeDefinitions.fromJson(i,this.attributes),e.addNode(this)}static fromJson(e,i,s){let n=new t(i,e);if(null!=e.children)for(let t of e.children){let e=A.fromJson(t,i);n.addChild(e)}return 0===n.children.length&&n.setSelected(-1),e.maximized&&!0===e.maximized&&(s.maximizedTabSet=n),e.active&&!0===e.active&&(s.activeTabSet=n),n}getName(){return this.getAttr("name")}isEnableActiveIcon(){return this.getAttr("enableActiveIcon")}getSelected(){let t=this.attributes.selected;return void 0!==t?t:-1}getSelectedNode(){let t=this.getSelected();if(-1!==t)return this.children[t]}getWeight(){return this.getAttr("weight")}getAttrMinWidth(){return this.getAttr("minWidth")}getAttrMinHeight(){return this.getAttr("minHeight")}getMinWidth(){return this.calculatedMinWidth}getMinHeight(){return this.calculatedMinHeight}getMinSize(t){return t===h.HORZ?this.getMinWidth():this.getMinHeight()}getAttrMaxWidth(){return this.getAttr("maxWidth")}getAttrMaxHeight(){return this.getAttr("maxHeight")}getMaxWidth(){return this.calculatedMaxWidth}getMaxHeight(){return this.calculatedMaxHeight}getMaxSize(t){return t===h.HORZ?this.getMaxWidth():this.getMaxHeight()}getConfig(){return this.attributes.config}isMaximized(){return this.model.getMaximizedTabset(this.getWindowId())===this}isActive(){return this.model.getActiveTabset(this.getWindowId())===this}isEnableDeleteWhenEmpty(){return this.getAttr("enableDeleteWhenEmpty")}isEnableDrop(){return this.getAttr("enableDrop")}isEnableTabWrap(){return this.getAttr("enableTabWrap")}isEnableDrag(){return this.getAttr("enableDrag")}isEnableDivide(){return this.getAttr("enableDivide")}isEnableMaximize(){return this.getAttr("enableMaximize")}isEnableClose(){return this.getAttr("enableClose")}isEnableSingleTabStretch(){return this.getAttr("enableSingleTabStretch")}isEnableTabStrip(){return this.getAttr("enableTabStrip")}isAutoSelectTab(){return this.getAttr("autoSelectTab")}isEnableTabScrollbar(){return this.getAttr("enableTabScrollbar")}getClassNameTabStrip(){return this.getAttr("classNameTabStrip")}getTabLocation(){return this.getAttr("tabLocation")}toJson(){let e={};return t.attributeDefinitions.toJson(e,this.attributes),e.children=this.children.map(t=>t.toJson()),this.isActive()&&(e.active=!0),this.isMaximized()&&(e.maximized=!0),e}calcMinMaxSize(){for(let t of(this.calculatedMinHeight=this.getAttrMinHeight(),this.calculatedMinWidth=this.getAttrMinWidth(),this.calculatedMaxHeight=this.getAttrMaxHeight(),this.calculatedMaxWidth=this.getAttrMaxWidth(),this.children))this.calculatedMinWidth=Math.max(this.calculatedMinWidth,t.getMinWidth()),this.calculatedMinHeight=Math.max(this.calculatedMinHeight,t.getMinHeight()),this.calculatedMaxWidth=Math.min(this.calculatedMaxWidth,t.getMaxWidth()),this.calculatedMaxHeight=Math.min(this.calculatedMaxHeight,t.getMaxHeight());this.calculatedMinHeight+=this.tabStripRect.height,this.calculatedMaxHeight+=this.tabStripRect.height}canMaximize(){return!!this.isEnableMaximize()&&(this.getModel().getMaximizedTabset(this.getWindowId())===this||this.getParent()!==this.getModel().getRoot(this.getWindowId())||1!==this.getModel().getRoot(this.getWindowId()).getChildren().length)}setContentRect(t){this.contentRect=t}getContentRect(){return this.contentRect}setTabStripRect(t){this.tabStripRect=t}setWeight(t){this.attributes.weight=t}setSelected(t){this.attributes.selected=t}getWindowId(){return this.parent.getWindowId()}canDrop(t,e,i){let s;if(t===this){let t=u.CENTER;s=new m(this,this.tabStripRect,t,-1,T.FLEXLAYOUT__OUTLINE_RECT)}else if(this.getWindowId()!==H.MAIN_WINDOW_ID&&!y(t))return;else if(this.contentRect.contains(e,i)){let t=u.CENTER;void 0===this.model.getMaximizedTabset(this.parent.getWindowId())&&(t=u.getLocation(this.contentRect,e,i)),s=new m(this,t.getDockRect(this.rect),t,-1,T.FLEXLAYOUT__OUTLINE_RECT)}else if(null!=this.tabStripRect&&this.tabStripRect.contains(e,i)){let t,n,o;if(0===this.children.length)n=(t=this.tabStripRect.clone()).y+3,o=t.height-4,t.width=2;else{let r=this.children[0];n=(t=r.getTabRect()).y,o=t.height;let a=this.tabStripRect.x,l=0;for(let o=0;o<this.children.length;o++){if((t=(r=this.children[o]).getTabRect()).y!==n&&(n=t.y,a=this.tabStripRect.x),l=t.x+t.width/2,a<=e&&e<l&&t.y<i&&i<t.getBottom()){let e=u.CENTER,i=new c(t.x-2,t.y,3,t.height);if(!(this.rect.x<t.x&&t.x<this.rect.getRight()))return;s=new m(this,i,e,o,T.FLEXLAYOUT__OUTLINE_RECT);break}a=l}}if(null==s&&t.getRight()<this.rect.getRight()){let e=u.CENTER;s=new m(this,new c(t.getRight()-2,n,3,o),e,this.children.length,T.FLEXLAYOUT__OUTLINE_RECT)}}if(t.canDockInto(t,s))return s}delete(){this.parent.removeChild(this)}remove(t){let e=this.removeChild(t);this.model.tidy(),S(this,e)}drop(e,i,s,n){if(this===e)return;let o=e.getParent(),r=0;if(void 0!==o&&(r=o.removeChild(e),o instanceof z&&o.getSelected()===r?o.setSelected(-1):S(o,r)),e instanceof A&&o===this&&r<s&&s>0&&s--,i===u.CENTER){let t=s;if(-1===t&&(t=this.children.length),e instanceof A)this.addChild(e,t),(n||!1!==n&&this.isAutoSelectTab())&&this.setSelected(t);else if(e instanceof U)e.forEachNode((e,i)=>{e instanceof A&&(this.addChild(e,t),t++)},0);else{for(let i=0;i<e.getChildren().length;i++){let s=e.getChildren()[i];this.addChild(s,t),t++}-1===this.getSelected()&&this.children.length>0&&this.setSelected(0)}this.model.setActiveTabset(this,this.parent.getWindowId())}else{let s=e;if(e instanceof A){let i=this.model.getOnCreateTabSet();(s=new t(this.model,i?i(e):{})).addChild(e),o=s}else if(e instanceof U){let t=this.getParent();if(e.getOrientation()===t.getOrientation()&&(i.getOrientation()===t.getOrientation()||i===u.CENTER)){let t=new U(this.model,this.getWindowId(),{});t.addChild(e),s=t}}else s=e;let n=this.parent,r=n.getChildren().indexOf(this);if(n.getOrientation()===i.orientation)s.setWeight(this.getWeight()/2),this.setWeight(this.getWeight()/2),n.addChild(s,r+i.indexPlus);else{let t=new U(this.model,this.getWindowId(),{});t.setWeight(this.getWeight()),t.addChild(this),this.setWeight(50),s.setWeight(50),t.addChild(s,i.indexPlus),n.removeChild(this),n.addChild(t,r)}s instanceof t&&this.model.setActiveTabset(s,this.getWindowId())}this.model.tidy()}updateAttrs(e){t.attributeDefinitions.update(e,this.attributes)}getAttributeDefinitions(){return t.attributeDefinitions}static getAttributeDefinitions(){return t.attributeDefinitions}static createAttributeDefinitions(){let e=new E;return e.add("type",t.TYPE,!0).setType(b.STRING).setFixed(),e.add("id",void 0).setType(b.STRING).setDescription("the unique id of the tab set, if left undefined a uuid will be assigned"),e.add("weight",100).setType(b.NUMBER).setDescription("relative weight for sizing of this tabset in parent row"),e.add("selected",0).setType(b.NUMBER).setDescription("index of selected/visible tab in tabset"),e.add("name",void 0).setType(b.STRING),e.add("config",void 0).setType("any").setDescription("a place to hold json config used in your own code"),e.addInherited("enableDeleteWhenEmpty","tabSetEnableDeleteWhenEmpty").setDescription("whether to delete this tabset when is has no tabs"),e.addInherited("enableDrop","tabSetEnableDrop").setDescription("allow user to drag tabs into this tabset"),e.addInherited("enableDrag","tabSetEnableDrag").setDescription("allow user to drag tabs out this tabset"),e.addInherited("enableDivide","tabSetEnableDivide").setDescription("allow user to drag tabs to region of this tabset, splitting into new tabset"),e.addInherited("enableMaximize","tabSetEnableMaximize").setDescription("allow user to maximize tabset to fill view via maximize button"),e.addInherited("enableClose","tabSetEnableClose").setDescription("allow user to close tabset via a close button"),e.addInherited("enableSingleTabStretch","tabSetEnableSingleTabStretch").setDescription("if the tabset has only a single tab then stretch the single tab to fill area and display in a header style"),e.addInherited("classNameTabStrip","tabSetClassNameTabStrip").setDescription("a class name to apply to the tab strip"),e.addInherited("enableTabStrip","tabSetEnableTabStrip").setDescription("enable tab strip and allow multiple tabs in this tabset"),e.addInherited("minWidth","tabSetMinWidth").setDescription("minimum width (in px) for this tabset"),e.addInherited("minHeight","tabSetMinHeight").setDescription("minimum height (in px) for this tabset"),e.addInherited("maxWidth","tabSetMaxWidth").setDescription("maximum width (in px) for this tabset"),e.addInherited("maxHeight","tabSetMaxHeight").setDescription("maximum height (in px) for this tabset"),e.addInherited("enableTabWrap","tabSetEnableTabWrap").setDescription("wrap tabs onto multiple lines"),e.addInherited("tabLocation","tabSetTabLocation").setDescription("the location of the tabs either top or bottom"),e.addInherited("autoSelectTab","tabSetAutoSelectTab").setType(b.BOOLEAN).setDescription("whether to select new/moved tabs in tabset"),e.addInherited("enableActiveIcon","tabSetEnableActiveIcon").setType(b.BOOLEAN).setDescription("whether the active icon (*) should be displayed when the tabset is active"),e.addInherited("enableTabScrollbar","tabSetEnableTabScrollbar").setType(b.BOOLEAN).setDescription("whether to show a mini scrollbar for the tabs"),e}};d(M,"TYPE","tabset"),d(M,"attributeDefinitions",M.createAttributeDefinitions());let B=M,C=class t extends O{constructor(e,i,s){super(e),d(this,"windowId"),d(this,"minHeight"),d(this,"minWidth"),d(this,"maxHeight"),d(this,"maxWidth"),this.windowId=i,this.minHeight=F,this.minWidth=F,this.maxHeight=Y,this.maxWidth=Y,t.attributeDefinitions.fromJson(s,this.attributes),this.normalizeWeights(),e.addNode(this)}static fromJson(e,i,s){let n=new t(i,s.windowId,e);if(null!=e.children)for(let o of e.children)if(o.type===B.TYPE){let t=B.fromJson(o,i,s);n.addChild(t)}else{let e=t.fromJson(o,i,s);n.addChild(e)}return n}getWeight(){return this.attributes.weight}toJson(){let e={};for(let i of(t.attributeDefinitions.toJson(e,this.attributes),e.children=[],this.children))e.children.push(i.toJson());return e}getWindowId(){return this.windowId}setWindowId(t){this.windowId=t}setWeight(t){this.attributes.weight=t}getSplitterBounds(t){let e=this.getOrientation()===h.HORZ,i=this.getChildren(),s=this.model.getSplitterSize(),n=i[0].getRect(),o=i[i.length-1].getRect(),r=e?[n.x,o.getRight()]:[n.y,o.getBottom()],a=e?[n.x,o.getRight()]:[n.y,o.getBottom()];for(let n=0;n<t;n++){let t=i[n];r[0]+=e?t.getMinWidth():t.getMinHeight(),a[0]+=e?t.getMaxWidth():t.getMaxHeight(),n>0&&(r[0]+=s,a[0]+=s)}for(let n=i.length-1;n>=t;n--){let t=i[n];r[1]-=(e?t.getMinWidth():t.getMinHeight())+s,a[1]-=(e?t.getMaxWidth():t.getMaxHeight())+s}return[Math.max(a[1],r[0]),Math.min(a[0],r[1])]}getSplitterInitials(t){let e=this.getOrientation()===h.HORZ,i=this.getChildren(),s=this.model.getSplitterSize(),n=[],o=0;for(let t=0;t<i.length;t++){let s=i[t].getRect(),r=e?s.width:s.height;n.push(r),o+=r}let r=i[t].getRect();return{initialSizes:n,sum:o,startPosition:(e?r.x:r.y)-s}}calculateSplit(t,e,i,s,n){let o=this.getOrientation()===h.HORZ,r=this.getChildren(),a=r[t],l=o?a.getMaxWidth():a.getMaxHeight(),d=[...i];if(e<n){let i=n-e,s=0;d[t]+i>l?(s=d[t]+i-l,d[t]=l):d[t]+=i;for(let e=t-1;e>=0;e--){let t=r[e],s=o?t.getMinWidth():t.getMinHeight();if(d[e]-i>s){d[e]-=i;break}i-=d[e]-s,d[e]=s}for(let e=t+1;e<r.length;e++){let t=r[e],i=o?t.getMaxWidth():t.getMaxHeight();if(d[e]+s<i){d[e]+=s;break}s-=i-d[e],d[e]=i}}else{let i=e-n,s=0;d[t-1]+i>l?(s=d[t-1]+i-l,d[t-1]=l):d[t-1]+=i;for(let e=t;e<r.length;e++){let t=r[e],s=o?t.getMinWidth():t.getMinHeight();if(d[e]-i>s){d[e]-=i;break}i-=d[e]-s,d[e]=s}for(let e=t-1;e>=0;e--){let t=r[e],i=o?t.getMaxWidth():t.getMaxHeight();if(d[e]+s<i){d[e]+=s;break}s-=i-d[e],d[e]=i}}return d.map(t=>100*Math.max(.1,t)/s)}getMinSize(t){return t===h.HORZ?this.getMinWidth():this.getMinHeight()}getMinWidth(){return this.minWidth}getMinHeight(){return this.minHeight}getMaxSize(t){return t===h.HORZ?this.getMaxWidth():this.getMaxHeight()}getMaxWidth(){return this.maxWidth}getMaxHeight(){return this.maxHeight}calcMinMaxSize(){this.minHeight=F,this.minWidth=F,this.maxHeight=Y,this.maxWidth=Y;let t=!0;for(let e of this.children)e.calcMinMaxSize(),this.getOrientation()===h.VERT?(this.minHeight+=e.getMinHeight(),this.maxHeight+=e.getMaxHeight(),t||(this.minHeight+=this.model.getSplitterSize(),this.maxHeight+=this.model.getSplitterSize()),this.minWidth=Math.max(this.minWidth,e.getMinWidth()),this.maxWidth=Math.min(this.maxWidth,e.getMaxWidth())):(this.minWidth+=e.getMinWidth(),this.maxWidth+=e.getMaxWidth(),t||(this.minWidth+=this.model.getSplitterSize(),this.maxWidth+=this.model.getSplitterSize()),this.minHeight=Math.max(this.minHeight,e.getMinHeight()),this.maxHeight=Math.min(this.maxHeight,e.getMaxHeight())),t=!1}tidy(){let e=0;for(;e<this.children.length;){let i=this.children[e];if(i instanceof t){i.tidy();let s=i.getChildren();if(0===s.length)this.removeChild(i);else if(1===s.length){let n=s[0];if(this.removeChild(i),n instanceof t){let t=0,s=n.getChildren();for(let e of s)t+=e.getWeight();for(let n=0;n<s.length;n++){let o=s[n];o.setWeight(i.getWeight()*o.getWeight()/t),this.addChild(o,e+n)}}else n.setWeight(i.getWeight()),this.addChild(n,e)}else e++}else i instanceof B&&0===i.getChildren().length&&i.isEnableDeleteWhenEmpty()?(this.removeChild(i),i===this.model.getMaximizedTabset(this.windowId)&&this.model.setMaximizedTabset(void 0,this.windowId)):e++}if(this===this.model.getRoot(this.windowId)&&0===this.children.length){let t=this.model.getOnCreateTabSet(),e=t?t():{};e={...e,selected:-1};let i=new B(this.model,e);this.model.setActiveTabset(i,this.windowId),this.addChild(i)}}canDrop(t,e,i){let s,n=i-this.rect.y,o=e-this.rect.x,r=this.rect.width,a=this.rect.height;if(this.getWindowId()===H.MAIN_WINDOW_ID||y(t)){if(this.model.isEnableEdgeDock()&&void 0===this.parent){if(e<this.rect.x+10&&n>a/2-50&&n<a/2+50){let t=u.LEFT,e=t.getDockRect(this.rect);e.width=e.width/2,s=new m(this,e,t,-1,T.FLEXLAYOUT__OUTLINE_RECT_EDGE)}else if(e>this.rect.getRight()-10&&n>a/2-50&&n<a/2+50){let t=u.RIGHT,e=t.getDockRect(this.rect);e.width=e.width/2,e.x+=e.width,s=new m(this,e,t,-1,T.FLEXLAYOUT__OUTLINE_RECT_EDGE)}else if(i<this.rect.y+10&&o>r/2-50&&o<r/2+50){let t=u.TOP,e=t.getDockRect(this.rect);e.height=e.height/2,s=new m(this,e,t,-1,T.FLEXLAYOUT__OUTLINE_RECT_EDGE)}else if(i>this.rect.getBottom()-10&&o>r/2-50&&o<r/2+50){let t=u.BOTTOM,e=t.getDockRect(this.rect);e.height=e.height/2,e.y+=e.height,s=new m(this,e,t,-1,T.FLEXLAYOUT__OUTLINE_RECT_EDGE)}if(void 0!==s&&!t.canDockInto(t,s))return}return s}}drop(e,i,s){let n,o=e.getParent();if(o&&o.removeChild(e),void 0!==o&&o instanceof B&&o.setSelected(0),void 0!==o&&o instanceof z&&o.setSelected(-1),e instanceof B||e instanceof t)(n=e)instanceof t&&n.getOrientation()===this.getOrientation()&&(i.getOrientation()===this.getOrientation()||i===u.CENTER)&&(n=new t(this.model,this.windowId,{})).addChild(e);else{let t=this.model.getOnCreateTabSet();(n=new B(this.model,t?t(e):{})).addChild(e)}let r=this.children.reduce((t,e)=>t+e.getWeight(),0);0===r&&(r=100),n.setWeight(r/3);let a=!this.model.isRootOrientationVertical();if(i===u.CENTER)-1===s?this.addChild(n,this.children.length):this.addChild(n,s);else if(a&&i===u.LEFT||!a&&i===u.TOP)this.addChild(n,0);else if(a&&i===u.RIGHT||!a&&i===u.BOTTOM)this.addChild(n);else if(a&&i===u.TOP||!a&&i===u.LEFT){let e=new t(this.model,this.windowId,{}),i=new t(this.model,this.windowId,{});for(let t of(i.setWeight(75),n.setWeight(25),this.children))i.addChild(t);this.removeAll(),e.addChild(n),e.addChild(i),this.addChild(e)}else if(a&&i===u.BOTTOM||!a&&i===u.RIGHT){let e=new t(this.model,this.windowId,{}),i=new t(this.model,this.windowId,{});for(let t of(i.setWeight(75),n.setWeight(25),this.children))i.addChild(t);this.removeAll(),e.addChild(i),e.addChild(n),this.addChild(e)}n instanceof B&&this.model.setActiveTabset(n,this.windowId),this.model.tidy()}isEnableDrop(){return!0}getAttributeDefinitions(){return t.attributeDefinitions}updateAttrs(e){t.attributeDefinitions.update(e,this.attributes)}static getAttributeDefinitions(){return t.attributeDefinitions}normalizeWeights(){let t=0;for(let e of this.children)t+=e.getWeight();for(let e of(0===t&&(t=1),this.children))e.setWeight(Math.max(.001,100*e.getWeight()/t))}static createAttributeDefinitions(){let e=new E;return e.add("type",t.TYPE,!0).setType(b.STRING).setFixed(),e.add("id",void 0).setType(b.STRING).setDescription("the unique id of the row, if left undefined a uuid will be assigned"),e.add("weight",100).setType(b.NUMBER).setDescription("relative weight for sizing of this row in parent row"),e}};d(C,"TYPE","row"),d(C,"attributeDefinitions",C.createAttributeDefinitions());let U=C;class W{constructor(t,e){d(this,"_windowId"),d(this,"_layout"),d(this,"_rect"),d(this,"_window"),d(this,"_root"),d(this,"_maximizedTabSet"),d(this,"_activeTabSet"),d(this,"_toScreenRectFunction"),this._windowId=t,this._rect=e,this._toScreenRectFunction=t=>t}visitNodes(t){this.root.forEachNode(t,0)}get windowId(){return this._windowId}get rect(){return this._rect}get layout(){return this._layout}get window(){return this._window}get root(){return this._root}get maximizedTabSet(){return this._maximizedTabSet}get activeTabSet(){return this._activeTabSet}set rect(t){this._rect=t}set layout(t){this._layout=t}set window(t){this._window=t}set root(t){this._root=t}set maximizedTabSet(t){this._maximizedTabSet=t}set activeTabSet(t){this._activeTabSet=t}get toScreenRectFunction(){return this._toScreenRectFunction}set toScreenRectFunction(t){this._toScreenRectFunction=t}toJson(){return this._window&&this._window.screenTop>-1e4&&(this.rect=new c(this._window.screenLeft,this._window.screenTop,this._window.outerWidth,this._window.outerHeight)),{layout:this.root.toJson(),rect:this.rect.toJson()}}static fromJson(t,e,i){let s=e.getwindowsMap().size,n=t.rect?c.fromJson(t.rect):new c(50+50*s,50+50*s,600,400);n.snap(10);let o=new W(i,n);return o.root=U.fromJson(t.layout,e,o),o}}let F=0,Y=99999,P=class t{constructor(){d(this,"attributes"),d(this,"idMap"),d(this,"changeListeners"),d(this,"borders"),d(this,"onAllowDrop"),d(this,"onCreateTabSet"),d(this,"windows"),d(this,"rootWindow"),this.attributes={},this.idMap=new Map,this.borders=new f(this),this.windows=new Map,this.rootWindow=new W(t.MAIN_WINDOW_ID,c.empty()),this.windows.set(t.MAIN_WINDOW_ID,this.rootWindow),this.changeListeners=[]}doAction(e){var i;let s;switch(e.type){case p.ADD_NODE:{let t=new A(this,e.data.json,!0),i=this.idMap.get(e.data.toNode);(i instanceof B||i instanceof z||i instanceof U)&&(i.drop(t,u.getByName(e.data.location),e.data.index,e.data.select),s=t);break}case p.MOVE_NODE:{let t=this.idMap.get(e.data.fromNode);if(t instanceof A||t instanceof B||t instanceof U){t===this.getMaximizedTabset(t.getWindowId())&&(this.windows.get(t.getWindowId()).maximizedTabSet=void 0);let i=this.idMap.get(e.data.toNode);(i instanceof B||i instanceof z||i instanceof U)&&i.drop(t,u.getByName(e.data.location),e.data.index,e.data.select)}this.removeEmptyWindows();break}case p.DELETE_TAB:{let t=this.idMap.get(e.data.node);t instanceof A&&t.delete(),this.removeEmptyWindows();break}case p.DELETE_TABSET:{let t=this.idMap.get(e.data.node);if(t instanceof B){let e=[...t.getChildren()];for(let t=0;t<e.length;t++){let i=e[t];i.isEnableClose()&&i.delete()}0===t.getChildren().length&&t.delete(),this.tidy()}this.removeEmptyWindows();break}case p.POPOUT_TABSET:{let t=this.idMap.get(e.data.node);if(t instanceof B){let e=t.isMaximized(),i=this.windows.get(t.getWindowId()),s=I(),n=new W(s,i.toScreenRectFunction(t.getRect())),o=U.fromJson({type:"row",children:[]},this,n);n.root=o,this.windows.set(s,n),o.drop(t,u.CENTER,0),e&&(this.rootWindow.maximizedTabSet=void 0)}this.removeEmptyWindows();break}case p.POPOUT_TAB:{let t=this.idMap.get(e.data.node);if(t instanceof A){let e=I(),i=c.empty();i=t.getParent()instanceof B?t.getParent().getRect():t.getParent().getContentRect();let s=new W(e,this.windows.get(t.getWindowId()).toScreenRectFunction(i)),n=I(),o=U.fromJson({type:"row",children:[{type:"tabset",id:n}]},this,s);s.root=o,this.windows.set(e,s),this.idMap.get(n).drop(t,u.CENTER,0,!0)}this.removeEmptyWindows();break}case p.CLOSE_WINDOW:{let s=this.windows.get(e.data.windowId);s&&(null==(i=this.rootWindow.root)||i.drop(s.root,u.CENTER,-1),this.rootWindow.visitNodes((e,i)=>{e instanceof U&&e.setWindowId(t.MAIN_WINDOW_ID)}),this.windows.delete(e.data.windowId));break}case p.CREATE_WINDOW:{let t=I(),i=new W(t,c.fromJson(e.data.rect)),n=U.fromJson(e.data.layout,this,i);i.root=n,this.windows.set(t,i),s=t;break}case p.RENAME_TAB:{let t=this.idMap.get(e.data.node);t instanceof A&&t.setName(e.data.text);break}case p.SELECT_TAB:{let i=this.idMap.get(e.data.tabNode),s=e.data.windowId?e.data.windowId:t.MAIN_WINDOW_ID,n=this.windows.get(s);if(i instanceof A){let t=i.getParent(),e=t.getChildren().indexOf(i);t instanceof z?t.getSelected()===e?t.setSelected(-1):t.setSelected(e):t instanceof B&&(t.getSelected()!==e&&t.setSelected(e),n.activeTabSet=t)}break}case p.SET_ACTIVE_TABSET:{let i=e.data.windowId?e.data.windowId:t.MAIN_WINDOW_ID,s=this.windows.get(i);if(void 0===e.data.tabsetNode)s.activeTabSet=void 0;else{let t=this.idMap.get(e.data.tabsetNode);t instanceof B&&(s.activeTabSet=t)}break}case p.ADJUST_WEIGHTS:{let t=this.idMap.get(e.data.nodeId).getChildren();for(let i=0;i<t.length;i++)t[i].setWeight(e.data.weights[i]);break}case p.ADJUST_BORDER_SPLIT:{let t=this.idMap.get(e.data.node);t instanceof z&&t.setSize(e.data.pos);break}case p.MAXIMIZE_TOGGLE:{let i=e.data.windowId?e.data.windowId:t.MAIN_WINDOW_ID,s=this.windows.get(i),n=this.idMap.get(e.data.node);n instanceof B&&(n===s.maximizedTabSet?s.maximizedTabSet=void 0:(s.maximizedTabSet=n,s.activeTabSet=n));break}case p.UPDATE_MODEL_ATTRIBUTES:this.updateAttrs(e.data.json);break;case p.UPDATE_NODE_ATTRIBUTES:this.idMap.get(e.data.node).updateAttrs(e.data.json)}for(let t of(this.updateIdMap(),this.changeListeners))t(e);return s}getActiveTabset(e=t.MAIN_WINDOW_ID){let i=this.windows.get(e);return i&&i.activeTabSet&&this.getNodeById(i.activeTabSet.getId())?i.activeTabSet:void 0}getMaximizedTabset(e=t.MAIN_WINDOW_ID){return this.windows.get(e).maximizedTabSet}getRoot(e=t.MAIN_WINDOW_ID){return this.windows.get(e).root}isRootOrientationVertical(){return this.attributes.rootOrientationVertical}isEnableRotateBorderIcons(){return this.attributes.enableRotateBorderIcons}getBorderSet(){return this.borders}getwindowsMap(){return this.windows}visitNodes(t){for(let[e,i]of(this.borders.forEachNode(t),this.windows))i.root.forEachNode(t,0)}visitWindowNodes(e,i){this.windows.has(e)&&(e===t.MAIN_WINDOW_ID&&this.borders.forEachNode(i),this.windows.get(e).visitNodes(i))}getNodeById(t){return this.idMap.get(t)}getFirstTabSet(e=this.windows.get(t.MAIN_WINDOW_ID).root){let i=e.getChildren()[0];return i instanceof B?i:this.getFirstTabSet(i)}static fromJson(e){let i=new t;if(t.attributeDefinitions.fromJson(e.global,i.attributes),e.borders&&(i.borders=f.fromJson(e.borders,i)),e.popouts)for(let t in e.popouts){let s=e.popouts[t],n=W.fromJson(s,i,t);i.windows.set(t,n)}return i.rootWindow.root=U.fromJson(e.layout,i,i.getwindowsMap().get(t.MAIN_WINDOW_ID)),i.tidy(),i}toJson(){let e={};t.attributeDefinitions.toJson(e,this.attributes),this.visitNodes(t=>{t.fireEvent("save",{})});let i={};for(let[e,s]of this.windows)e!==t.MAIN_WINDOW_ID&&(i[e]=s.toJson());return{global:e,borders:this.borders.toJson(),layout:this.rootWindow.root.toJson(),popouts:i}}getSplitterSize(){return this.attributes.splitterSize}getSplitterExtra(){return this.attributes.splitterExtra}isEnableEdgeDock(){return this.attributes.enableEdgeDock}isSplitterEnableHandle(){return this.attributes.splitterEnableHandle}setOnAllowDrop(t){this.onAllowDrop=t}setOnCreateTabSet(t){this.onCreateTabSet=t}addChangeListener(t){this.changeListeners.push(t)}removeChangeListener(t){let e=this.changeListeners.findIndex(e=>e===t);-1!==e&&this.changeListeners.splice(e,1)}toString(){return JSON.stringify(this.toJson())}removeEmptyWindows(){let e=new Set;for(let[i]of this.windows)if(i!==t.MAIN_WINDOW_ID){let t=0;this.visitWindowNodes(i,e=>{e instanceof A&&t++}),0===t&&e.add(i)}for(let t of e)this.windows.delete(t)}setActiveTabset(t,e){let i=this.windows.get(e);i&&(t?i.activeTabSet=t:i.activeTabSet=void 0)}setMaximizedTabset(t,e){let i=this.windows.get(e);i&&(t?i.maximizedTabSet=t:i.maximizedTabSet=void 0)}updateIdMap(){this.idMap.clear(),this.visitNodes(t=>{this.idMap.set(t.getId(),t)})}addNode(t){let e=t.getId();if(this.idMap.has(e))throw Error(`Error: each node must have a unique id, duplicate id:${t.getId()}`);this.idMap.set(e,t)}findDropTargetNode(e,i,s,n){let o=this.windows.get(e).root.findDropTargetNode(e,i,s,n);return void 0===o&&e===t.MAIN_WINDOW_ID&&(o=this.borders.findDropTargetNode(i,s,n)),o}tidy(){for(let[t,e]of this.windows)e.root.tidy()}updateAttrs(e){t.attributeDefinitions.update(e,this.attributes)}nextUniqueId(){return"#"+I()}getAttribute(t){return this.attributes[t]}getOnAllowDrop(){return this.onAllowDrop}getOnCreateTabSet(){return this.onCreateTabSet}static toTypescriptInterfaces(){t.attributeDefinitions.pairAttributes("RowNode",U.getAttributeDefinitions()),t.attributeDefinitions.pairAttributes("TabSetNode",B.getAttributeDefinitions()),t.attributeDefinitions.pairAttributes("TabNode",A.getAttributeDefinitions()),t.attributeDefinitions.pairAttributes("BorderNode",z.getAttributeDefinitions());let e=[];e.push(t.attributeDefinitions.toTypescriptInterface("Global",void 0)),e.push(U.getAttributeDefinitions().toTypescriptInterface("Row",t.attributeDefinitions)),e.push(B.getAttributeDefinitions().toTypescriptInterface("TabSet",t.attributeDefinitions)),e.push(A.getAttributeDefinitions().toTypescriptInterface("Tab",t.attributeDefinitions)),e.push(z.getAttributeDefinitions().toTypescriptInterface("Border",t.attributeDefinitions)),console.log(e.join("\n"))}static createAttributeDefinitions(){let t=new E;return t.add("enableEdgeDock",!0).setType(b.BOOLEAN).setDescription("enable docking to the edges of the layout, this will show the edge indicators"),t.add("rootOrientationVertical",!1).setType(b.BOOLEAN).setDescription("the top level 'row' will layout horizontally by default, set this option true to make it layout vertically"),t.add("enableRotateBorderIcons",!0).setType(b.BOOLEAN).setDescription("boolean indicating if tab icons should rotate with the text in the left and right borders"),t.add("splitterSize",8).setType(b.NUMBER).setDescription("width in pixels of all splitters between tabsets/borders"),t.add("splitterExtra",0).setType(b.NUMBER).setDescription("additional width in pixels of the splitter hit test area"),t.add("splitterEnableHandle",!1).setType(b.BOOLEAN).setDescription("enable a small centralized handle on all splitters"),t.add("tabEnableClose",!0).setType(b.BOOLEAN),t.add("tabCloseType",1).setType("ICloseType"),t.add("tabEnablePopout",!1).setType(b.BOOLEAN).setAlias("tabEnableFloat"),t.add("tabEnablePopoutIcon",!0).setType(b.BOOLEAN),t.add("tabEnablePopoutOverlay",!1).setType(b.BOOLEAN),t.add("tabEnableDrag",!0).setType(b.BOOLEAN),t.add("tabEnableRename",!0).setType(b.BOOLEAN),t.add("tabContentClassName",void 0).setType(b.STRING),t.add("tabClassName",void 0).setType(b.STRING),t.add("tabIcon",void 0).setType(b.STRING),t.add("tabEnableRenderOnDemand",!0).setType(b.BOOLEAN),t.add("tabDragSpeed",.3).setType(b.NUMBER),t.add("tabBorderWidth",-1).setType(b.NUMBER),t.add("tabBorderHeight",-1).setType(b.NUMBER),t.add("tabSetEnableDeleteWhenEmpty",!0).setType(b.BOOLEAN),t.add("tabSetEnableDrop",!0).setType(b.BOOLEAN),t.add("tabSetEnableDrag",!0).setType(b.BOOLEAN),t.add("tabSetEnableDivide",!0).setType(b.BOOLEAN),t.add("tabSetEnableMaximize",!0).setType(b.BOOLEAN),t.add("tabSetEnableClose",!1).setType(b.BOOLEAN),t.add("tabSetEnableSingleTabStretch",!1).setType(b.BOOLEAN),t.add("tabSetAutoSelectTab",!0).setType(b.BOOLEAN),t.add("tabSetEnableActiveIcon",!1).setType(b.BOOLEAN),t.add("tabSetClassNameTabStrip",void 0).setType(b.STRING),t.add("tabSetEnableTabStrip",!0).setType(b.BOOLEAN),t.add("tabSetEnableTabWrap",!1).setType(b.BOOLEAN),t.add("tabSetTabLocation","top").setType("ITabLocation"),t.add("tabMinWidth",F).setType(b.NUMBER),t.add("tabMinHeight",F).setType(b.NUMBER),t.add("tabSetMinWidth",F).setType(b.NUMBER),t.add("tabSetMinHeight",F).setType(b.NUMBER),t.add("tabMaxWidth",Y).setType(b.NUMBER),t.add("tabMaxHeight",Y).setType(b.NUMBER),t.add("tabSetMaxWidth",Y).setType(b.NUMBER),t.add("tabSetMaxHeight",Y).setType(b.NUMBER),t.add("tabSetEnableTabScrollbar",!1).setType(b.BOOLEAN),t.add("borderSize",200).setType(b.NUMBER),t.add("borderMinSize",F).setType(b.NUMBER),t.add("borderMaxSize",Y).setType(b.NUMBER),t.add("borderEnableDrop",!0).setType(b.BOOLEAN),t.add("borderAutoSelectTabWhenOpen",!0).setType(b.BOOLEAN),t.add("borderAutoSelectTabWhenClosed",!1).setType(b.BOOLEAN),t.add("borderClassName",void 0).setType(b.STRING),t.add("borderEnableAutoHide",!1).setType(b.BOOLEAN),t.add("borderEnableTabScrollbar",!1).setType(b.BOOLEAN),t}};d(P,"MAIN_WINDOW_ID","__main_window_id__"),d(P,"attributeDefinitions",P.createAttributeDefinitions());let H=P,X=class t extends O{constructor(e,i,s){super(s),d(this,"contentRect",c.empty()),d(this,"tabHeaderRect",c.empty()),d(this,"location"),this.location=e,this.attributes.id=`border_${e.getName()}`,t.attributeDefinitions.fromJson(i,this.attributes),s.addNode(this)}static fromJson(e,i){let s=new t(u.getByName(e.location),e,i);return e.children&&(s.children=e.children.map(t=>{let e=A.fromJson(t,i);return e.setParent(s),e})),s}getLocation(){return this.location}getClassName(){return this.getAttr("className")}isHorizontal(){return this.location.orientation===h.HORZ}getSize(){let t=this.getAttr("size"),e=this.getSelected();if(-1===e)return t;{let i=this.children[e],s=this.isHorizontal()?i.getAttr("borderWidth"):i.getAttr("borderHeight");return -1===s?t:s}}getMinSize(){let t=this.getSelectedNode(),e=this.getAttr("minSize");return t&&(e=Math.max(e,this.isHorizontal()?t.getMinWidth():t.getMinHeight())),e}getMaxSize(){let t=this.getSelectedNode(),e=this.getAttr("maxSize");return t&&(e=Math.min(e,this.isHorizontal()?t.getMaxWidth():t.getMaxHeight())),e}getSelected(){return this.attributes.selected}isAutoHide(){return this.getAttr("enableAutoHide")}getSelectedNode(){if(-1!==this.getSelected())return this.children[this.getSelected()]}getOrientation(){return this.location.getOrientation()}getConfig(){return this.attributes.config}isMaximized(){return!1}isShowing(){return this.attributes.show}toJson(){let e={};return t.attributeDefinitions.toJson(e,this.attributes),e.location=this.location.getName(),e.children=this.children.map(t=>t.toJson()),e}isAutoSelectTab(t){return(null==t&&(t=-1!==this.getSelected()),t)?this.getAttr("autoSelectTabWhenOpen"):this.getAttr("autoSelectTabWhenClosed")}isEnableTabScrollbar(){return this.getAttr("enableTabScrollbar")}setSelected(t){this.attributes.selected=t}getTabHeaderRect(){return this.tabHeaderRect}setTabHeaderRect(t){this.tabHeaderRect=t}getRect(){return this.tabHeaderRect}getContentRect(){return this.contentRect}setContentRect(t){this.contentRect=t}isEnableDrop(){return this.getAttr("enableDrop")}setSize(t){let e=this.getSelected();if(-1===e)this.attributes.size=t;else{let i=this.children[e];-1===(this.isHorizontal()?i.getAttr("borderWidth"):i.getAttr("borderHeight"))?this.attributes.size=t:this.isHorizontal()?i.setBorderWidth(t):i.setBorderHeight(t)}}updateAttrs(e){t.attributeDefinitions.update(e,this.attributes)}remove(t){let e=this.removeChild(t);-1!==this.getSelected()&&S(this,e)}canDrop(t,e,i){let s;if(!(t instanceof A))return;let n=u.CENTER;if(this.tabHeaderRect.contains(e,i)){if(this.location.orientation===h.VERT)if(this.children.length>0){let t=this.children[0],i=t.getTabRect(),o=i.y,r=i.height,a=this.tabHeaderRect.x,l=0;for(let d=0;d<this.children.length;d++){if(l=(i=(t=this.children[d]).getTabRect()).x+i.width/2,e>=a&&e<l){s=new m(this,new c(i.x-2,o,3,r),n,d,T.FLEXLAYOUT__OUTLINE_RECT);break}a=l}null==s&&(s=new m(this,new c(i.getRight()-2,o,3,r),n,this.children.length,T.FLEXLAYOUT__OUTLINE_RECT))}else s=new m(this,new c(this.tabHeaderRect.x+1,this.tabHeaderRect.y+2,3,18),n,0,T.FLEXLAYOUT__OUTLINE_RECT);else if(this.children.length>0){let t=this.children[0],e=t.getTabRect(),o=e.x,r=e.width,a=this.tabHeaderRect.y,l=0;for(let d=0;d<this.children.length;d++){if(l=(e=(t=this.children[d]).getTabRect()).y+e.height/2,i>=a&&i<l){s=new m(this,new c(o,e.y-2,r,3),n,d,T.FLEXLAYOUT__OUTLINE_RECT);break}a=l}null==s&&(s=new m(this,new c(o,e.getBottom()-2,r,3),n,this.children.length,T.FLEXLAYOUT__OUTLINE_RECT))}else s=new m(this,new c(this.tabHeaderRect.x+2,this.tabHeaderRect.y+1,18,3),n,0,T.FLEXLAYOUT__OUTLINE_RECT);if(!t.canDockInto(t,s))return}else if(-1!==this.getSelected()&&this.contentRect.contains(e,i)&&(s=new m(this,this.contentRect,n,-1,T.FLEXLAYOUT__OUTLINE_RECT),!t.canDockInto(t,s)))return;return s}drop(e,i,s,n){let o=0,r=e.getParent();void 0!==r&&(o=r.removeChild(e),r!==this&&r instanceof t&&r.getSelected()===o?r.setSelected(-1):S(r,o)),e instanceof A&&r===this&&o<s&&s>0&&s--;let a=s;-1===a&&(a=this.children.length),e instanceof A&&this.addChild(e,a),(n||!1!==n&&this.isAutoSelectTab())&&this.setSelected(a),this.model.tidy()}getSplitterBounds(t,e=!1){let i=[0,0],s=e?this.getMinSize():0,n=e?this.getMaxSize():99999,o=this.model.getRoot(H.MAIN_WINDOW_ID),r=o.getRect(),a=this.model.getSplitterSize();if(this.location===u.TOP){i[0]=this.tabHeaderRect.getBottom()+s;let t=this.tabHeaderRect.getBottom()+n;i[1]=Math.max(i[0],r.getBottom()-o.getMinHeight()-a),i[1]=Math.min(i[1],t)}else if(this.location===u.LEFT){i[0]=this.tabHeaderRect.getRight()+s;let t=this.tabHeaderRect.getRight()+n;i[1]=Math.max(i[0],r.getRight()-o.getMinWidth()-a),i[1]=Math.min(i[1],t)}else if(this.location===u.BOTTOM){i[1]=this.tabHeaderRect.y-s-a;let t=this.tabHeaderRect.y-n-a;i[0]=Math.min(i[1],r.y+o.getMinHeight()),i[0]=Math.max(i[0],t)}else if(this.location===u.RIGHT){i[1]=this.tabHeaderRect.x-s-a;let t=this.tabHeaderRect.x-n-a;i[0]=Math.min(i[1],r.x+o.getMinWidth()),i[0]=Math.max(i[0],t)}return i}calculateSplit(t,e){let i=this.getSplitterBounds(e);return this.location===u.BOTTOM||this.location===u.RIGHT?Math.max(0,i[1]-e):Math.max(0,e-i[0])}getAttributeDefinitions(){return t.attributeDefinitions}static getAttributeDefinitions(){return t.attributeDefinitions}static createAttributeDefinitions(){let e=new E;return e.add("type",t.TYPE,!0).setType(b.STRING).setFixed(),e.add("selected",-1).setType(b.NUMBER).setDescription("index of selected/visible tab in border; -1 means no tab selected"),e.add("show",!0).setType(b.BOOLEAN).setDescription("show/hide this border"),e.add("config",void 0).setType("any").setDescription("a place to hold json config used in your own code"),e.addInherited("enableDrop","borderEnableDrop").setType(b.BOOLEAN).setDescription("whether tabs can be dropped into this border"),e.addInherited("className","borderClassName").setType(b.STRING).setDescription("class applied to tab button"),e.addInherited("autoSelectTabWhenOpen","borderAutoSelectTabWhenOpen").setType(b.BOOLEAN).setDescription("whether to select new/moved tabs in border when the border is already open"),e.addInherited("autoSelectTabWhenClosed","borderAutoSelectTabWhenClosed").setType(b.BOOLEAN).setDescription("whether to select new/moved tabs in border when the border is currently closed"),e.addInherited("size","borderSize").setType(b.NUMBER).setDescription("size of the tab area when selected"),e.addInherited("minSize","borderMinSize").setType(b.NUMBER).setDescription("the minimum size of the tab area"),e.addInherited("maxSize","borderMaxSize").setType(b.NUMBER).setDescription("the maximum size of the tab area"),e.addInherited("enableAutoHide","borderEnableAutoHide").setType(b.BOOLEAN).setDescription("hide border if it has zero tabs"),e.addInherited("enableTabScrollbar","borderEnableTabScrollbar").setType(b.BOOLEAN).setDescription("whether to show a mini scrollbar for the tabs"),e}};d(X,"TYPE","border"),d(X,"attributeDefinitions",X.createAttributeDefinitions());let z=X,j=!1,k=t=>{let e,{layout:i,node:o,index:r,horizontal:a}=t,[l,d]=n.useState(!1),u=n.useRef(null),g=n.useRef(null),_=n.useRef([]),b=n.useRef(void 0),E=n.useRef(void 0),m=n.useRef(0),f=n.useRef(0),O=n.useRef({initialSizes:[],sum:0,startPosition:0}),A=o.getModel().getSplitterSize(),L=o.getModel().getSplitterExtra();R()||(L=Math.max(20,L+A)-A),n.useEffect(()=>{var t,e;return null==(t=u.current)||t.addEventListener("touchstart",w,{passive:!1}),null==(e=g.current)||e.addEventListener("touchstart",w,{passive:!1}),()=>{var t,e;null==(t=u.current)||t.removeEventListener("touchstart",w),null==(e=g.current)||e.removeEventListener("touchstart",w)}},[]);let w=t=>{t.preventDefault(),t.stopImmediatePropagation()},v=t=>{var e;t.stopPropagation(),o instanceof U&&(O.current=o.getSplitterInitials(r)),N(!1,i.getCurrentDocument()),x(t.currentTarget.ownerDocument,t,D,S,y),_.current=o.getSplitterBounds(r,!0);let s=i.getRootDiv();b.current=i.getCurrentDocument().createElement("div"),b.current.style.flexDirection=a?"row":"column",b.current.className=i.getClassName(T.FLEXLAYOUT__SPLITTER_DRAG),b.current.style.cursor=o.getOrientation()===h.VERT?"ns-resize":"ew-resize",o.getModel().isSplitterEnableHandle()&&(E.current=i.getCurrentDocument().createElement("div"),E.current.className=B(T.FLEXLAYOUT__SPLITTER_HANDLE)+" "+(a?B(T.FLEXLAYOUT__SPLITTER_HANDLE_HORZ):B(T.FLEXLAYOUT__SPLITTER_HANDLE_VERT)),b.current.appendChild(E.current));let n=null==(e=u.current)?void 0:e.getBoundingClientRect(),l=new c(n.x-i.getDomRect().x,n.y-i.getDomRect().y,n.width,n.height);m.current=t.clientX-n.x,f.current=t.clientY-n.y,l.positionElement(b.current),s&&s.appendChild(b.current),d(!0),j=!0},y=()=>{let t=i.getRootDiv();t&&b.current&&t.removeChild(b.current),b.current=void 0,d(!1),j=!1},D=(t,e)=>{if(b.current){let s=i.getDomRect();s&&(o.getOrientation()===h.VERT?b.current.style.top=M(e-s.y-f.current)+"px":b.current.style.left=M(t-s.x-m.current)+"px",i.isRealtimeResize()&&I())}},S=()=>{if(b.current){I();let t=i.getRootDiv();t&&b.current&&t.removeChild(b.current),b.current=void 0}N(!0,i.getCurrentDocument()),d(!1),j=!1},I=t=>{(()=>{if(b.current){let t=0;if(t=o.getOrientation()===h.VERT?b.current.offsetTop:b.current.offsetLeft,o instanceof z){let e=o.calculateSplit(o,t);i.doAction(p.adjustBorderSplit(o.getId(),e))}else{let e=O.current,s=o.calculateSplit(r,t,e.initialSizes,e.sum,e.startPosition);i.doAction(p.adjustWeights(o.getId(),s))}}})()},M=t=>{let e=_.current,i=t;return t<e[0]&&(i=e[0]),t>e[1]&&(i=e[1]),i},B=i.getClassName,C={cursor:a?"ew-resize":"ns-resize",flexDirection:a?"column":"row"},W=B(T.FLEXLAYOUT__SPLITTER)+" "+B(T.FLEXLAYOUT__SPLITTER_+o.getOrientation().getName());if(o instanceof z?W+=" "+B(T.FLEXLAYOUT__SPLITTER_BORDER):void 0!==o.getModel().getMaximizedTabset(i.getWindowId())&&(C.display="none"),a?(C.width=A+"px",C.minWidth=A+"px"):(C.height=A+"px",C.minHeight=A+"px"),!l&&o.getModel().isSplitterEnableHandle()&&(e=(0,s.jsx)("div",{className:B(T.FLEXLAYOUT__SPLITTER_HANDLE)+" "+(a?B(T.FLEXLAYOUT__SPLITTER_HANDLE_HORZ):B(T.FLEXLAYOUT__SPLITTER_HANDLE_VERT))})),0===L)return(0,s.jsx)("div",{className:W,style:C,ref:u,"data-layout-path":o.getPath()+"/s"+(r-1),onPointerDown:v,children:e});{let t={};o.getOrientation()===h.HORZ?(t.height="100%",t.width=A+L+"px",t.cursor="ew-resize"):(t.height=A+L+"px",t.width="100%",t.cursor="ns-resize");let e=B(T.FLEXLAYOUT__SPLITTER_EXTRA);return(0,s.jsx)("div",{className:W,style:C,ref:u,"data-layout-path":o.getPath()+"/s"+(r-1),onPointerDown:v,children:(0,s.jsx)("div",{style:t,ref:g,className:e,onPointerDown:v})})}};function G(t){let{layout:e,border:i,show:o}=t,r=n.useRef(null),a=n.useRef(void 0);n.useLayoutEffect(()=>{let t=e.getBoundingClientRect(r.current);isNaN(t.x)||!(t.width>0)||i.getContentRect().equals(t)||(i.setContentRect(t),j?(a.current&&clearTimeout(a.current),a.current=setTimeout(()=>{e.redrawInternal("border content rect "+t),a.current=void 0},50)):e.redrawInternal("border content rect "+t))});let l=!0,d={};i.getOrientation()===h.HORZ?(d.width=i.getSize(),d.minWidth=i.getMinSize(),d.maxWidth=i.getMaxSize()):(d.height=i.getSize(),d.minHeight=i.getMinSize(),d.maxHeight=i.getMaxSize(),l=!1),d.display=o?"flex":"none";let c=e.getClassName(T.FLEXLAYOUT__BORDER_TAB_CONTENTS);return i.getLocation()===u.LEFT||i.getLocation()===u.TOP?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{ref:r,style:d,className:c}),o&&(0,s.jsx)(k,{layout:e,node:i,index:0,horizontal:l})]}):(0,s.jsxs)(s.Fragment,{children:[o&&(0,s.jsx)(k,{layout:e,node:i,index:0,horizontal:l}),(0,s.jsx)("div",{ref:r,style:d,className:c})]})}var V=(t=>(t[t.Visible=1]="Visible",t[t.Always=2]="Always",t[t.Selected=3]="Selected",t))(V||{});let J=t=>{let{layout:e,node:i,selected:o,border:r,icons:a,path:l}=t,d=n.useRef(null),h=n.useRef(null),c=()=>{let t=i.getCloseType();return!!o||t===V.Always||t===V.Visible&&!!window.matchMedia&&!!window.matchMedia("(hover: hover) and (pointer: fine)").matches};n.useLayoutEffect(()=>{i.setTabRect(e.getBoundingClientRect(d.current)),e.getEditingTab()===i&&h.current.select()});let u=e.getClassName,_=u(T.FLEXLAYOUT__BORDER_BUTTON)+" "+u(T.FLEXLAYOUT__BORDER_BUTTON_+r);o?_+=" "+u(T.FLEXLAYOUT__BORDER_BUTTON__SELECTED):_+=" "+u(T.FLEXLAYOUT__BORDER_BUTTON__UNSELECTED),void 0!==i.getClassName()&&(_+=" "+i.getClassName());let b=0;!1===i.getModel().isEnableRotateBorderIcons()&&("left"===r?b=90:"right"===r&&(b=-90));let E=L(e,i,b),m=E.content?(0,s.jsx)("div",{className:u(T.FLEXLAYOUT__BORDER_BUTTON_CONTENT),children:E.content}):null,f=E.leading?(0,s.jsx)("div",{className:u(T.FLEXLAYOUT__BORDER_BUTTON_LEADING),children:E.leading}):null;if(e.getEditingTab()===i&&(m=(0,s.jsx)("input",{ref:h,className:u(T.FLEXLAYOUT__TAB_BUTTON_TEXTBOX),"data-layout-path":l+"/textbox",type:"text",autoFocus:!0,defaultValue:i.getName(),onKeyDown:t=>{"Escape"===t.code?e.setEditingTab(void 0):("Enter"===t.code||"NumpadEnter"===t.code)&&(e.setEditingTab(void 0),e.doAction(p.renameTab(i.getId(),t.target.value)))},onPointerDown:t=>{t.stopPropagation()}})),i.isEnableClose()){let t=e.i18nName(g.Close_Tab);E.buttons.push((0,s.jsx)("div",{"data-layout-path":l+"/button/close",title:t,className:u(T.FLEXLAYOUT__BORDER_BUTTON_TRAILING),onPointerDown:t=>{t.stopPropagation()},onClick:t=>{c()&&(e.doAction(p.deleteTab(i.getId())),t.stopPropagation())},children:"function"==typeof a.close?a.close(i):a.close},"close"))}return(0,s.jsxs)("div",{ref:d,"data-layout-path":l,className:_,onClick:()=>{e.doAction(p.selectTab(i.getId()))},onAuxClick:t=>{w(t)&&e.auxMouseClick(i,t)},onContextMenu:t=>{e.showContextMenu(i,t)},title:i.getHelpText(),draggable:!0,onDragStart:t=>{i.isEnableDrag()?(t.stopPropagation(),e.setDragNode(t.nativeEvent,i)):t.preventDefault()},onDragEnd:t=>{t.stopPropagation(),e.clearDragMain()},children:[f,m,E.buttons]})},Z=t=>{let{layout:e,node:i}=t,n=e.getClassName,o=n(T.FLEXLAYOUT__TAB_BUTTON_STAMP),r=L(e,i),a=r.content?(0,s.jsx)("div",{className:n(T.FLEXLAYOUT__TAB_BUTTON_CONTENT),children:r.content}):i.getNameForOverflowMenu(),l=r.leading?(0,s.jsx)("div",{className:n(T.FLEXLAYOUT__TAB_BUTTON_LEADING),children:r.leading}):null;return(0,s.jsxs)("div",{className:o,title:i.getHelpText(),children:[l,a]})};function q(t,e,i,n,o){let r=o.getRootDiv(),a=o.getClassName,l=t.ownerDocument,d=t.getBoundingClientRect(),h=(null==r?void 0:r.getBoundingClientRect())??new DOMRect(0,0,100,100),c=l.createElement("div");c.className=a(T.FLEXLAYOUT__POPUP_MENU_CONTAINER),d.left<h.left+h.width/2?c.style.left=d.left-h.left+"px":c.style.right=h.right-d.right+"px",d.top<h.top+h.height/2?c.style.top=d.top-h.top+"px":c.style.bottom=h.bottom-d.bottom+"px",o.showOverlay(!0),r&&r.appendChild(c);let u=()=>{o.hideControlInPortal(),o.showOverlay(!1),r&&r.removeChild(c),c.removeEventListener("pointerdown",g),l.removeEventListener("pointerdown",_)},g=t=>{t.stopPropagation()},_=t=>{u()};c.addEventListener("pointerdown",g),l.addEventListener("pointerdown",_),o.showControlInPortal((0,s.jsx)($,{currentDocument:l,parentNode:e,onSelect:n,onHide:u,items:i,classNameMapper:a,layout:o}),c)}let $=t=>{let{parentNode:e,items:i,onHide:o,onSelect:r,classNameMapper:a,layout:l}=t,d=(0,n.useRef)(null);(0,n.useEffect)(()=>{d.current&&d.current.focus()},[]);let h=(t,e)=>{r(t),o(),e.stopPropagation()},c=(t,e)=>{t.stopPropagation(),l.setDragNode(t.nativeEvent,e),setTimeout(()=>{o()},0)},u=t=>{l.clearDragMain()},g=i.map((t,i)=>{let n=a(T.FLEXLAYOUT__POPUP_MENU_ITEM);return e.getSelected()===t.index&&(n+=" "+a(T.FLEXLAYOUT__POPUP_MENU_ITEM__SELECTED)),(0,s.jsx)("div",{className:n,"data-layout-path":"/popup-menu/tb"+i,onClick:e=>h(t,e),draggable:!0,onDragStart:e=>c(e,t.node),onDragEnd:u,title:t.node.getHelpText(),children:(0,s.jsx)(Z,{node:t.node,layout:l})},t.index)});return(0,s.jsx)("div",{className:a(T.FLEXLAYOUT__POPUP_MENU),ref:d,tabIndex:0,onKeyDown:t=>{"Escape"===t.key&&o()},"data-layout-path":"/popup-menu",children:g})},K=(t,e,i,s,o,r)=>{let[a,l]=n.useState([]),[d,c]=n.useState(!1),[u,g]=n.useState(!1),T=n.useRef(null),_=n.useRef(!1),p=n.useRef(void 0),b=n.useRef([]),E=n.useRef(0),m=n.useRef(!1);b.current=a,n.useLayoutEffect(()=>{s.current&&M(0)},[e.getId()]),n.useLayoutEffect(()=>{_.current=!1},[e.getSelectedNode(),e.getRect().width,e.getRect().height]),n.useLayoutEffect(()=>{w(),!1===_.current&&function(){let i=e.getSelectedNode();if(i&&s.current){let e=t.getBoundingClientRect(s.current),n=i.getTabRect(),o=v(e)-v(n);o>0||S(n)>S(e)?(M(B(s.current)-o),m.current=!0):(o=y(n)-y(e))>0&&(M(B(s.current)+o),m.current=!0)}}(),f(),O()});let f=()=>{if(s.current&&o.current){let t=s.current,e=o.current,n=D(t),r=I(t),a=B(t);if(r>n&&r>0){let t=n*n/r,s=0;t<20&&(s=20-t,t=20);let o=a*(n-s)/r;i===h.HORZ?(e.style.width=t+"px",e.style.left=o+"px"):(e.style.height=t+"px",e.style.top=o+"px"),e.style.display="block"}else e.style.display="none";i===h.HORZ?e.style.bottom="0px":e.style.right="0px"}},O=()=>{let t=N().length>0;t!==d&&c(t),void 0===p.current&&(p.current=setTimeout(()=>{let t=N();!function(t,e){return t.length===e.length&&t.every((t,i)=>t===e[i])}(t,b.current)&&l(t),p.current=void 0},100))},A=(t,e)=>{if(s.current&&o.current){let n=s.current,r=o.current,a=D(n),l=I(n),d=D(r),c=n.getBoundingClientRect(),u=0;u=Math.max(0,Math.min(l-d,u=i===h.HORZ?t-c.x-E.current:e-c.y-E.current)),a>0&&M(u*l/a)}},R=()=>{},L=()=>{},w=()=>{if(s.current){let t=D(s.current.firstElementChild)+10*!!u>D(s.current);t!==u&&g(t)}},N=()=>{let t=[];if(s.current){let e=s.current,i=e.getBoundingClientRect(),n=v(i)-1,o=y(i)+1,a=e.firstElementChild,l=0;Array.from(a.children).forEach(e=>{let i=e.getBoundingClientRect();e.classList.contains(r)&&((v(i)<n||y(i)>o)&&t.push(l),l++)})}return t},v=t=>i===h.HORZ?t.x:t.y,y=t=>i===h.HORZ?t.right:t.bottom,D=t=>i===h.HORZ?t.clientWidth:t.clientHeight,S=t=>i===h.HORZ?t.width:t.height,I=t=>i===h.HORZ?t.scrollWidth:t.scrollHeight,M=t=>{i===h.HORZ?s.current.scrollLeft=t:s.current.scrollTop=t},B=t=>i===h.HORZ?t.scrollLeft:t.scrollTop;return{selfRef:T,userControlledPositionRef:_,onScroll:()=>{m.current||(_.current=!0),m.current=!1,f(),O()},onScrollPointerDown:t=>{var e;t.stopPropagation(),o.current.setPointerCapture(t.pointerId);let s=null==(e=o.current)?void 0:e.getBoundingClientRect();i===h.HORZ?E.current=t.clientX-s.x:E.current=t.clientY-s.y,x(t.currentTarget.ownerDocument,t,A,R,L)},hiddenTabs:a,onMouseWheel:t=>{if(s.current){if(0===e.getChildren().length)return;let i=0;if(Math.abs(t.deltaY)>0){i=-t.deltaY,1===t.deltaMode&&(i*=40);let e=B(s.current)-i;M(Math.max(0,Math.min(I(s.current)-D(s.current),e))),t.stopPropagation()}}},isDockStickyButtons:u,isShowHiddenTabs:d}},Q=t=>{let e,i,o,{border:r,layout:a,size:l}=t,d=n.useRef(null),c=n.useRef(null),_=n.useRef(null),b=n.useRef(null),E=n.useRef(null),m=a.getIcons();n.useLayoutEffect(()=>{r.setTabHeaderRect(a.getBoundingClientRect(f.current))});let{selfRef:f,userControlledPositionRef:O,onScroll:A,onScrollPointerDown:R,hiddenTabs:L,onMouseWheel:N,isDockStickyButtons:v,isShowHiddenTabs:x}=K(a,r,h.flip(r.getOrientation()),E,c,a.getClassName(T.FLEXLAYOUT__BORDER_BUTTON)),y=t=>{w(t)&&a.auxMouseClick(r,t)},D=t=>{t.stopPropagation()},S=t=>{a.doAction(p.selectTab(t.node.getId())),O.current=!1},I=a.getClassName,M=[],B=t=>{let e=r.getSelected()===t,i=r.getChildren()[t];M.push((0,s.jsx)(J,{layout:a,border:r.getLocation().getName(),node:i,path:r.getPath()+"/tb"+t,selected:e,icons:m},i.getId())),t<r.getChildren().length-1&&M.push((0,s.jsx)("div",{className:I(T.FLEXLAYOUT__BORDER_TAB_DIVIDER)},"divider"+t))};for(let t=0;t<r.getChildren().length;t++)B(t);let C=I(T.FLEXLAYOUT__BORDER)+" "+I(T.FLEXLAYOUT__BORDER_+r.getLocation().getName());void 0!==r.getClassName()&&(C+=" "+r.getClassName());let U=[],W=[],F={leading:e,buttons:U,stickyButtons:W,overflowPosition:void 0};if(a.customizeTabSet(r,F),e=F.leading,W=F.stickyButtons,U=F.buttons,void 0===F.overflowPosition&&(F.overflowPosition=W.length),W.length>0&&(v?U=[...W,...U]:M.push((0,s.jsx)("div",{ref:b,onPointerDown:D,onDragStart:t=>{t.preventDefault()},className:I(T.FLEXLAYOUT__TAB_TOOLBAR_STICKY_BUTTONS_CONTAINER),children:W},"sticky_buttons_container"))),x){let t,e=a.i18nName(g.Overflow_Menu_Tooltip);if("function"==typeof m.more){let e=L.map(t=>({index:t,node:r.getChildren()[t]}));t=m.more(r,e)}else t=(0,s.jsxs)(s.Fragment,{children:[m.more,(0,s.jsx)("div",{className:I(T.FLEXLAYOUT__TAB_BUTTON_OVERFLOW_COUNT),children:L.length>0?L.length:""})]});U.splice(Math.min(F.overflowPosition,U.length),0,(0,s.jsx)("button",{ref:_,className:I(T.FLEXLAYOUT__BORDER_TOOLBAR_BUTTON)+" "+I(T.FLEXLAYOUT__BORDER_TOOLBAR_BUTTON_OVERFLOW)+" "+I(T.FLEXLAYOUT__BORDER_TOOLBAR_BUTTON_OVERFLOW_+r.getLocation().getName()),title:e,onClick:t=>{let e=a.getShowOverflowMenu(),i=L.map(t=>({index:t,node:r.getChildren()[t]}));void 0!==e?e(r,t,i,S):q(_.current,r,i,S,a),t.stopPropagation()},onPointerDown:D,children:t},"overflowbutton"))}let Y=r.getSelected();if(-1!==Y){let t=r.getChildren()[Y];if(void 0!==t&&a.isSupportsPopout()&&t.isEnablePopout()){let e=a.i18nName(g.Popout_Tab);U.push((0,s.jsx)("button",{title:e,className:I(T.FLEXLAYOUT__BORDER_TOOLBAR_BUTTON)+" "+I(T.FLEXLAYOUT__BORDER_TOOLBAR_BUTTON_FLOAT),onClick:t=>{let e=r.getChildren()[r.getSelected()];void 0!==e&&a.doAction(p.popoutTab(e.getId())),t.stopPropagation()},onPointerDown:D,children:"function"==typeof m.popout?m.popout(t):m.popout},"popout"))}}let P=(0,s.jsx)("div",{ref:d,className:I(T.FLEXLAYOUT__BORDER_TOOLBAR)+" "+I(T.FLEXLAYOUT__BORDER_TOOLBAR_+r.getLocation().getName()),children:U},"toolbar"),H={},X={},z=l-1;return r.getLocation()===u.LEFT?(H={right:"100%",top:0},X={width:z,overflowY:"auto"}):r.getLocation()===u.RIGHT?(H={left:"100%",top:0},X={width:z,overflowY:"auto"}):(H={left:0},X={height:z,overflowX:"auto"}),r.isEnableTabScrollbar()&&(i=(0,s.jsx)("div",{ref:c,className:I(T.FLEXLAYOUT__MINI_SCROLLBAR),onPointerDown:R})),e&&(o=(0,s.jsx)("div",{className:I(T.FLEXLAYOUT__BORDER_LEADING),children:e})),(0,s.jsxs)("div",{ref:f,style:{display:"flex",flexDirection:r.getOrientation()===h.VERT?"row":"column"},className:C,"data-layout-path":r.getPath(),onClick:y,onAuxClick:y,onContextMenu:t=>{a.showContextMenu(r,t)},onWheel:N,children:[o,(0,s.jsxs)("div",{className:I(T.FLEXLAYOUT__MINI_SCROLLBAR_CONTAINER),children:[(0,s.jsx)("div",{ref:E,className:I(T.FLEXLAYOUT__BORDER_INNER)+" "+I(T.FLEXLAYOUT__BORDER_INNER_+r.getLocation().getName()),style:X,onScroll:A,children:(0,s.jsx)("div",{style:H,className:I(T.FLEXLAYOUT__BORDER_INNER_TAB_CONTAINER)+" "+I(T.FLEXLAYOUT__BORDER_INNER_TAB_CONTAINER_+r.getLocation().getName()),children:M})}),i]}),P]})},tt=t=>{let{layout:e,node:i}=t,o=n.useRef(null);n.useEffect(()=>{i.setTabStamp(o.current)},[i,o.current]);let r=(0,e.getClassName)(T.FLEXLAYOUT__DRAG_RECT);return(0,s.jsx)("div",{ref:o,className:r,children:(0,s.jsx)(Z,{layout:e,node:i},i.getId())})},te=t=>{let{title:e,layout:i,layoutWindow:s,url:r,onCloseWindow:a,onSetWindow:l,children:d}=t,h=n.useRef(null),[c,u]=n.useState(void 0),g=new Map;return(n.useLayoutEffect(()=>{if(!h.current){let t=s.windowId,i=s.rect;h.current=window.open(r,t,`left=${i.x},top=${i.y},width=${i.width},height=${i.height}`),h.current?(s.window=h.current,l(s,h.current),window.addEventListener("beforeunload",()=>{if(h.current){let t=h.current;h.current=null,t.close()}}),h.current.addEventListener("load",()=>{if(h.current){h.current.focus(),h.current.resizeTo(i.width,i.height),h.current.moveTo(i.x,i.y);let t=h.current.document;t.title=e;let n=t.createElement("div");n.className=T.FLEXLAYOUT__FLOATING_WINDOW_CONTENT,t.body.appendChild(n),(function(t,e){let i=[];for(let s of document.querySelectorAll('style, link[rel="stylesheet"]'))ti(t,s,e,i);return Promise.all(i)})(t,g).then(()=>{u(n)});let o=new MutationObserver(e=>(function(t,e,i){for(let s of t)if("childList"===s.type){for(let t of s.addedNodes)(t instanceof HTMLLinkElement||t instanceof HTMLStyleElement)&&ti(e,t,i);for(let t of s.removedNodes)if(t instanceof HTMLLinkElement||t instanceof HTMLStyleElement){let s=i.get(t);s&&e.head.removeChild(s)}}})(e,t,g));o.observe(document.head,{childList:!0}),h.current.addEventListener("beforeunload",()=>{h.current&&(a(s),h.current=null,o.disconnect())})}})):(console.warn(`Unable to open window ${r}`),a(s))}return()=>{var t;i.getModel().getwindowsMap().has(s.windowId)||(null==(t=h.current)||t.close(),h.current=null)}},[]),void 0!==c)?(0,o.createPortal)(d,c):null};function ti(t,e,i,s){if(e instanceof HTMLLinkElement){let n=e.cloneNode(!0);t.head.appendChild(n),i.set(e,n),s&&s.push(new Promise(t=>{n.onload=()=>t(!0)}))}else if(e instanceof HTMLStyleElement)try{let s=e.cloneNode(!0);t.head.appendChild(s),i.set(e,s)}catch(t){}}let ts={width:"1em",height:"1em",display:"flex",alignItems:"center"},tn=()=>(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",style:ts,viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{fill:"none",d:"M0 0h24v24H0z"}),(0,s.jsx)("path",{stroke:"var(--color-icon)",fill:"var(--color-icon)",d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})]}),to=t=>{let{layout:e,show:i}=t;return(0,s.jsx)("div",{className:e.getClassName(T.FLEXLAYOUT__LAYOUT_OVERLAY),style:{display:i?"flex":"none"}})},tr=t=>{let{layout:e,node:i,selected:o,path:r}=t,a=n.useRef(null),l=n.useRef(null),d=e.getIcons();n.useLayoutEffect(()=>{i.setTabRect(e.getBoundingClientRect(a.current)),e.getEditingTab()===i&&l.current.select()});let h=()=>{e.setEditingTab(i),e.getCurrentDocument().body.addEventListener("pointerdown",c)},c=t=>{t.target!==l.current&&(e.getCurrentDocument().body.removeEventListener("pointerdown",c),e.setEditingTab(void 0))},u=()=>{let t=i.getCloseType();return!!o||t===V.Always||t===V.Visible&&!!window.matchMedia&&!!window.matchMedia("(hover: hover) and (pointer: fine)").matches},_=e.getClassName,b=i.getParent(),E=b.isEnableSingleTabStretch()&&1===b.getChildren().length,m=E?T.FLEXLAYOUT__TAB_BUTTON_STRETCH:T.FLEXLAYOUT__TAB_BUTTON,f=_(m);f+=" "+_(m+"_"+b.getTabLocation()),E||(o?f+=" "+_(m+"--selected"):f+=" "+_(m+"--unselected")),void 0!==i.getClassName()&&(f+=" "+i.getClassName());let O=L(e,i),A=O.content?(0,s.jsx)("div",{className:_(T.FLEXLAYOUT__TAB_BUTTON_CONTENT),children:O.content}):null,R=O.leading?(0,s.jsx)("div",{className:_(T.FLEXLAYOUT__TAB_BUTTON_LEADING),children:O.leading}):null;if(e.getEditingTab()===i&&(A=(0,s.jsx)("input",{ref:l,className:_(T.FLEXLAYOUT__TAB_BUTTON_TEXTBOX),"data-layout-path":r+"/textbox",type:"text",autoFocus:!0,defaultValue:i.getName(),onKeyDown:t=>{"Escape"===t.code?e.setEditingTab(void 0):("Enter"===t.code||"NumpadEnter"===t.code)&&(e.setEditingTab(void 0),e.doAction(p.renameTab(i.getId(),t.target.value)))},onPointerDown:t=>{t.stopPropagation()}})),i.isEnableClose()&&!E){let t=e.i18nName(g.Close_Tab);O.buttons.push((0,s.jsx)("div",{"data-layout-path":r+"/button/close",title:t,className:_(T.FLEXLAYOUT__TAB_BUTTON_TRAILING),onPointerDown:t=>{t.stopPropagation()},onClick:t=>{u()&&(e.doAction(p.deleteTab(i.getId())),t.stopPropagation())},children:"function"==typeof d.close?d.close(i):d.close},"close"))}return(0,s.jsxs)("div",{ref:a,"data-layout-path":r,className:f,onClick:()=>{e.doAction(p.selectTab(i.getId()))},onAuxClick:t=>{w(t)&&e.auxMouseClick(i,t)},onContextMenu:t=>{e.showContextMenu(i,t)},title:i.getHelpText(),draggable:!0,onDragStart:t=>{i.isEnableDrag()?(t.stopPropagation(),e.setDragNode(t.nativeEvent,i)):t.preventDefault()},onDragEnd:t=>{e.clearDragMain()},onDoubleClick:t=>{i.isEnableRename()&&(h(),t.stopPropagation())},children:[R,A,O.buttons]})},ta=t=>{let e,i,r,a,{node:l,layout:d}=t,c=n.useRef(null),u=n.useRef(null),_=n.useRef(null),b=n.useRef(null),E=n.useRef(null),m=n.useRef(null),f=n.useRef(null),O=n.useRef(void 0),A=d.getIcons();n.useLayoutEffect(()=>{l.setRect(d.getBoundingClientRect(R.current)),c.current&&l.setTabStripRect(d.getBoundingClientRect(c.current));let t=d.getBoundingClientRect(b.current);l.getContentRect().equals(t)||isNaN(t.x)||(l.setContentRect(t),j?(O.current&&clearTimeout(O.current),O.current=setTimeout(()=>{d.redrawInternal("border content rect "+t),O.current=void 0},50)):d.redrawInternal("border content rect "+t))});let{selfRef:R,userControlledPositionRef:L,onScroll:N,onScrollPointerDown:v,hiddenTabs:x,onMouseWheel:y,isDockStickyButtons:D,isShowHiddenTabs:S}=K(d,l,h.HORZ,_,u,d.getClassName(T.FLEXLAYOUT__TAB_BUTTON)),I=t=>{d.doAction(p.selectTab(t.node.getId())),L.current=!1},M=t=>{d.getEditingTab()?t.preventDefault():l.isEnableDrag()?(t.stopPropagation(),d.setDragNode(t.nativeEvent,l)):t.preventDefault()},B=t=>{w(t)||d.doAction(p.setActiveTabset(l.getId(),d.getWindowId()))},C=t=>{w(t)&&d.auxMouseClick(l,t)},U=t=>{d.showContextMenu(l,t)},W=t=>{t.stopPropagation()},F=t=>{l.canMaximize()&&d.maximize(l)},Y=d.getClassName,P=l.getSelectedNode(),H=l.getPath(),X=[];if(l.isEnableTabStrip())for(let t=0;t<l.getChildren().length;t++){let e=l.getChildren()[t],i=l.getSelected()===t;X.push((0,s.jsx)(tr,{layout:d,node:e,path:H+"/tb"+t,selected:i},e.getId())),t<l.getChildren().length-1&&X.push((0,s.jsx)("div",{className:Y(T.FLEXLAYOUT__TABSET_TAB_DIVIDER)},"divider"+t))}let z=[],k=[],G={leading:r,stickyButtons:z,buttons:k,overflowPosition:void 0};d.customizeTabSet(l,G),r=G.leading,z=G.stickyButtons,k=G.buttons;let V=l.isEnableSingleTabStretch()&&1===l.getChildren().length,J=V&&l.getChildren()[0].isEnableClose()||l.isEnableClose();if(void 0===G.overflowPosition&&(G.overflowPosition=z.length),z.length>0&&(!l.isEnableTabWrap()&&(D||V)?k=[...z,...k]:X.push((0,s.jsx)("div",{ref:f,onPointerDown:W,onDragStart:t=>{t.preventDefault()},className:Y(T.FLEXLAYOUT__TAB_TOOLBAR_STICKY_BUTTONS_CONTAINER),children:z},"sticky_buttons_container"))),!l.isEnableTabWrap()&&S){let t,e=d.i18nName(g.Overflow_Menu_Tooltip);if("function"==typeof A.more){let e=x.map(t=>({index:t,node:l.getChildren()[t]}));t=A.more(l,e)}else t=(0,s.jsxs)(s.Fragment,{children:[A.more,(0,s.jsx)("div",{className:Y(T.FLEXLAYOUT__TAB_BUTTON_OVERFLOW_COUNT),children:x.length>0?x.length:""})]});k.splice(Math.min(G.overflowPosition,k.length),0,(0,s.jsx)("button",{"data-layout-path":H+"/button/overflow",ref:m,className:Y(T.FLEXLAYOUT__TAB_TOOLBAR_BUTTON)+" "+Y(T.FLEXLAYOUT__TAB_BUTTON_OVERFLOW),title:e,onClick:t=>{let e=d.getShowOverflowMenu(),i=x.map(t=>({index:t,node:l.getChildren()[t]}));void 0!==e?e(l,t,i,I):q(m.current,l,i,I,d),t.stopPropagation()},onPointerDown:W,children:t},"overflowbutton"))}if(void 0!==P&&d.isSupportsPopout()&&P.isEnablePopout()&&P.isEnablePopoutIcon()){let t=d.i18nName(g.Popout_Tab);k.push((0,s.jsx)("button",{"data-layout-path":H+"/button/popout",title:t,className:Y(T.FLEXLAYOUT__TAB_TOOLBAR_BUTTON)+" "+Y(T.FLEXLAYOUT__TAB_TOOLBAR_BUTTON_FLOAT),onClick:t=>{void 0!==P&&d.doAction(p.popoutTab(P.getId())),t.stopPropagation()},onPointerDown:W,children:"function"==typeof A.popout?A.popout(P):A.popout},"popout"))}if(l.canMaximize()){let t=d.i18nName(g.Restore),e=d.i18nName(g.Maximize);k.push((0,s.jsx)("button",{"data-layout-path":H+"/button/max",title:l.isMaximized()?t:e,className:Y(T.FLEXLAYOUT__TAB_TOOLBAR_BUTTON)+" "+Y(T.FLEXLAYOUT__TAB_TOOLBAR_BUTTON_+(l.isMaximized()?"max":"min")),onClick:t=>{l.canMaximize()&&d.maximize(l),t.stopPropagation()},onPointerDown:W,children:l.isMaximized()?"function"==typeof A.restore?A.restore(l):A.restore:"function"==typeof A.maximize?A.maximize(l):A.maximize},"max"))}if(!l.isMaximized()&&J){let t=V?d.i18nName(g.Close_Tab):d.i18nName(g.Close_Tabset);k.push((0,s.jsx)("button",{"data-layout-path":H+"/button/close",title:t,className:Y(T.FLEXLAYOUT__TAB_TOOLBAR_BUTTON)+" "+Y(T.FLEXLAYOUT__TAB_TOOLBAR_BUTTON_CLOSE),onClick:V?t=>{d.doAction(p.deleteTab(l.getChildren()[0].getId())),t.stopPropagation()}:t=>{d.doAction(p.deleteTabset(l.getId())),t.stopPropagation()},onPointerDown:W,children:"function"==typeof A.closeTabset?A.closeTabset(l):A.closeTabset},"close"))}if(l.isActive()&&l.isEnableActiveIcon()){let t=d.i18nName(g.Active_Tabset);k.push((0,s.jsx)("div",{"data-layout-path":H+"/button/active",title:t,className:Y(T.FLEXLAYOUT__TAB_TOOLBAR_ICON),children:"function"==typeof A.activeTabset?A.activeTabset(l):A.activeTabset},"active"))}let Z=(0,s.jsx)("div",{ref:E,className:Y(T.FLEXLAYOUT__TAB_TOOLBAR),onPointerDown:W,onDragStart:t=>{t.preventDefault()},children:k},"buttonbar"),$=Y(T.FLEXLAYOUT__TABSET_TABBAR_OUTER);if(void 0!==l.getClassNameTabStrip()&&($+=" "+l.getClassNameTabStrip()),$+=" "+T.FLEXLAYOUT__TABSET_TABBAR_OUTER_+l.getTabLocation(),l.isActive()&&($+=" "+Y(T.FLEXLAYOUT__TABSET_SELECTED)),l.isMaximized()&&($+=" "+Y(T.FLEXLAYOUT__TABSET_MAXIMIZED)),V){let t=l.getChildren()[0];void 0!==t.getTabSetClassName()&&($+=" "+t.getTabSetClassName())}if(r&&(a=(0,s.jsx)("div",{className:Y(T.FLEXLAYOUT__TABSET_LEADING),children:r})),l.isEnableTabWrap())l.isEnableTabStrip()&&(e=(0,s.jsxs)("div",{className:$,style:{flexWrap:"wrap",gap:"1px",marginTop:"2px"},ref:c,"data-layout-path":H+"/tabstrip",onPointerDown:B,onDoubleClick:F,onContextMenu:U,onClick:C,onAuxClick:C,draggable:!0,onDragStart:M,children:[a,X,(0,s.jsx)("div",{style:{flexGrow:1}}),Z]}));else if(l.isEnableTabStrip()){let t;l.isEnableTabScrollbar()&&(t=(0,s.jsx)("div",{ref:u,className:Y(T.FLEXLAYOUT__MINI_SCROLLBAR),onPointerDown:v})),e=(0,s.jsxs)("div",{className:$,ref:c,"data-layout-path":H+"/tabstrip",onPointerDown:B,onDoubleClick:F,onContextMenu:U,onClick:C,onAuxClick:C,draggable:!0,onWheel:y,onDragStart:M,children:[a,(0,s.jsxs)("div",{className:Y(T.FLEXLAYOUT__MINI_SCROLLBAR_CONTAINER),children:[(0,s.jsx)("div",{ref:_,className:Y(T.FLEXLAYOUT__TABSET_TABBAR_INNER)+" "+Y(T.FLEXLAYOUT__TABSET_TABBAR_INNER_+l.getTabLocation()),style:{overflowX:"auto",overflowY:"hidden"},onScroll:N,children:(0,s.jsx)("div",{style:{width:V?"100%":"none"},className:Y(T.FLEXLAYOUT__TABSET_TABBAR_INNER_TAB_CONTAINER)+" "+Y(T.FLEXLAYOUT__TABSET_TABBAR_INNER_TAB_CONTAINER_+l.getTabLocation()),children:X})}),t]}),Z]})}if(0===l.getChildren().length){let t=d.getTabSetPlaceHolderCallback();t&&(i=t(l))}let Q=(0,s.jsx)("div",{ref:b,className:Y(T.FLEXLAYOUT__TABSET_CONTENT),children:i});Q="top"===l.getTabLocation()?(0,s.jsxs)(s.Fragment,{children:[e,Q]}):(0,s.jsxs)(s.Fragment,{children:[Q,e]});let tt={flexGrow:Math.max(1,1e3*l.getWeight()),minWidth:l.getMinWidth(),minHeight:l.getMinHeight(),maxWidth:l.getMaxWidth(),maxHeight:l.getMaxHeight()};void 0===l.getModel().getMaximizedTabset(d.getWindowId())||l.isMaximized()||(tt.display="none");let te=(0,s.jsx)("div",{ref:R,className:Y(T.FLEXLAYOUT__TABSET_CONTAINER),style:tt,children:(0,s.jsx)("div",{className:Y(T.FLEXLAYOUT__TABSET),"data-layout-path":H,children:Q})});return l.isMaximized()&&d.getMainElement()?(0,o.createPortal)((0,s.jsx)("div",{style:{position:"absolute",display:"flex",top:0,left:0,bottom:0,right:0},children:te}),d.getMainElement()):te},tl=t=>{let{layout:e,node:i}=t,o=n.useRef(null),r=i.getOrientation()===h.HORZ;n.useLayoutEffect(()=>{i.setRect(e.getBoundingClientRect(o.current))});let a=[],l=0;for(let t of i.getChildren())l>0&&a.push((0,s.jsx)(k,{layout:e,node:i,index:l,horizontal:r},"splitter"+l)),t instanceof U?a.push((0,s.jsx)(tl,{layout:e,node:t},t.getId())):t instanceof B&&a.push((0,s.jsx)(ta,{layout:e,node:t},t.getId())),l++;let d={flexGrow:Math.max(1,1e3*i.getWeight()),minWidth:i.getMinWidth(),minHeight:i.getMinHeight(),maxWidth:i.getMaxWidth(),maxHeight:i.getMaxHeight()};return r?d.flexDirection="row":d.flexDirection="column",(0,s.jsx)("div",{ref:o,className:e.getClassName(T.FLEXLAYOUT__ROW),style:d,children:a})},td=t=>{let{layout:e,selected:i,node:o,path:r}=t,a=n.useRef(null),l=n.useRef(!0),d=o.getParent(),h=d.getContentRect();n.useLayoutEffect(()=>{let t=o.getMoveableElement();a.current.appendChild(t),o.setMoveableElement(t);let e=()=>{o.saveScrollPosition()};return t.addEventListener("scroll",e),a.current.addEventListener("pointerdown",c),()=>{t.removeEventListener("scroll",e),a.current&&a.current.removeEventListener("pointerdown",c),o.setVisible(!1)}},[]),n.useEffect(()=>{o.isSelected()&&l.current&&(o.restoreScrollPosition(),l.current=!1)});let c=()=>{let t=o.getParent();t instanceof B&&!t.isActive()&&e.doAction(p.setActiveTabset(t.getId(),e.getWindowId()))};o.setRect(h);let u=e.getClassName,g={};h.styleWithPosition(g);let _=null;if(i){if(o.setVisible(!0),document.hidden&&o.isEnablePopoutOverlay()){let t={};h.styleWithPosition(t),_=(0,s.jsx)("div",{style:t,className:u(T.FLEXLAYOUT__TAB_OVERLAY)})}}else g.display="none",o.setVisible(!1);d instanceof B&&void 0!==o.getModel().getMaximizedTabset(e.getWindowId())&&(d.isMaximized()?g.zIndex=10:g.display="none"),d instanceof z&&!d.isShowing()&&(g.display="none");let b=u(T.FLEXLAYOUT__TAB);return d instanceof z&&(b+=" "+u(T.FLEXLAYOUT__TAB_BORDER),b+=" "+u(T.FLEXLAYOUT__TAB_BORDER_+d.getLocation().getName())),void 0!==o.getContentClassName()&&(b+=" "+o.getContentClassName()),(0,s.jsxs)(s.Fragment,{children:[_,(0,s.jsx)("div",{ref:a,style:g,className:b,"data-layout-path":r})]})};class th extends n.Component{constructor(t){super(t),d(this,"retry",()=>{this.setState({hasError:!1})}),this.state={hasError:!1}}static getDerivedStateFromError(t){return{hasError:!0}}componentDidCatch(t,e){console.debug(t),console.debug(e)}render(){return this.state.hasError?(0,s.jsx)("div",{className:T.FLEXLAYOUT__ERROR_BOUNDARY_CONTAINER,children:(0,s.jsx)("div",{className:T.FLEXLAYOUT__ERROR_BOUNDARY_CONTENT,children:(0,s.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center"},children:[this.props.message,(0,s.jsx)("p",{children:(0,s.jsx)("button",{onClick:this.retry,children:this.props.retryText})})]})})}):this.props.children}}let tc=n.memo(({layout:t,node:e})=>(0,s.jsx)(th,{message:t.i18nName(g.Error_rendering_component),retryText:t.i18nName(g.Error_rendering_component_retry),children:t.props.factory(e)}),function(t,e){return!(e.visible&&(!t.rect.equalSize(e.rect)||t.forceRevision!==e.forceRevision||t.tabsRevision!==e.tabsRevision))});class tu extends n.Component{constructor(t){super(t),d(this,"selfRef"),d(this,"revision"),this.selfRef=n.createRef(),this.revision=0}redraw(){this.selfRef.current.redraw("parent "+this.revision)}addTabToTabSet(t,e){return this.selfRef.current.addTabToTabSet(t,e)}addTabWithDragAndDrop(t,e,i){this.selfRef.current.addTabWithDragAndDrop(t,e,i)}moveTabWithDragAndDrop(t,e){this.selfRef.current.moveTabWithDragAndDrop(t,e)}addTabToActiveTabSet(t){return this.selfRef.current.addTabToActiveTabSet(t)}setDragComponent(t,e,i,s){this.selfRef.current.setDragComponent(t,e,i,s)}getRootDiv(){return this.selfRef.current.getRootDiv()}render(){return(0,s.jsx)(tT,{ref:this.selfRef,...this.props,renderRevision:this.revision++})}}let tg=class t extends n.Component{constructor(e){super(e),d(this,"selfRef"),d(this,"moveablesRef"),d(this,"findBorderBarSizeRef"),d(this,"mainRef"),d(this,"previousModel"),d(this,"orderedTabIds"),d(this,"orderedTabMoveableIds"),d(this,"moveableElementMap",new Map),d(this,"dropInfo"),d(this,"outlineDiv"),d(this,"currentDocument"),d(this,"currentWindow"),d(this,"supportsPopout"),d(this,"popoutURL"),d(this,"icons"),d(this,"resizeObserver"),d(this,"dragEnterCount",0),d(this,"dragging",!1),d(this,"windowId"),d(this,"layoutWindow"),d(this,"mainLayout"),d(this,"isMainWindow"),d(this,"isDraggingOverWindow"),d(this,"styleObserver"),d(this,"popoutWindowName"),d(this,"onScroll",()=>{this.updateRect()}),d(this,"updateLayoutMetrics",()=>{if(this.findBorderBarSizeRef.current){let t=this.findBorderBarSizeRef.current.getBoundingClientRect().height;t!==this.state.calculatedBorderBarSize&&this.setState({calculatedBorderBarSize:t})}}),d(this,"onModelChange",t=>{this.redrawInternal("model change"),this.props.onModelChange&&this.props.onModelChange(this.props.model,t)}),d(this,"updateRect",()=>{if(this.selfRef.current){let t=c.fromDomRect(this.selfRef.current.getBoundingClientRect());t.equals(this.state.rect)||0===t.width||0===t.height||(this.setState({rect:t}),this.windowId!==H.MAIN_WINDOW_ID&&this.redrawInternal("rect updated"))}}),d(this,"getClassName",t=>void 0===this.props.classNameMapper?t:this.props.classNameMapper(t)),d(this,"onCloseWindow",t=>{this.doAction(p.closeWindow(t.windowId))}),d(this,"onSetWindow",(t,e)=>{}),d(this,"showControlInPortal",(t,e)=>{let i=(0,o.createPortal)(t,e);this.setState({portal:i})}),d(this,"hideControlInPortal",()=>{this.setState({portal:void 0})}),d(this,"getIcons",()=>this.icons),d(this,"setDragNode",(e,i)=>{if(t.dragState=new tm(this.mainLayout,"internal",i,void 0,void 0),e.dataTransfer.setData("text/plain","--flexlayout--"),e.dataTransfer.effectAllowed="copyMove",e.dataTransfer.dropEffect="move",this.dragEnterCount=0,i instanceof B){let t=!1,s=this.i18nName(g.Move_Tabset);if(i.getChildren().length>0&&(s=this.i18nName(g.Move_Tabs).replace("?",String(i.getChildren().length))),this.props.onRenderDragRect){let n=this.props.onRenderDragRect(s,i,void 0);n&&(this.setDragComponent(e,n,10,10),t=!0)}t||this.setDragComponent(e,s,10,10)}else{let t=e.target.getBoundingClientRect(),n=e.clientX-t.left,o=e.clientY-t.top,r=null==i?void 0:i.getParent(),a=r instanceof z&&r.getOrientation()===h.HORZ,l=a?10:n,d=a?10:o,c=!1;if(this.props.onRenderDragRect){let t=(0,s.jsx)(Z,{layout:this,node:i},i.getId()),n=this.props.onRenderDragRect(t,i,void 0);n&&(this.setDragComponent(e,n,l,d),c=!0)}c||(function(){let t=navigator.userAgent;return t.includes("Safari")&&!t.includes("Chrome")&&!t.includes("Chromium")}()?this.setDragComponent(e,(0,s.jsx)(Z,{node:i,layout:this}),l,d):e.dataTransfer.setDragImage(i.getTabStamp(),l,d))}}),d(this,"onDragEnterRaw",t=>{this.dragEnterCount++,1===this.dragEnterCount&&this.onDragEnter(t)}),d(this,"onDragLeaveRaw",t=>{this.dragEnterCount--,0===this.dragEnterCount&&this.onDragLeave(t)}),d(this,"onDragEnter",e=>{if(!t.dragState&&this.props.onExternalDrag){let i=this.props.onExternalDrag(e);if(i){let e=A.fromJson(i.json,this.props.model,!1);t.dragState=new tm(this.mainLayout,"external",e,i.json,i.onDrop)}}if(t.dragState){if(this.windowId!==H.MAIN_WINDOW_ID&&t.dragState.mainLayout===this.mainLayout&&t.dragState.mainLayout.setDraggingOverWindow(!0),t.dragState.mainLayout!==this.mainLayout)return;e.preventDefault(),this.dropInfo=void 0;let i=this.selfRef.current;this.outlineDiv=this.currentDocument.createElement("div"),this.outlineDiv.className=this.getClassName(T.FLEXLAYOUT__OUTLINE_RECT),this.outlineDiv.style.visibility="hidden";let s=this.props.model.getAttribute("tabDragSpeed");this.outlineDiv.style.transition=`top ${s}s, left ${s}s, width ${s}s, height ${s}s`,i.appendChild(this.outlineDiv),this.dragging=!0,this.showOverlay(!0),this.isDraggingOverWindow||void 0!==this.props.model.getMaximizedTabset(this.windowId)||this.setState({showEdges:this.props.model.isEnableEdgeDock()});let n=this.selfRef.current.getBoundingClientRect();new c(e.clientX-n.left,e.clientY-n.top,1,1).positionElement(this.outlineDiv)}}),d(this,"onDragOver",e=>{var i;if(this.dragging&&!this.isDraggingOverWindow){e.preventDefault();let s=null==(i=this.selfRef.current)?void 0:i.getBoundingClientRect(),n={x:e.clientX-((null==s?void 0:s.left)??0),y:e.clientY-((null==s?void 0:s.top)??0)};this.checkForBorderToShow(n.x,n.y);let o=this.props.model.findDropTargetNode(this.windowId,t.dragState.dragNode,n.x,n.y);o&&(this.dropInfo=o,this.outlineDiv&&(this.outlineDiv.className=this.getClassName(o.className),o.rect.positionElement(this.outlineDiv),this.outlineDiv.style.visibility="visible"))}}),d(this,"onDragLeave",e=>{this.dragging&&(this.windowId!==H.MAIN_WINDOW_ID&&t.dragState.mainLayout.setDraggingOverWindow(!1),this.clearDragLocal())}),d(this,"onDrop",e=>{if(this.dragging){e.preventDefault();let i=t.dragState;if(this.dropInfo)if(void 0!==i.dragJson){let t=this.doAction(p.addNode(i.dragJson,this.dropInfo.node.getId(),this.dropInfo.location,this.dropInfo.index));void 0!==i.fnNewNodeDropped&&i.fnNewNodeDropped(t,e)}else void 0!==i.dragNode&&this.doAction(p.moveNode(i.dragNode.getId(),this.dropInfo.node.getId(),this.dropInfo.location,this.dropInfo.index));this.mainLayout.clearDragMain()}this.dragEnterCount=0}),this.orderedTabIds=[],this.orderedTabMoveableIds=[],this.selfRef=n.createRef(),this.moveablesRef=n.createRef(),this.mainRef=n.createRef(),this.findBorderBarSizeRef=n.createRef(),this.supportsPopout=void 0!==e.supportsPopout?e.supportsPopout:tp,this.popoutURL=e.popoutURL?e.popoutURL:"popout.html",this.icons={...t_,...e.icons},this.windowId=e.windowId?e.windowId:H.MAIN_WINDOW_ID,this.mainLayout=this.props.mainLayout?this.props.mainLayout:this,this.isDraggingOverWindow=!1,this.layoutWindow=this.props.model.getwindowsMap().get(this.windowId),this.layoutWindow.layout=this,this.popoutWindowName=this.props.popoutWindowName||"Popout Window",this.state={rect:c.empty(),editingTab:void 0,showEdges:!1,showOverlay:!1,calculatedBorderBarSize:29,layoutRevision:0,forceRevision:0,showHiddenBorder:u.CENTER},this.isMainWindow=this.windowId===H.MAIN_WINDOW_ID}componentDidMount(){if(this.updateRect(),this.currentDocument=this.selfRef.current.ownerDocument,this.currentWindow=this.currentDocument.defaultView,this.layoutWindow.window=this.currentWindow,this.layoutWindow.toScreenRectFunction=t=>this.getScreenRect(t),this.resizeObserver=new ResizeObserver(t=>{requestAnimationFrame(()=>{this.updateRect()})}),this.selfRef.current&&this.resizeObserver.observe(this.selfRef.current),this.currentWindow.addEventListener("scroll",this.onScroll),this.isMainWindow)this.props.model.addChangeListener(this.onModelChange),this.updateLayoutMetrics();else{this.currentWindow.addEventListener("resize",()=>{this.updateRect()});let t=this.props.mainLayout.getRootDiv(),e=this.selfRef.current;D(t,e),this.styleObserver=new MutationObserver(()=>{D(t,e)&&this.redraw("mutation observer")}),this.styleObserver.observe(t,{attributeFilter:["style"]})}document.addEventListener("visibilitychange",()=>{for(let[t,e]of this.props.model.getwindowsMap())e.layout&&this.redraw("visibility change")})}componentDidUpdate(){this.currentDocument=this.selfRef.current.ownerDocument,this.currentWindow=this.currentDocument.defaultView,this.isMainWindow&&(this.props.model!==this.previousModel&&(void 0!==this.previousModel&&this.previousModel.removeChangeListener(this.onModelChange),this.props.model.getwindowsMap().get(this.windowId).layout=this,this.props.model.addChangeListener(this.onModelChange),this.layoutWindow=this.props.model.getwindowsMap().get(this.windowId),this.layoutWindow.layout=this,this.layoutWindow.toScreenRectFunction=t=>this.getScreenRect(t),this.previousModel=this.props.model,this.tidyMoveablesMap()),this.updateLayoutMetrics())}componentWillUnmount(){var t,e,i;this.selfRef.current&&(null==(t=this.resizeObserver)||t.unobserve(this.selfRef.current)),null==(e=this.currentWindow)||e.removeEventListener("scroll",this.onScroll),this.isMainWindow&&this.props.model.removeChangeListener(this.onModelChange),null==(i=this.styleObserver)||i.disconnect()}render(){if(!this.selfRef.current)return(0,s.jsxs)("div",{ref:this.selfRef,className:this.getClassName(T.FLEXLAYOUT__LAYOUT),children:[(0,s.jsx)("div",{ref:this.moveablesRef,className:this.getClassName(T.FLEXLAYOUT__LAYOUT_MOVEABLES)},"__moveables__"),this.renderMetricsElements()]});let t=this.props.model;t.getRoot(this.windowId).calcMinMaxSize(),t.getRoot(this.windowId).setPaths(""),t.getBorderSet().setPaths();let e=this.renderLayout(),i=this.renderBorders(e),n=this.renderTabs(),o=this.reorderComponents(n,this.orderedTabIds),r=null,a=null,l=null,d=null;if(this.isMainWindow){r=this.renderWindows(),d=this.renderMetricsElements();let t=this.renderTabMoveables();a=this.reorderComponents(t,this.orderedTabMoveableIds),l=(0,s.jsx)("div",{className:this.getClassName(T.FLEXLAYOUT__LAYOUT_TAB_STAMPS),children:this.renderTabStamps()},"__tabStamps__")}return(0,s.jsxs)("div",{ref:this.selfRef,className:this.getClassName(T.FLEXLAYOUT__LAYOUT),onDragEnter:this.onDragEnterRaw,onDragLeave:this.onDragLeaveRaw,onDragOver:this.onDragOver,onDrop:this.onDrop,children:[(0,s.jsx)("div",{ref:this.moveablesRef,className:this.getClassName(T.FLEXLAYOUT__LAYOUT_MOVEABLES)},"__moveables__"),d,(0,s.jsx)(to,{layout:this,show:this.state.showOverlay},"__overlay__"),i,o,a,l,this.state.portal,r]})}renderBorders(t){let e=this.getClassName(T.FLEXLAYOUT__LAYOUT_MAIN),i=this.props.model.getBorderSet().getBorderMap();if(!this.isMainWindow||!(i.size>0))return(0,s.jsx)("div",{className:e,ref:this.mainRef,style:{position:"absolute",top:0,left:0,bottom:0,right:0,display:"flex"},children:t});{t=(0,s.jsx)("div",{className:e,ref:this.mainRef,children:t});let n=new Map,o=new Map;for(let[t,e]of u.values){let t=i.get(e);t&&t.isShowing()&&(!t.isAutoHide()||t.isAutoHide()&&(t.getChildren().length>0||this.state.showHiddenBorder===e))&&(n.set(e,(0,s.jsx)(Q,{layout:this,border:t,size:this.state.calculatedBorderBarSize})),o.set(e,(0,s.jsx)(G,{layout:this,border:t,show:-1!==t.getSelected()})))}let r=this.getClassName(T.FLEXLAYOUT__LAYOUT_BORDER_CONTAINER),a=this.getClassName(T.FLEXLAYOUT__LAYOUT_BORDER_CONTAINER_INNER);if(this.props.model.getBorderSet().getLayoutHorizontal()){let e=(0,s.jsxs)("div",{className:a,style:{flexDirection:"column"},children:[o.get(u.TOP),(0,s.jsxs)("div",{className:a,style:{flexDirection:"row"},children:[o.get(u.LEFT),t,o.get(u.RIGHT)]}),o.get(u.BOTTOM)]});return(0,s.jsxs)("div",{className:r,style:{flexDirection:"column"},children:[n.get(u.TOP),(0,s.jsxs)("div",{className:a,style:{flexDirection:"row"},children:[n.get(u.LEFT),e,n.get(u.RIGHT)]}),n.get(u.BOTTOM)]})}{let e=(0,s.jsxs)("div",{className:a,style:{flexDirection:"row"},children:[o.get(u.LEFT),(0,s.jsxs)("div",{className:a,style:{flexDirection:"column"},children:[o.get(u.TOP),t,o.get(u.BOTTOM)]}),o.get(u.RIGHT)]});return(0,s.jsxs)("div",{className:r,style:{flexDirection:"row"},children:[n.get(u.LEFT),(0,s.jsxs)("div",{className:a,style:{flexDirection:"column"},children:[n.get(u.TOP),e,n.get(u.BOTTOM)]}),n.get(u.RIGHT)]})}}}renderLayout(){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(tl,{layout:this,node:this.props.model.getRoot(this.windowId)},"__row__"),this.renderEdgeIndicators()]})}renderEdgeIndicators(){let t=[],e=this.icons.edgeArrow;if(this.state.showEdges){let i=this.props.model.getRoot(this.windowId).getRect(),n=tb/2,o=this.getClassName(T.FLEXLAYOUT__EDGE_RECT);t.push((0,s.jsx)("div",{style:{top:0,left:i.width/2-n,width:tb,height:tE,borderBottomLeftRadius:50,borderBottomRightRadius:50},className:o+" "+this.getClassName(T.FLEXLAYOUT__EDGE_RECT_TOP),children:(0,s.jsx)("div",{style:{transform:"rotate(180deg)"},children:e})},"North")),t.push((0,s.jsx)("div",{style:{top:i.height/2-n,left:0,width:tE,height:tb,borderTopRightRadius:50,borderBottomRightRadius:50},className:o+" "+this.getClassName(T.FLEXLAYOUT__EDGE_RECT_LEFT),children:(0,s.jsx)("div",{style:{transform:"rotate(90deg)"},children:e})},"West")),t.push((0,s.jsx)("div",{style:{top:i.height-tE,left:i.width/2-n,width:tb,height:tE,borderTopLeftRadius:50,borderTopRightRadius:50},className:o+" "+this.getClassName(T.FLEXLAYOUT__EDGE_RECT_BOTTOM),children:(0,s.jsx)("div",{children:e})},"South")),t.push((0,s.jsx)("div",{style:{top:i.height/2-n,left:i.width-tE,width:tE,height:tb,borderTopLeftRadius:50,borderBottomLeftRadius:50},className:o+" "+this.getClassName(T.FLEXLAYOUT__EDGE_RECT_RIGHT),children:(0,s.jsx)("div",{style:{transform:"rotate(-90deg)"},children:e})},"East"))}return t}renderWindows(){let e=[];if(this.supportsPopout){let i=this.props.model.getwindowsMap(),n=1;for(let[o,r]of i)o!==H.MAIN_WINDOW_ID&&(e.push((0,s.jsx)(te,{layout:this,title:this.popoutWindowName+" "+n,layoutWindow:r,url:this.popoutURL+"?id="+o,onSetWindow:this.onSetWindow,onCloseWindow:this.onCloseWindow,children:(0,s.jsx)("div",{className:this.props.popoutClassName,children:(0,s.jsx)(t,{...this.props,windowId:o,mainLayout:this})})},o)),n++)}return e}renderTabMoveables(){let t=new Map;return this.props.model.visitNodes(e=>{if(e instanceof A){let i=this.getMoveableElement(e.getId());e.setMoveableElement(i);let n=e.isSelected(),r=e.getParent().getContentRect(),a=n||!e.isEnableRenderOnDemand(),l=e.isRendered()||a&&r.width>0&&r.height>0;if(l){let n=e.getId()+(e.isEnableWindowReMount()?e.getWindowId():"");t.set(e.getId(),(0,o.createPortal)((0,s.jsx)(tc,{layout:this,node:e,rect:r,visible:a,forceRevision:this.state.forceRevision,tabsRevision:this.props.renderRevision},n),i,n)),e.setRendered(l)}}}),t}renderTabStamps(){let t=[];return this.props.model.visitNodes(e=>{e instanceof A&&t.push((0,s.jsx)(tt,{layout:this,node:e},e.getId()))}),t}renderTabs(){let t=new Map;return this.props.model.visitWindowNodes(this.windowId,e=>{if(e instanceof A){let i=e.isSelected(),n=e.getPath();(e.isRendered()||i||!e.isEnableRenderOnDemand())&&t.set(e.getId(),(0,s.jsx)(td,{layout:this,path:n,node:e,selected:i},e.getId()))}}),t}renderMetricsElements(){return(0,s.jsx)("div",{ref:this.findBorderBarSizeRef,className:this.getClassName(T.FLEXLAYOUT__BORDER_SIZER),children:"FindBorderBarSize"},"findBorderBarSize")}checkForBorderToShow(t,e){let i=this.getBoundingClientRect(this.mainRef.current),s=i.getCenter(),n=tb/2,o=!1;this.props.model.isEnableEdgeDock()&&this.state.showHiddenBorder===u.CENTER&&(e>s.y-n&&e<s.y+n||t>s.x-n&&t<s.x+n)&&(o=!0);let r=u.CENTER;!o&&(t<=i.x+tE?r=u.LEFT:t>=i.getRight()-tE?r=u.RIGHT:e<=i.y+tE?r=u.TOP:e>=i.getBottom()-tE&&(r=u.BOTTOM)),r!==this.state.showHiddenBorder&&this.setState({showHiddenBorder:r})}tidyMoveablesMap(){let t=new Map;for(let[e,i]of(this.props.model.visitNodes((e,i)=>{e instanceof A&&t.set(e.getId(),e)}),this.moveableElementMap))t.has(e)||(i.remove(),this.moveableElementMap.delete(e))}reorderComponents(t,e){let i=[],s=new Set,n=[];for(let n of e)t.get(n)&&(i.push(n),s.add(n));for(let[n,o]of(e.splice(0,e.length,...i),t))s.has(n)||e.push(n);return e.map(e=>t.get(e))}redraw(t){this.mainLayout.setState((t,e)=>({forceRevision:t.forceRevision+1}))}redrawInternal(t){this.mainLayout.setState((t,e)=>({layoutRevision:t.layoutRevision+1}))}doAction(t){if(void 0===this.props.onAction)return this.props.model.doAction(t);{let e=this.props.onAction(t);return void 0!==e?this.props.model.doAction(e):void 0}}getBoundingClientRect(t){let e=this.getDomRect();return e?c.getBoundingClientRect(t).relativeTo(e):c.empty()}getMoveableContainer(){return this.moveablesRef.current}getMoveableElement(t){let e=this.moveableElementMap.get(t);return void 0===e&&(e=document.createElement("div"),this.moveablesRef.current.appendChild(e),e.className=T.FLEXLAYOUT__TAB_MOVEABLE,this.moveableElementMap.set(t,e)),e}getMainLayout(){return this.mainLayout}getCurrentDocument(){return this.currentDocument}getDomRect(){return this.state.rect}getWindowId(){return this.windowId}getRootDiv(){return this.selfRef.current}getMainElement(){return this.mainRef.current}getFactory(){return this.props.factory}isSupportsPopout(){return this.supportsPopout}isRealtimeResize(){return this.props.realtimeResize??!1}getPopoutURL(){return this.popoutURL}setEditingTab(t){this.setState({editingTab:t})}getEditingTab(){return this.state.editingTab}getModel(){return this.props.model}getScreenRect(t){let e=t.clone(),i=this.getDomRect();return e.x=this.currentWindow.screenX+this.currentWindow.scrollX+1+i.x+e.x,e.y=this.currentWindow.screenY+this.currentWindow.scrollY+59+i.y+e.y,e.height+=60,e.width+=2,e}addTabToTabSet(t,e){if(void 0!==this.props.model.getNodeById(t))return this.doAction(p.addNode(e,t,u.CENTER,-1))}addTabToActiveTabSet(t){let e=this.props.model.getActiveTabset(this.windowId);if(void 0!==e)return this.doAction(p.addNode(t,e.getId(),u.CENTER,-1))}maximize(t){this.doAction(p.maximizeToggle(t.getId(),this.getWindowId()))}customizeTab(t,e){this.props.onRenderTab&&this.props.onRenderTab(t,e)}customizeTabSet(t,e){this.props.onRenderTabSet&&this.props.onRenderTabSet(t,e)}i18nName(t,e){let i;return this.props.i18nMapper&&(i=this.props.i18nMapper(t,e)),void 0===i&&(i=t+(void 0===e?"":e)),i}getShowOverflowMenu(){return this.props.onShowOverflowMenu}getTabSetPlaceHolderCallback(){return this.props.onTabSetPlaceHolder}showContextMenu(t,e){this.props.onContextMenu&&this.props.onContextMenu(t,e)}auxMouseClick(t,e){this.props.onAuxMouseClick&&this.props.onAuxMouseClick(t,e)}showOverlay(t){this.setState({showOverlay:t}),N(!t,this.currentDocument)}addTabWithDragAndDrop(e,i,s){let n=A.fromJson(i,this.props.model,!1);t.dragState=new tm(this.mainLayout,"add",n,i,s)}moveTabWithDragAndDrop(t,e){this.setDragNode(t,e)}setDragComponent(t,e,i,n){let o=(0,s.jsx)("div",{style:{position:"unset"},className:this.getClassName(T.FLEXLAYOUT__LAYOUT)+" "+this.getClassName(T.FLEXLAYOUT__DRAG_RECT),children:e}),a=this.currentDocument.createElement("div");a.setAttribute("data-layout-path","/drag-rectangle"),a.style.position="absolute",a.style.left="-10000px",a.style.top="-10000px",this.currentDocument.body.appendChild(a),(0,r.createRoot)(a).render(o),t.dataTransfer.setDragImage(a,i,n),setTimeout(()=>{this.currentDocument.body.removeChild(a)},0)}setDraggingOverWindow(t){this.isDraggingOverWindow!==t&&(this.outlineDiv&&(this.outlineDiv.style.visibility=t?"hidden":"visible"),t?this.setState({showEdges:!1}):void 0===this.props.model.getMaximizedTabset(this.windowId)&&this.setState({showEdges:this.props.model.isEnableEdgeDock()}),this.isDraggingOverWindow=t)}clearDragMain(){for(let[,e]of(t.dragState=void 0,this.windowId===H.MAIN_WINDOW_ID&&(this.isDraggingOverWindow=!1),this.props.model.getwindowsMap()))e.layout.clearDragLocal()}clearDragLocal(){this.setState({showEdges:!1}),this.showOverlay(!1),this.dragEnterCount=0,this.dragging=!1,this.outlineDiv&&(this.selfRef.current.removeChild(this.outlineDiv),this.outlineDiv=void 0)}};d(tg,"dragState");let tT=tg,t_={close:(0,s.jsx)(tn,{}),closeTabset:(0,s.jsx)(tn,{}),popout:(0,s.jsx)(()=>(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",style:ts,viewBox:"0 0 20 20",fill:"var(--color-icon)",children:[(0,s.jsx)("path",{d:"M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"}),(0,s.jsx)("path",{d:"M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"})]}),{}),maximize:(0,s.jsx)(()=>(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",style:ts,viewBox:"0 0 24 24",fill:"var(--color-icon)",children:[(0,s.jsx)("path",{d:"M0 0h24v24H0z",fill:"none"}),(0,s.jsx)("path",{stroke:"var(--color-icon)",d:"M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"})]}),{}),restore:(0,s.jsx)(()=>(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",style:ts,viewBox:"0 0 24 24",fill:"var(--color-icon)",children:[(0,s.jsx)("path",{d:"M0 0h24v24H0z",fill:"none"}),(0,s.jsx)("path",{stroke:"var(--color-icon)",d:"M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"})]}),{}),more:(0,s.jsx)(()=>(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",style:ts,viewBox:"0 0 24 24",fill:"var(--color-icon)",children:[(0,s.jsx)("path",{d:"M0 0h24v24H0z",fill:"none"}),(0,s.jsx)("path",{stroke:"var(--color-icon)",d:"M7 10l5 5 5-5z"})]}),{}),edgeArrow:(0,s.jsx)(()=>(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",style:{display:"block",width:10,height:10},preserveAspectRatio:"none",viewBox:"0 0 100 100",children:(0,s.jsx)("path",{fill:"var(--color-edge-icon)",stroke:"var(--color-edge-icon)",d:"M10 30 L90 30 l-40 40 Z"})}),{}),activeTabset:(0,s.jsx)(()=>(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",style:ts,height:"24px",viewBox:"0 -960 960 960",width:"24px",children:(0,s.jsx)("path",{fill:"var(--color-icon)",stroke:"var(--color-icon)",d:"M440-120v-264L254-197l-57-57 187-186H120v-80h264L197-706l57-57 186 187v-264h80v264l186-187 57 57-187 186h264v80H576l187 186-57 57-186-187v264h-80Z"})}),{})},tp=R(),tb=100,tE=10;class tm{constructor(t,e,i,s,n){d(this,"mainLayout"),d(this,"dragSource"),d(this,"dragNode"),d(this,"dragJson"),d(this,"fnNewNodeDropped"),this.mainLayout=t,this.dragSource=e,this.dragNode=i,this.dragJson=s,this.fnNewNodeDropped=n}}}}]);