(()=>{var e={};e.id=658,e.ids=[658],e.modules={3229:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9844:()=>{},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17631:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30561:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\KairosAI\\\\Kairos_t0\\\\frontend\\\\app\\\\popout\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\KairosAI\\Kairos_t0\\frontend\\app\\popout\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},38559:(e,r,t)=>{Promise.resolve().then(t.bind(t,30561))},59261:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75511:(e,r,t)=>{Promise.resolve().then(t.bind(t,80459))},80459:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(60687),n=t(43210),o=t(16189);function i(){let e=(0,o.useSearchParams)().get("id");return e?(0,s.jsx)("div",{className:"h-screen w-full overflow-hidden",children:(0,s.jsx)("div",{id:`popout-content-${e}`,className:"h-full w-full"})}):(0,s.jsx)("div",{children:"Missing popout ID"})}function a(){return(0,s.jsx)(n.Suspense,{fallback:(0,s.jsx)("div",{children:"Loading..."}),children:(0,s.jsx)(i,{})})}},81754:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(37413);function n({children:e}){return(0,s.jsx)("div",{className:"h-screen w-full overflow-hidden bg-transparent",children:e})}t(61120),t(9844),t(17631)},84294:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>c,tree:()=>d});var s=t(65239),n=t(48088),o=t(88170),i=t.n(o),a=t(30893),p={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>a[e]);t.d(r,p);let d={children:["",{children:["popout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,30561)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\KairosAI\\Kairos_t0\\frontend\\app\\popout\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,81754)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\KairosAI\\Kairos_t0\\frontend\\app\\popout\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\KairosAI\\Kairos_t0\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\OneDrive\\Desktop\\KairosAI\\Kairos_t0\\frontend\\app\\popout\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/popout/page",pathname:"/popout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,953,201],()=>t(84294));module.exports=s})();