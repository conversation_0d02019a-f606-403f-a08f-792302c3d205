'use client';

import React, { useState, useEffect } from 'react';
import { Line<PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart } from 'recharts';
import { motion } from 'motion/react';
import { useTheme } from '../../contexts/ThemeContext';

// Sample data for the chart with YOUR COLOR PALETTE
const initialData = [
  { name: 'Jan', actual: 4000, predicted: 4200, confidence: 3800 },
  { name: 'Feb', actual: 3000, predicted: 3100, confidence: 2900 },
  { name: 'Mar', actual: 2000, predicted: 2300, confidence: 1950 },
  { name: 'Apr', actual: 2780, predicted: 2600, confidence: 2650 },
  { name: 'May', actual: 1890, predicted: 1800, confidence: 1750 },
  { name: 'Jun', actual: 2390, predicted: 2500, confidence: 2350 },
  { name: 'Jul', actual: 3490, predicted: 3400, confidence: 3300 },
  { name: 'Aug', actual: 4000, predicted: 4100, confidence: 3950 },
  { name: 'Sep', actual: 5000, predicted: 4800, confidence: 4750 },
  { name: 'Oct', actual: 6000, predicted: 5900, confidence: 5850 },
  { name: 'Nov', actual: 7000, predicted: 7200, confidence: 6950 },
  { name: 'Dec', actual: 8000, predicted: 8100, confidence: 7900 },
];

// Custom tooltip component with YOUR COLORS
const CustomTooltip = ({ active, payload, label }: any) => {
  if (!active || !payload?.length) return null;

  return (
    <motion.div
      className="glass-card p-4 border border-border/50 text-foreground text-sm shadow-2xl"
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.2 }}
    >
      <p className="font-bold text-foreground mb-2">{label}</p>
      <div className="space-y-1">
        <p className="flex items-center">
          <div className="w-3 h-3 rounded-full bg-kairos-red mr-2"></div>
          <span className="text-kairos-red">Actual: {payload[0]?.value}</span>
        </p>
        <p className="flex items-center">
          <div className="w-3 h-3 rounded-full bg-kairos-purple mr-2"></div>
          <span className="text-kairos-purple">Predicted: {payload[1]?.value}</span>
        </p>
        <p className="flex items-center">
          <div className="w-3 h-3 rounded-full bg-kairos-beige mr-2"></div>
          <span className="text-kairos-beige">Confidence: {payload[2]?.value}</span>
        </p>
      </div>
    </motion.div>
  );
};

export default function DemoChart() {
  const { theme } = useTheme();
  const [data, setData] = useState(initialData);
  const [key, setKey] = useState(0);

  // Effect to periodically refresh the animation
  useEffect(() => {
    const interval = setInterval(() => {
      // Add small random variations to make the chart look dynamic
      const newData = initialData.map(item => ({
        ...item,
        actual: item.actual + (Math.random() * 400 - 200),
        predicted: item.predicted + (Math.random() * 400 - 200),
        confidence: item.confidence + (Math.random() * 300 - 150)
      }));
      setData(newData);
      setKey(prev => prev + 1);
    }, 8000); // Refresh every 8 seconds

    return () => clearInterval(interval);
  }, []);

  // Dynamic colors based on theme - DARKER GRID LINES
  const gridColor = theme === 'dark' ? 'rgba(201, 173, 167, 0.3)' : 'rgba(74, 78, 105, 0.4)';
  const axisColor = theme === 'dark' ? '#c9ada7' : '#4a4e69';

  return (
    <motion.div
      className="w-full h-full min-h-[400px] p-6 bg-transparent relative overflow-hidden"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true, amount: 0.3 }}
      transition={{ duration: 0.8 }}
    >
      {/* Animated background gradient */}
      <div className="absolute inset-0 bg-kairos-gradient opacity-5 animate-gradient-shift bg-[length:200%_200%]"></div>

      <motion.div
        className="mb-6 relative z-10"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <h3 className="text-2xl font-inknut bg-gradient-to-r from-kairos-red via-kairos-beige to-kairos-purple bg-clip-text text-transparent animate-gradient-shift bg-[length:200%_200%]">
          Time Series Forecast Demo
        </h3>
        <p className="text-muted mt-2">Real-time AI predictions with confidence intervals</p>
      </motion.div>

      <motion.div
        className="relative z-10"
        initial={{ scale: 0.9, opacity: 0 }}
        whileInView={{ scale: 1, opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8, delay: 0.4 }}
      >
        <ResponsiveContainer width="100%" height={350}>
          <LineChart
            key={key}
            data={data}
            margin={{
              top: 20,
              right: 40,
              left: 20,
              bottom: 20,
            }}
          >
            <CartesianGrid
              strokeDasharray="2 4"
              stroke={gridColor}
              strokeOpacity={0.8}
              strokeWidth={1.5}
            />
            <XAxis
              dataKey="name"
              stroke={axisColor}
              fontSize={12}
              fontWeight={500}
            />
            <YAxis
              stroke={axisColor}
              fontSize={12}
              fontWeight={500}
            />
            <Tooltip content={<CustomTooltip />} />

            {/* Confidence interval line */}
            <Line
              type="monotone"
              dataKey="confidence"
              stroke="#c9ada7"
              strokeWidth={1}
              strokeDasharray="5 5"
              dot={false}
              activeDot={false}
              isAnimationActive={true}
              animationDuration={3000}
              animationEasing="ease-in-out"
            />

            {/* Actual values line - YOUR KAIROS RED */}
            <Line
              type="monotone"
              dataKey="actual"
              stroke="#f2e9e4"
              strokeWidth={3}
              dot={{
                r: 5,
                strokeWidth: 2,
                fill: "#f2e9e4",
                stroke: "#22223b",
                filter: "drop-shadow(0 0 6px #f2e9e4)"
              }}
              activeDot={{
                r: 8,
                fill: "#f2e9e4",
                stroke: "#22223b",
                strokeWidth: 3,
                filter: "drop-shadow(0 0 12px #f2e9e4)"
              }}
              isAnimationActive={true}
              animationDuration={4000}
              animationEasing="ease-in-out"
            />

            {/* Predicted values line - YOUR KAIROS PURPLE */}
            <Line
              type="monotone"
              dataKey="predicted"
              stroke="#9a8c98"
              strokeWidth={3}
              dot={{
                r: 5,
                strokeWidth: 2,
                fill: "#9a8c98",
                stroke: "#22223b",
                filter: "drop-shadow(0 0 6px #9a8c98)"
              }}
              activeDot={{
                r: 8,
                fill: "#9a8c98",
                stroke: "#22223b",
                strokeWidth: 3,
                filter: "drop-shadow(0 0 12px #9a8c98)"
              }}
              isAnimationActive={true}
              animationDuration={2500}
              animationEasing="ease-in-out"
            />
          </LineChart>
        </ResponsiveContainer>
      </motion.div>

      {/* Beautiful legend with YOUR COLORS */}
      <motion.div
        className="flex justify-center mt-8 space-x-8"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6, delay: 1.0 }}
      >
        <motion.div
          className="flex items-center group cursor-pointer"
          whileHover={{ scale: 1.05 }}
        >
          <div className="w-4 h-4 rounded-full bg-kairos-red mr-3 shadow-lg group-hover:shadow-kairos-red/50 transition-all duration-300"></div>
          <span className="text-sm font-medium text-foreground group-hover:text-kairos-red transition-colors">Actual Values</span>
        </motion.div>

        <motion.div
          className="flex items-center group cursor-pointer"
          whileHover={{ scale: 1.05 }}
        >
          <div className="w-4 h-4 rounded-full bg-kairos-purple mr-3 shadow-lg group-hover:shadow-kairos-purple/50 transition-all duration-300"></div>
          <span className="text-sm font-medium text-foreground group-hover:text-kairos-purple transition-colors">AI Predictions</span>
        </motion.div>

        <motion.div
          className="flex items-center group cursor-pointer"
          whileHover={{ scale: 1.05 }}
        >
          <div className="w-4 h-1 bg-kairos-beige mr-3 opacity-70 group-hover:opacity-100 transition-all duration-300" style={{borderRadius: '2px', borderStyle: 'dashed', borderWidth: '1px', borderColor: '#c9ada7'}}></div>
          <span className="text-sm font-medium text-foreground group-hover:text-kairos-beige transition-colors">Confidence</span>
        </motion.div>
      </motion.div>

      {/* Animated stats */}
      <motion.div
        className="mt-8 grid grid-cols-3 gap-4 text-center"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6, delay: 1.2 }}
      >
        <div className="glass-card p-4 border-l-4 border-kairos-red">
          <div className="text-2xl font-bold text-kairos-red animate-color-cycle">94.2%</div>
          <div className="text-xs text-muted">Accuracy</div>
        </div>
        <div className="glass-card p-4 border-l-4 border-kairos-purple">
          <div className="text-2xl font-bold text-kairos-purple animate-color-cycle">2.1s</div>
          <div className="text-xs text-muted">Response Time</div>
        </div>
        <div className="glass-card p-4 border-l-4 border-kairos-beige">
          <div className="text-2xl font-bold text-kairos-beige animate-color-cycle">12M+</div>
          <div className="text-xs text-muted">Data Points</div>
        </div>
      </motion.div>
    </motion.div>
  );
}












