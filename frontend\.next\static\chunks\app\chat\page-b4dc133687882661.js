(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[457],{75720:(e,t,n)=>{Promise.resolve().then(n.bind(n,80991)),Promise.resolve().then(n.bind(n,88657))},88657:(e,t,n)=>{"use strict";n.d(t,{default:()=>d});var s=n(95155),c=n(6874),a=n.n(c),o=n(35695),l=n(12115);function d(){let e=(0,o.usePathname)(),[t,n]=(0,l.useState)(!1),c=t=>e===t?"text-white font-medium":"text-gray-300 hover:text-white";return(0,s.jsxs)("nav",{className:"p-4 font-inknut shadow-sm",children:[(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(a(),{href:"/",className:"text-xl text-white font-inknut",children:"Kairos"})}),(0,s.jsx)("div",{className:"hidden md:block",children:(0,s.jsxs)("div",{className:"ml-10 flex items-center space-x-4",children:[(0,s.jsx)(a(),{href:"/",className:"".concat(c("/")," px-3 py-2 rounded-md text-sm font-inknut"),children:"Home"}),(0,s.jsx)(a(),{href:"/chat",className:"".concat(c("/chat")," px-3 py-2 rounded-md text-sm font-inknut"),children:"Chat"}),(0,s.jsx)(a(),{href:"/about",className:"".concat(c("/about")," px-3 py-2 rounded-md text-sm font-inknut"),children:"About"}),(0,s.jsx)(a(),{href:"/contact",className:"".concat(c("/contact")," px-3 py-2 rounded-md text-sm font-inknut"),children:"Contact"})]})}),(0,s.jsx)("div",{className:"md:hidden flex items-center",children:(0,s.jsxs)("button",{type:"button",onClick:()=>{n(!t)},className:"inline-flex items-center justify-center p-2 rounded-md text-gray-300 hover:text-white focus:outline-none","aria-expanded":"false",children:[(0,s.jsx)("span",{className:"sr-only",children:"Open main menu"}),(0,s.jsx)("svg",{className:"".concat(t?"hidden":"block"," h-6 w-6"),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 6h16M4 12h16M4 18h16"})}),(0,s.jsx)("svg",{className:"".concat(t?"block":"hidden"," h-6 w-6"),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})]})})]})}),(0,s.jsx)("div",{className:"".concat(t?"block":"hidden"," md:hidden"),children:(0,s.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 flex flex-col items-center",children:[(0,s.jsx)(a(),{href:"/",className:"".concat(c("/")," block px-3 py-2 rounded-md text-base font-inknut text-center w-full"),onClick:()=>n(!1),children:"Home"}),(0,s.jsx)(a(),{href:"/chat",className:"".concat(c("/chat")," block px-3 py-2 rounded-md text-base font-inknut text-center w-full"),onClick:()=>n(!1),children:"Chat"}),(0,s.jsx)(a(),{href:"/about",className:"".concat(c("/about")," block px-3 py-2 rounded-md text-base font-inknut text-center w-full"),onClick:()=>n(!1),children:"About"}),(0,s.jsx)(a(),{href:"/contact",className:"".concat(c("/contact")," block px-3 py-2 rounded-md text-base font-inknut text-center w-full"),onClick:()=>n(!1),children:"Contact"})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[309,874,517,110,991,441,684,358],()=>t(75720)),_N_E=e.O()}]);