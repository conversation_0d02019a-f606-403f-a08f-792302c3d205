(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[356],{34750:(e,t,a)=>{Promise.resolve().then(a.bind(a,41643))},41643:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>$});var o=a(95155),r=a(12115),n=a(83760);a(62729),a(50674);var l=a(80991);function s(e){let{onSend:t,maxHeight:a="120px",inSidebar:r=!0}=e;return(0,o.jsx)(l.default,{onSend:e=>{t?t(e):console.log("Message sent but no handler provided:",e)},skipNavigation:!0,autoGrow:!0,maxHeight:a,inSidebar:r})}var i=a(83540),c=a(53989),d=a(94754),u=a(96025),h=a(16238),f=a(94517),m=a(24026),p=a(21374),x=a(45164),b=a(47110),g=a(10184),w=a(48987),v=a(74500),y=a(5279);let j=()=>{let e=[],t=new Date;for(let a=0;a<100;a++){let o=new Date(t);o.setDate(t.getDate()-(100-a));let r=o.toISOString().split("T")[0],n=.5*a,l=20*Math.sin(a/10),s=100+n+l+(10*Math.random()-5),i=a>70?1.05*s+(5*Math.random()-2.5):null,c=a>70?.92*s+.7*n+(3*Math.random()-1.5):null,d=a>70?.95*s+1.2*l+(4*Math.random()-2):null;e.push({name:r,date:r,value:s,forecast:i,arimaForecast:c,emaForecast:d})}return e},k=e=>{let{active:t,payload:a,label:r,selectedPoint:n,isSelected:l}=e;if(!t||!(null==a?void 0:a.length))return null;let s=l&&(null==n?void 0:n.name)===r&&(null==n?void 0:n.value)===a[0].value;return(0,o.jsxs)("div",{className:"p-2 rounded border shadow-lg text-xs transition-all duration-150 ".concat(s?"bg-indigo-900 border-indigo-500 text-white scale-110":"bg-black/80 border-white/20 text-white"),children:[(0,o.jsx)("p",{className:"font-bold",children:r}),a.map((e,t)=>{var a;return(0,o.jsxs)("p",{className:s&&0===t?"text-indigo-300":"text-[".concat(e.color,"]"),children:[e.name,": ",null==(a=e.value)?void 0:a.toFixed(2)]},"value-".concat(t))}),s&&(0,o.jsx)("div",{className:"mt-1 text-xs text-indigo-200",children:"Selected"})]})},N=()=>{let{chartData:e,setChartSelection:t,chartSelection:a}=(0,b.Y)(),[n,l]=(0,r.useState)(null),[s,N]=(0,r.useState)(!1),[C,S]=(0,r.useState)(0),[E,A]=(0,r.useState)(0),[M,D]=(0,r.useState)(()=>e||j()),[F,z]=(0,r.useState)(0),[T,L]=(0,r.useState)(0),I=(0,r.useRef)(null);(0,r.useEffect)(()=>{e&&(D(e),S(e=>e+1))},[e,M]),(0,r.useEffect)(()=>{A(e=>e+1)},[s]),(0,r.useEffect)(()=>{(null==a?void 0:a.point)?l(a.point):l(null)},[a]);let[P,W]=(0,r.useState)({selection:{start:null,end:null,isSelecting:!1},zoom:{xMin:0,xMax:M.length-1,yMin:0,yMax:Math.max(...M.map(e=>e.value))+100}}),R=(0,r.useMemo)(()=>M,[M]),B=(0,r.useMemo)(()=>n?R.map(e=>({...e,_selected:e.name===n.name&&e.value===n.value})):R,[R,n]);(0,r.useCallback)((e,t)=>{let a=M.findIndex(t=>t.name===e),o=M.findIndex(e=>e.name===t);if(-1===a||-1===o)return[0,1e3];let r=Math.min(a,o),n=Math.max(a,o),l=M.slice(r,n+1),s=Math.min(...l.map(e=>e.value)),i=Math.max(...l.map(e=>e.value)),c=(i-s)*.1;return[Math.floor(s-c),Math.ceil(i+c)]},[M]),(0,r.useEffect)(()=>{z(0),L(0);let e=Date.now(),t=()=>{let a=Date.now()-e,o=Math.min(a/1500,1);z(o),o>=1&&L(Math.min((a-1500)/1500,1)),(o<1||T<1)&&requestAnimationFrame(t)};requestAnimationFrame(t)},[M]);let O=(0,r.useCallback)(e=>{(null==e?void 0:e.activeLabel)&&W(t=>({...t,selection:{start:e.activeLabel,end:null,isSelecting:!0}}))},[]),V=(0,r.useCallback)(e=>{P.selection.isSelecting&&(null==e?void 0:e.activeLabel)&&W(t=>({...t,selection:{...t.selection,end:e.activeLabel}}))},[P.selection.isSelecting]),G=(0,r.useCallback)(()=>{W(e=>{if(!e.selection.start||!e.selection.end)return{...e,selection:{start:null,end:null,isSelecting:!1}};let a=M.findIndex(t=>t.name===e.selection.start),o=M.findIndex(t=>t.name===e.selection.end);if(-1!==a&&-1!==o)if(e.selection.start===e.selection.end){let e=M[a];setTimeout(()=>{t({point:{name:e.name,value:e.value}})},0)}else{let a={start:e.selection.start,end:e.selection.end};setTimeout(()=>{t({range:a,point:n})},0)}return{...e,selection:{...e.selection,isSelecting:!1}}})},[M,t,n]),K=(0,r.useCallback)(e=>{let a=e.payload;if((null==n?void 0:n.name)===a.name&&(null==n?void 0:n.value)===a.value){l(null),t(e=>({...e,point:void 0}));return}l(a),t(e=>({...e,point:{name:a.name,value:a.value}}))},[n,t]),_=(0,r.useCallback)(()=>{t({}),l(null),W({selection:{start:null,end:null,isSelecting:!1},zoom:{xMin:0,xMax:M.length-1,yMin:0,yMax:Math.max(...M.map(e=>e.value))+100}})},[M,t]),J=(0,r.useCallback)(()=>{t({}),l(null),W(e=>({...e,selection:{start:null,end:null,isSelecting:!1}}))},[t]),Y=(0,r.useCallback)(e=>{let{cx:t,cy:a,payload:r,index:l}=e;return void 0===r.value||null===r.value?null:(null==n?void 0:n.name)===r.name&&(null==n?void 0:n.value)===r.value?(0,o.jsx)("circle",{cx:t,cy:a,r:6,fill:"#4f46e5",stroke:"#ffffff",strokeWidth:2},"dot-".concat(l,"-").concat(r.name)):(0,o.jsx)("circle",{cx:t,cy:a,r:2,fill:"#8884d8",stroke:"#8884d8",strokeWidth:1},"dot-".concat(l,"-").concat(r.name))},[n]),U=(0,r.useCallback)(e=>{let{cx:t,cy:a,stroke:r,payload:l,index:s,dataKey:i}=e;if(void 0===l.value||null===l.value)return null;let c=(null==n?void 0:n.name)===l.name&&(null==n?void 0:n.value)===l.value;return(0,o.jsx)("circle",{cx:t,cy:a,r:c?8:6,stroke:c?"#ffffff":"white",strokeWidth:c?3:2,fill:c?"#4f46e5":"#ff7300",style:{cursor:"pointer"},onClick:()=>K(e)},"active-dot-".concat(s,"-").concat(l.name))},[n,K]),q=(0,r.useCallback)(e=>{let{cx:t,cy:a,payload:r,index:l}=e;return void 0===r.arimaForecast||null===r.arimaForecast?null:(null==n?void 0:n.name)===r.name&&(null==n?void 0:n.value)===r.arimaForecast?(0,o.jsx)("circle",{cx:t,cy:a,r:6,fill:"#ff7300",stroke:"#ffffff",strokeWidth:2},"arima-dot-".concat(l,"-").concat(r.name)):(0,o.jsx)("circle",{cx:t,cy:a,r:2,fill:"#ff7300",stroke:"#ff7300",strokeWidth:1},"arima-dot-".concat(l,"-").concat(r.name))},[n]),H=(0,r.useCallback)(e=>{let{cx:t,cy:a,stroke:r,payload:l,index:s}=e;if(void 0===l.arimaForecast||null===l.arimaForecast)return null;let i=(null==n?void 0:n.name)===l.name&&(null==n?void 0:n.value)===l.arimaForecast;return(0,o.jsx)("circle",{cx:t,cy:a,r:i?8:6,stroke:i?"#ffffff":"white",strokeWidth:i?3:2,fill:"#ff7300",style:{cursor:"pointer"},onClick:()=>K({...e,payload:{...e.payload,value:e.payload.arimaForecast}})},"arima-active-dot-".concat(s,"-").concat(l.name))},[n,K]),Z=(0,r.useCallback)(e=>{let{cx:t,cy:a,payload:r,index:l}=e;return void 0===r.emaForecast||null===r.emaForecast?null:(null==n?void 0:n.name)===r.name&&(null==n?void 0:n.value)===r.emaForecast?(0,o.jsx)("circle",{cx:t,cy:a,r:6,fill:"#0088fe",stroke:"#ffffff",strokeWidth:2},"ema-dot-".concat(l,"-").concat(r.name)):(0,o.jsx)("circle",{cx:t,cy:a,r:2,fill:"#0088fe",stroke:"#0088fe",strokeWidth:1},"ema-dot-".concat(l,"-").concat(r.name))},[n]),$=(0,r.useCallback)(e=>{let{cx:t,cy:a,stroke:r,payload:l,index:s}=e;if(void 0===l.emaForecast||null===l.emaForecast)return null;let i=(null==n?void 0:n.name)===l.name&&(null==n?void 0:n.value)===l.emaForecast;return(0,o.jsx)("circle",{cx:t,cy:a,r:i?8:6,stroke:i?"#ffffff":"white",strokeWidth:i?3:2,fill:"#0088fe",style:{cursor:"pointer"},onClick:()=>K({...e,payload:{...e.payload,value:e.payload.emaForecast}})},"ema-active-dot-".concat(s,"-").concat(l.name))},[n,K]),Q=(0,r.useCallback)(e=>{let{cx:t,cy:a,payload:r,index:l}=e;return void 0===r.forecast||null===r.forecast?null:(null==n?void 0:n.name)===r.name&&(null==n?void 0:n.value)===r.forecast?(0,o.jsx)("circle",{cx:t,cy:a,r:6,fill:"#82ca9d",stroke:"#ffffff",strokeWidth:2},"forecast-dot-".concat(l,"-").concat(r.name)):(0,o.jsx)("circle",{cx:t,cy:a,r:2,fill:"#82ca9d",stroke:"#82ca9d",strokeWidth:1},"forecast-dot-".concat(l,"-").concat(r.name))},[n]),X=(0,r.useCallback)(e=>{let{cx:t,cy:a,stroke:r,payload:l,index:s}=e;if(void 0===l.forecast||null===l.forecast)return null;let i=(null==n?void 0:n.name)===l.name&&(null==n?void 0:n.value)===l.forecast;return(0,o.jsx)("circle",{cx:t,cy:a,r:i?8:6,stroke:i?"#ffffff":"white",strokeWidth:i?3:2,fill:"#82ca9d",style:{cursor:"pointer"},onClick:()=>K({...e,payload:{...e.payload,value:e.payload.forecast}})},"forecast-active-dot-".concat(s,"-").concat(l.name))},[n,K]);return(0,o.jsxs)("div",{className:"glass-card flex-1 p-4 m-1 flex flex-col overflow-hidden h-full",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-2 text-white px-1",children:[(0,o.jsxs)("h4",{className:"text-base font-semibold font-inknut text-red-400",children:[(0,o.jsx)("span",{className:"text-white/60",children:"Time Series "}),e?"AI Generated Chart":"Forecast"]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[n&&(0,o.jsxs)("div",{className:"bg-indigo-900/80 px-3 py-1 rounded border border-indigo-600/40 text-white text-xs",children:[(0,o.jsxs)("span",{className:"font-medium text-indigo-300",children:[n.name,":"]}),(0,o.jsx)("span",{className:"ml-1",children:"number"==typeof n.value?n.value.toFixed(2):n.value})]}),(0,o.jsxs)("button",{type:"button",onClick:()=>N(!s),className:"p-1.5 rounded bg-white/5 hover:bg-white/10 text-white transition-all duration-200 shadow hover:scale-110 hover:shadow-lg group relative border border-white/10 backdrop-blur-sm ".concat(s?"bg-white/10":""),children:[s?(0,o.jsx)(g.A,{className:"w-4 h-4"}):(0,o.jsx)(w.A,{className:"w-4 h-4"}),(0,o.jsx)("span",{className:"absolute top-full mt-1 left-1/2 -translate-x-1/2 bg-gray-800/90 backdrop-blur-sm text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap border border-white/10",children:s?"Show Main Forecast":"Show Comparison Models"})]}),(0,o.jsxs)("button",{type:"button",onClick:J,className:"p-1.5 rounded bg-white/5 hover:bg-white/10 text-white transition-all duration-200 shadow hover:scale-110 hover:shadow-lg group relative border border-white/10 backdrop-blur-sm",children:[(0,o.jsx)(v.A,{className:"w-4 h-4"}),(0,o.jsx)("span",{className:"absolute top-full mt-1 left-1/2 -translate-x-1/2 bg-gray-800/90 backdrop-blur-sm text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap border border-white/10",children:"Clear Selection"})]}),(0,o.jsxs)("button",{type:"button",onClick:_,className:"p-1.5 rounded bg-white/5 hover:bg-white/10 text-white transition-all duration-200 shadow hover:scale-110 hover:shadow-lg group relative border border-white/10 backdrop-blur-sm",children:[(0,o.jsx)(y.A,{className:"w-4 h-4"}),(0,o.jsx)("span",{className:"absolute top-full mt-1 left-1 -translate-x-1/2 bg-gray-800/90 backdrop-blur-sm text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap border border-white/10",children:"Reset View"})]})]})]}),(0,o.jsx)("div",{className:"text-xs text-white/70 px-1 mb-2",children:"Click on a point to select it, or click and drag to select a range."}),(0,o.jsx)("div",{className:"flex-1 overflow-hidden select-none touch-none",onContextMenu:e=>e.preventDefault(),children:(0,o.jsx)(i.u,{width:"100%",height:"100%",children:(0,o.jsxs)(c.b,{ref:I,data:B,onMouseDown:O,onMouseMove:V,onMouseUp:G,margin:{top:5,right:20,left:10,bottom:5},children:[(0,o.jsx)(d.d,{strokeDasharray:"3 3",stroke:"rgba(255, 255, 255, 0.15)"}),(0,o.jsx)(u.W,{dataKey:"name",type:"category",tick:{fill:"rgba(255, 255, 255, 0.7)",fontSize:10},stroke:"rgba(255, 255, 255, 0.3)",tickFormatter:e=>{if(/^\d{4}-\d{2}-\d{2}$/.test(e)){let t=new Date(e);return"".concat(t.getMonth()+1,"/").concat(t.getDate())}return e}}),(0,o.jsx)(h.h,{type:"number",domain:["auto","auto"],tick:{fill:"rgba(255, 255, 255, 0.7)",fontSize:10},stroke:"rgba(255, 255, 255, 0.3)",allowDecimals:!1,width:50}),(0,o.jsx)(f.m,{content:(0,o.jsx)(k,{selectedPoint:n,isSelected:!!n}),wrapperStyle:{zIndex:1e3}}),(0,o.jsx)(m.s,{verticalAlign:"top",height:36,wrapperStyle:{paddingTop:"10px",fontSize:"12px",color:"rgba(255, 255, 255, 0.7)"}}),(0,o.jsx)(p.N,{type:"monotone",dataKey:"value",stroke:"#8884d8",strokeWidth:2,name:"Actual",dot:Y,activeDot:U,isAnimationActive:!0,animationDuration:1e3,animationEasing:"ease-in-out"},"actual-line-".concat(C)),(0,o.jsx)(p.N,{type:"monotone",dataKey:"forecast",stroke:"#82ca9d",strokeWidth:2,name:"Kairos Forecast",dot:Q,activeDot:X,isAnimationActive:!0,animationDuration:1e3,animationBegin:1e3*(C!==E),animationEasing:"ease-in-out",connectNulls:!0},"forecast-line-".concat(C,"-").concat(E)),s&&(0,o.jsx)(p.N,{type:"monotone",dataKey:"arimaForecast",stroke:"#ff7300",name:"ARIMA",strokeWidth:2,dot:q,activeDot:H,isAnimationActive:!0,animationDuration:1e3,animationBegin:1e3*(C!==E),animationEasing:"ease-in-out",connectNulls:!0},"arima-forecast-line-".concat(C,"-").concat(E)),s&&(0,o.jsx)(p.N,{type:"monotone",dataKey:"emaForecast",stroke:"#0088fe",name:"EMA",strokeWidth:2,dot:Z,activeDot:$,isAnimationActive:!0,animationDuration:1e3,animationBegin:1e3*(C!==E),animationEasing:"ease-in-out",connectNulls:!0},"ema-forecast-line-".concat(C,"-").concat(E)),P.selection.start&&P.selection.end&&(0,o.jsx)(x.T,{x1:P.selection.start,x2:P.selection.end,stroke:"rgba(255, 115, 0, 0.7)",fill:"rgba(255, 115, 0, 0.2)",strokeOpacity:1})]},"main-chart")})})]})};var C=a(55028);a(69934);var S=a(2866),E=a(47655),A=a(80025),M=a(57765),D=a(47165),F=a(25052);let z=(0,C.default)(()=>Promise.all([a.e(747),a.e(711)]).then(a.bind(a,48711)),{loadableGenerated:{webpack:()=>[48711]},ssr:!1,loading:()=>(0,o.jsx)("div",{className:"flex-1 w-full flex items-center justify-center",children:"Loading graph..."})}),T="#8C52FF",L="#2A3F50",I="#4CAF79",P=[{selector:"node",style:{"background-color":T,label:"data(label)",color:"#fff","text-valign":"center","text-halign":"center","font-family":"Inknut Antiqua, serif","font-size":"10px",width:"50px",height:"50px","border-width":"2px","border-color":"rgba(255, 255, 255, 0.8)","border-opacity":.8,"border-style":"solid","text-outline-width":"1px","text-outline-color":L,"text-outline-opacity":.9,shape:"round-hexagon","shadow-blur":"10px","shadow-color":T,"shadow-opacity":.4}},{selector:"edge",style:{width:3,"line-color":"rgba(255, 255, 255, 0.6)","line-opacity":.4,"line-style":"solid","curve-style":"bezier","target-arrow-color":"rgba(255, 255, 255, 0.6)","target-arrow-opacity":.4,"target-arrow-shape":"triangle","target-arrow-fill":"filled","arrow-scale":1.2,label:"data(label)","font-size":"9px",color:"#fff","text-outline-width":"1px","text-outline-color":"#000","text-background-color":L,"text-background-opacity":.7,"text-background-padding":"2px","text-background-shape":"roundrectangle"}},{selector:":selected",style:{"background-color":I,"border-color":"#ffffff","border-width":"3px","line-color":"#ffffff","target-arrow-color":"#ffffff","source-arrow-color":"#ffffff","shadow-blur":"20px","shadow-color":"#ffffff","shadow-opacity":.8,"text-outline-color":I,"transition-property":"background-color, border-color, border-width, line-color, target-arrow-color, source-arrow-color, shadow-blur, shadow-opacity","transition-duration":"0.3s"}},{selector:".highlighted",style:{"background-color":I,"border-color":"#ffffff","border-width":"3px","line-color":"#ffffff","target-arrow-color":"#ffffff","source-arrow-color":"#ffffff","transition-property":"background-color, line-color, target-arrow-color, source-arrow-color, border-color, border-width, shadow-blur, shadow-opacity","transition-duration":"0.4s","shadow-blur":"20px","shadow-color":"#ffffff","shadow-opacity":.8}},{selector:".dimmed",style:{opacity:.2,"transition-property":"opacity","transition-duration":"0.3s"}},{selector:".pulse",style:{"border-width":"3px","border-color":"#ffffff","border-opacity":1,"shadow-blur":"25px","shadow-color":"#ffffff","shadow-opacity":.9}},{selector:"node.hover",style:{"border-color":"#ffffff","border-width":"3px","shadow-blur":"15px","shadow-color":"#ffffff","shadow-opacity":.7,"transition-property":"border-width, shadow-blur, shadow-opacity","transition-duration":"0.2s"}},{selector:"edge.hover",style:{width:4,"line-color":"#ffffff","line-opacity":.8,"target-arrow-color":"#ffffff","target-arrow-opacity":.8,"shadow-blur":"15px","shadow-color":"#ffffff","shadow-opacity":.6,"transition-property":"width, line-color, line-opacity, target-arrow-color, shadow-blur, shadow-opacity","transition-duration":"0.2s"}}],W={name:"dagre",rankSep:120,padding:20,animate:!0,animationDuration:800,animationEasing:"ease-in-out"},R=[{data:{id:"a",label:"Variable A"}},{data:{id:"b",label:"Variable B"}},{data:{id:"c",label:"Variable C"}},{data:{id:"d",label:"Variable D"}},{data:{id:"e",label:"Variable E"}},{data:{id:"ab",source:"a",target:"b",label:"0.7"}},{data:{id:"ac",source:"a",target:"c",label:"0.3"}},{data:{id:"bd",source:"b",target:"d",label:"0.5"}},{data:{id:"ce",source:"c",target:"e",label:"0.6"}}],B=()=>{let{causalityData:e}=(0,b.Y)(),t=(0,r.useRef)(null),[n,l]=(0,r.useState)(!0),s=(0,r.useRef)(null),[i,c]=(0,r.useState)("TB");(0,r.useEffect)(()=>((async()=>{try{let e=await Promise.resolve().then(a.t.bind(a,69934,23)),t=await a.e(747).then(a.bind(a,54641));t.default&&t.default.use?t.default.use(e.default):t.use&&t.use(e.default),l(!1)}catch(e){l(!1),console.error("Error loading cytoscape extensions:",e)}})(),()=>{s.current&&clearInterval(s.current)}),[]),(0,r.useEffect)(()=>{t.current&&(t.current.layout({name:"dagre",rankDir:i,rankSep:120,padding:20,animate:!0,animationDuration:800,animationEasing:"ease-in-out"}).run(),t.current.fit())},[e,i]),(0,r.useEffect)(()=>{let e=()=>{t.current&&t.current.fit()};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let d=e=>{if(!e||!t.current)return;let a=t.current.container();if(!a)return;let o=document.createElement("div");o.className="edge-particle",o.style.position="absolute",o.style.width="8px",o.style.height="8px",o.style.borderRadius="50%",o.style.backgroundColor="#ffffff",o.style.boxShadow="0 0 10px 2px rgba(140, 82, 255, 0.8)",o.style.transition="transform 2s ease-in-out, opacity 2s ease-in-out",o.style.opacity="0",o.style.zIndex="999",a.appendChild(o),setTimeout(()=>{o.style.opacity="1";let t=e.source().renderedPosition(),r=e.target().renderedPosition();o.style.transform="translate(".concat(t.x,"px, ").concat(t.y,"px)"),setTimeout(()=>{o.style.transform="translate(".concat(r.x,"px, ").concat(r.y,"px)"),setTimeout(()=>{o.style.opacity="0",setTimeout(()=>{a.removeChild(o)},2e3)},1800)},50)},50)},u=R,h=!0;return e&&Array.isArray(e.nodes)&&Array.isArray(e.edges)&&e.nodes.length>0&&e.edges.length>0?(u=[...e.nodes,...e.edges],h=!0):!e||0!==Object.keys(e).length&&e.nodes&&e.edges||(h=!1),(0,o.jsxs)("div",{className:"relative w-full h-full bg-[#1A1A1A] rounded-lg overflow-hidden glass-card",children:[(0,o.jsxs)("div",{className:"absolute top-4 right-6 z-20 flex items-center space-x-2 pointer-events-none",children:[(0,o.jsxs)("button",{className:"p-1.5 rounded bg-white/5 hover:bg-white/10 text-white transition-all duration-200 shadow hover:scale-110 hover:shadow-lg group relative border border-white/10 backdrop-blur-sm pointer-events-auto",onClick:()=>{let e="TB"===i?"LR":"TB";c(e),t.current&&(t.current.layout({name:"dagre",rankDir:e,rankSep:120,padding:20,animate:!0,animationDuration:800,animationEasing:"ease-in-out"}).run(),t.current.fit())},children:["TB"===i?(0,o.jsx)(S.A,{className:"w-4 h-4"}):(0,o.jsx)(E.A,{className:"w-4 h-4"}),(0,o.jsx)("span",{className:"absolute top-full mt-1 left-1/2 -translate-x-1/2 bg-gray-800/90 backdrop-blur-sm text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap border border-white/10 pointer-events-none z-50",children:"Rotate Graph 90\xb0"})]}),(0,o.jsxs)("button",{className:"p-1.5 rounded bg-white/5 hover:bg-white/10 text-white transition-all duration-200 shadow hover:scale-110 hover:shadow-lg group relative border border-white/10 backdrop-blur-sm pointer-events-auto",onClick:()=>{t.current&&(t.current.layout({name:"dagre",rankDir:i,rankSep:120,padding:20,animate:!0,animationDuration:800,animationEasing:"ease-in-out"}).run(),t.current.fit())},children:[(0,o.jsx)(A.A,{className:"w-4 h-4"}),(0,o.jsx)("span",{className:"absolute top-full mt-1 left-1/2 -translate-x-1/2 bg-gray-800/90 backdrop-blur-sm text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap border border-white/10 pointer-events-none z-50",children:"Reset Layout"})]}),(0,o.jsxs)("button",{className:"p-1.5 rounded bg-white/5 hover:bg-white/10 text-white transition-all duration-200 shadow hover:scale-110 hover:shadow-lg group relative border border-white/10 backdrop-blur-sm pointer-events-auto",onClick:()=>{t.current&&t.current.zoom({level:1.2*t.current.zoom(),renderedPosition:{x:t.current.width()/2,y:t.current.height()/2}})},children:[(0,o.jsx)(M.A,{className:"w-4 h-4"}),(0,o.jsx)("span",{className:"absolute top-full mt-1 left-1/2 -translate-x-1/2 bg-gray-800/90 backdrop-blur-sm text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap border border-white/10 pointer-events-none z-50",children:"Zoom In"})]}),(0,o.jsxs)("button",{className:"p-1.5 rounded bg-white/5 hover:bg-white/10 text-white transition-all duration-200 shadow hover:scale-110 hover:shadow-lg group relative border border-white/10 backdrop-blur-sm pointer-events-auto",onClick:()=>{t.current&&t.current.zoom({level:t.current.zoom()/1.2,renderedPosition:{x:t.current.width()/2,y:t.current.height()/2}})},children:[(0,o.jsx)(D.A,{className:"w-4 h-4"}),(0,o.jsx)("span",{className:"absolute top-full mt-1 left-1/2 -translate-x-1/2 bg-gray-800/90 backdrop-blur-sm text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap border border-white/10 pointer-events-none z-50",children:"Zoom Out"})]}),(0,o.jsxs)("button",{className:"p-1.5 rounded bg-white/5 hover:bg-white/10 text-white transition-all duration-200 shadow hover:scale-110 hover:shadow-lg group relative border border-white/10 backdrop-blur-sm pointer-events-auto",onClick:()=>{if(t.current){let e=t.current.png({full:!0,scale:2,bg:"#1A1A1A"}),a=document.createElement("a");a.href=e,a.download="causality-graph.png",a.click()}},children:[(0,o.jsx)(F.A,{className:"w-4 h-4"}),(0,o.jsx)("span",{className:"absolute top-full mt-1 left-1 -translate-x-1/2 bg-gray-800/90 backdrop-blur-sm text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200  border border-white/10 pointer-events-none z-50",children:"Download as Image"})]})]}),(0,o.jsxs)("div",{className:"absolute top-4 left-4 z-10",children:[(0,o.jsx)("h3",{className:"font-semibold mb-2 text-white",children:"Causality Graph"}),(0,o.jsx)("p",{className:"text-gray-400 text-xs",children:"Hover over nodes to see connections"})]}),(0,o.jsxs)("div",{className:"w-full h-full pointer-events-auto",children:[!n&&h&&(0,o.jsx)(z,{elements:u,style:{width:"100%",height:"100%"},cy:e=>{if(!e)return;t.current=e,e.on("mouseover","node",function(e){e.target.addClass("hover")}),e.on("mouseout","node",function(e){e.target.removeClass("hover")}),e.on("mouseover","edge",function(e){let t=e.target;t.data("enabled")&&(t.addClass("hover"),d(t))}),e.on("mouseout","edge",function(e){e.target.removeClass("hover")}),e.on("tap","node",function(t){let a=t.target;console.log("Tapped node:",a.id()),e.elements().addClass("dimmed").removeClass("highlighted"),a.neighborhood().add(a).removeClass("dimmed").addClass("highlighted"),a.connectedEdges().forEach(e=>{e.data("enabled")&&setTimeout(()=>{d(e)},5*Math.random()*100)})}),e.on("tap",function(t){t.target===e&&e.elements().removeClass("highlighted dimmed")});let a=document.createElement("style");a.textContent="\n      .edge-particle {\n        pointer-events: none;\n        will-change: transform, opacity;\n      }\n    ",document.head.appendChild(a)},stylesheet:P,layout:W,userZoomingEnabled:!0,userPanningEnabled:!0,boxSelectionEnabled:!0}),!n&&!h&&(0,o.jsx)("div",{className:"flex items-center justify-center h-full text-gray-400 text-base",children:"No causality graph available for this query."})]})]})};var O=a(90792),V=a(66766),G=a(87869),K=a(60760);let _=()=>{let{messages:e,externalContexts:t,setSelectedExternalContexts:a}=(0,b.Y)(),[n,l]=(0,r.useState)([]),[s,i]=(0,r.useState)(new Set),[c,d]=(0,r.useState)(0);(0,r.useEffect)(()=>{let o=[];l(o=t&&t.length>0?t:Array.from(new Map((o=e.filter(e=>"ai"===e.type).flatMap(e=>(e.content.match(/(https?:\/\/[^\s]+)/g)||[]).map((t,a)=>{try{let o=new URL(t);return{id:"".concat(void 0!==e.id?e.id:a,"-").concat(t),url:t,title:o.hostname,favicon:"https://www.google.com/s2/favicons?domain=".concat(o.hostname),type:t.includes(".pdf")||t.includes("article")?"article":"website",isSelected:!0}}catch(e){return null}}).filter(Boolean))).map(e=>[e.url,e])).values())),i(new Set(o.map(e=>e.id))),d(0),a(o)},[e,t]),(0,r.useEffect)(()=>{if(0===n.length)return;d(0);let e=setTimeout(()=>{d(1)},100);return()=>clearTimeout(e)},[n]),(0,r.useEffect)(()=>{if(0===n.length||0===c||c>=n.length)return;let e=setTimeout(()=>{d(c+1)},220);return()=>clearTimeout(e)},[c,n.length]),(0,r.useEffect)(()=>{a(n.filter(e=>s.has(e.id)))},[s]);let u=e=>{i(t=>{let a=new Set(t);return a.has(e)?a.delete(e):a.add(e),a})},h={hidden:{opacity:0,y:24},visible:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}}};return(0,o.jsx)("div",{className:"h-full flex flex-col",children:(0,o.jsxs)("div",{className:"glass-card flex-1 p-2 flex flex-col overflow-hidden m-1 ",children:[(0,o.jsx)("h3",{className:"text-lg font-inknut text-kairosGreen px-2 pt-2 mb-2",children:"Context"}),0===n.length?(0,o.jsx)("div",{className:" flex items-center justify-center",children:(0,o.jsx)("span",{className:"text-gray-400",children:"No context sources yet."})}):(0,o.jsx)(G.P.div,{className:"flex-1 overflow-y-auto space-y-2 pr-1 custom-scrollbar",variants:{visible:{transition:{staggerChildren:.18}},hidden:{}},initial:"hidden",animate:"visible",children:(0,o.jsx)(K.N,{children:n.slice(0,c).map(e=>(0,o.jsxs)(G.P.div,{variants:h,initial:"hidden",animate:"visible",exit:"hidden",layout:!0,className:"flex items-center space-x-3 p-2 rounded-md hover:bg-white/10 transition-colors",children:[(0,o.jsx)("input",{type:"checkbox",checked:s.has(e.id),onChange:()=>u(e.id),className:"w-4 h-4 rounded border-white/20 bg-white/5 checked:bg-kairosGreen"}),(0,o.jsx)("div",{className:"w-6 h-6 relative",children:(0,o.jsx)(V.default,{src:e.favicon||"/default-favicon.png",alt:"".concat(e.title," favicon"),width:24,height:24,className:"rounded-sm",onError:e=>{e.target.src="/default-favicon.png"}})}),(0,o.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,o.jsx)("p",{className:"text-sm text-white truncate",children:e.title}),(0,o.jsx)("p",{className:"text-xs text-white/60 truncate",children:e.url})]}),(0,o.jsx)("span",{className:"text-xs px-2 py-1 rounded-full bg-white/10 text-white/80",children:e.type})]},e.id))})})]})})},J=e=>{let{markdown:t,onExportPDF:a,onExportPPT:n}=e,[l,s]=(0,r.useState)(!1),i=(0,r.useRef)(null);return(0,r.useEffect)(()=>{function e(e){i.current&&!i.current.contains(e.target)&&s(!1)}return l?document.addEventListener("mousedown",e):document.removeEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[l]),(0,o.jsx)("div",{className:"h-full flex flex-col",children:(0,o.jsxs)(G.P.div,{className:"glass-card flex-1 p-4 flex flex-col overflow-hidden m-1",initial:{opacity:0,y:24},animate:{opacity:1,y:0},transition:{duration:.6,ease:"easeOut"},children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsx)("h3",{className:"text-lg font-inknut text-kairosGreen",children:"\uD83D\uDCA1Insights"}),(0,o.jsxs)("div",{className:"relative",ref:i,children:[(0,o.jsxs)("button",{className:"p-1.5 rounded bg-white/5 hover:bg-white/10 text-white transition-all duration-200 shadow hover:scale-110 hover:shadow-lg group relative border border-white/10 backdrop-blur-sm",onClick:()=>s(e=>!e),children:[(0,o.jsx)(F.A,{className:"h-4 w-4"}),(0,o.jsx)("span",{className:"absolute top-full mt-1 left-1/2 -translate-x-1/2 bg-gray-800/90 backdrop-blur-sm text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap border border-white/10 pointer-events-none",children:"Export"})]}),l&&(0,o.jsxs)(G.P.div,{initial:{opacity:0,y:8},animate:{opacity:1,y:0},exit:{opacity:0,y:8},className:"absolute right-0 mt-2 w-36 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded shadow-lg z-20",children:[(0,o.jsx)("button",{className:"block w-full text-left px-4 py-2 text-sm text-gray-800 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800",onClick:()=>{s(!1),a&&a()},children:"Export PDF"}),(0,o.jsx)("button",{className:"block w-full text-left px-4 py-2 text-sm text-gray-800 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800",onClick:()=>{s(!1),n&&n()},children:"Export PPT"})]})]})]}),(0,o.jsx)("div",{className:"flex-1 overflow-y-auto custom-scrollbar prose prose-invert bg-transparent p-2 rounded",children:(0,o.jsx)(O.oz,{children:t})})]})})},Y=e=>{let{text:t,speed:a=30,onComplete:n}=e,[l,s]=(0,r.useState)(""),[i,c]=(0,r.useState)(0);return(0,r.useEffect)(()=>{if(i<t.length){let e=setTimeout(()=>{s(e=>e+t[i]),c(e=>e+1)},a);return()=>clearTimeout(e)}n&&n()},[i,t,a,n]),(0,r.useEffect)(()=>{s(""),c(0)},[t]),(0,o.jsx)(o.Fragment,{children:l})},U=()=>{let{messages:e,thinking:t,isThinking:a,sendMessage:n,connectionStatus:l}=(0,b.Y)(),i=(0,r.useRef)(null),[c,d]=(0,r.useState)({});return(0,r.useEffect)(()=>{i.current&&(i.current.scrollTop=i.current.scrollHeight)},[e,a]),(0,o.jsx)("div",{className:"h-full flex flex-col",children:(0,o.jsxs)("div",{className:"glass-card flex-1 p-2 flex flex-col overflow-hidden m-2 mt-2",children:[" ",(0,o.jsxs)("div",{ref:i,className:"flex-1 overflow-y-auto mb-2 space-y-2 pr-1 p-1",style:{scrollbarWidth:"thin",scrollbarColor:"rgba(180, 180, 180, 0.1) rgba(0, 0, 0, 0.05)"},children:[e.map((e,t)=>(0,o.jsx)("div",{className:"".concat("user"===e.type?"text-right":"text-left"),children:(0,o.jsx)("div",{className:"inline-block p-1.5 rounded-md max-w-[90%] break-words ".concat("user"===e.type?"bg-gray-300/10 text-white":"bg-transparent/10 text-white"),children:"user"===e.type?(0,o.jsx)("p",{className:"text-sm",children:e.content}):(0,o.jsx)("div",{className:"text-sm prose prose-invert ",children:c[t]?(0,o.jsx)(O.oz,{children:e.content}):(0,o.jsx)(Y,{text:e.content,speed:1,onComplete:()=>d(e=>({...e,[t]:!0}))})})})},t)),a&&(0,o.jsx)("div",{className:"text-left",children:(0,o.jsx)("div",{className:"inline-block p-1.5 rounded-md glass-card max-w-[90%] break-words",children:(0,o.jsxs)("p",{className:"text-gray-300 text-sm",children:[(0,o.jsx)("span",{className:"inline-block mr-1",children:"\uD83E\uDDE0"}),t]})})})]}),(0,o.jsxs)("div",{className:"mt-auto border-t border-white/10 pt-2",children:[(0,o.jsxs)("div",{className:"flex items-center mb-1 px-1",children:[(0,o.jsx)("span",{className:"text-xs text-gray-400 mr-1",children:"Status:"}),"connected"===l&&(0,o.jsx)("span",{className:"text-green-500 text-xs",children:"● Connected"}),"connecting"===l&&(0,o.jsx)("span",{className:"text-yellow-500 text-xs",children:"● Connecting..."}),("disconnected"===l||"error"===l)&&(0,o.jsx)("span",{className:"text-red-500 text-xs",children:"● Disconnected"})]}),(0,o.jsx)(s,{onSend:n,inSidebar:!1})]})]})})},q=()=>(0,o.jsx)("div",{className:"h-full w-full flex flex-col",children:(0,o.jsx)(N,{})}),H=()=>(0,o.jsx)("div",{className:"h-full w-full flex flex-col",children:(0,o.jsx)(B,{})}),Z=e=>{let t=e.getComponent();switch(t){case"chat":return(0,o.jsx)(U,{});case"graph":return(0,o.jsx)(q,{});case"charts":return(0,o.jsx)(H,{});case"context":return(0,o.jsx)(_,{});case"insights":{let{insights:e}=(0,b.Y)();return(0,o.jsx)(J,{markdown:e})}default:return(0,o.jsxs)("div",{children:["Unknown component: ",t]})}};function $(e){let{params:t}=e,a=(0,r.use)(t).id,{connectWithSlug:l,currentSlug:s}=(0,b.Y)(),i=(0,r.useRef)(null),[c,d]=(0,r.useState)(!1);(0,r.useEffect)(()=>{if(console.log("Chat page mounted with id:",a),a&&"new-chat"!==a&&!c){let e=setTimeout(()=>{console.log("Connecting with slug (delayed):",a),l(a),d(!0)},500);return()=>clearTimeout(e)}},[a,l,c]),(0,r.useEffect)(()=>{s&&s!==a&&c&&(console.log("Current slug changed, updating URL:",s),window.history.replaceState({},"","/chats/".concat(s)))},[s,a,c]);let u={global:{tabEnableClose:!1,tabEnablePopout:!0,tabSetEnableMaximize:!0,tabSetMinWidth:100,tabSetMinHeight:100,splitterSize:2,splitterExtra:2},borders:[],layout:{type:"row",weight:100,children:[{type:"column",weight:30,children:[{type:"tabset",weight:100,children:[{type:"tab",name:"Chat",component:"chat",id:"chat-tab"}]}]},{type:"column",weight:70,children:[{type:"row",weight:100,children:[{type:"column",weight:60,children:[{type:"tabset",weight:50,children:[{type:"tab",name:"Line Chart",component:"graph",id:"graph-tab"}]},{type:"tabset",weight:50,children:[{type:"tab",name:"Causality Graph",component:"charts",id:"charts-tab"}]}]},{type:"column",weight:40,children:[{type:"tabset",weight:50,children:[{type:"tab",name:"Context",component:"context",id:"context-tab"}]},{type:"tabset",weight:50,children:[{type:"tab",name:"Insights",component:"insights",id:"insights-tab"}]}]}]}]}]}},[h,f]=(0,r.useState)(()=>{{let e=localStorage.getItem("chatLayoutV2");if(e)try{return n.Kx.fromJson(JSON.parse(e))}catch(e){console.error("Failed to parse saved layout:",e),localStorage.removeItem("chatLayoutV2")}}return n.Kx.fromJson(u)});return(0,o.jsxs)("div",{className:"h-screen w-full overflow-hidden bg-transparent",children:[(0,o.jsx)("button",{type:"button",onClick:()=>{f(n.Kx.fromJson(u)),localStorage.removeItem("chatLayoutV2")},className:"absolute top-2 right-2 z-10 bg-kairosBlue/80 text-white px-3 py-1 rounded text-xs hover:bg-opacity-100 transition-colors glass-card",children:"Reset Layout"}),(0,o.jsx)(n.PE,{ref:i,model:h,factory:Z,onModelChange:()=>{localStorage.setItem("chatLayoutV2",JSON.stringify(h.toJson()))},supportsPopout:!0,popoutURL:"/popout"})]})}},50674:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[430,843,309,480,517,289,110,991,441,684,358],()=>t(34750)),_N_E=e.O()}]);