// components/BackgroundBlobs.tsx
'use client';

import React from 'react';
import { useTheme } from '../contexts/ThemeContext';

interface XYBlobProps {
  x: string;           // X position from left (e.g., "10%", "200px")
  y: string;           // Y position from top (e.g., "20%", "300px")
  size: string;        // Width/height (e.g., "40vw", "300px")
  color: string;       // Main color (e.g., "#3f6bfd")
  opacity?: number;    // 0-1 opacity value
  blur?: string;       // Blur amount (e.g., "80px")
  mirror?: boolean;    // Whether to add mirror effect
}

// XYBlob component for positioning blobs anywhere
function XYBlob({ 
  x, 
  y, 
  size, 
  color, 
  opacity = 0.7, 
  blur = "80px",
  mirror = false
}: XYBlobProps) {
  // Create gradient based on color
  const getGradient = () => {
    const rgba = hexToRgba(color, 0.4);
    return `radial-gradient(circle at 50% 50%, ${color} 0%, ${rgba} 30%, transparent 70%)`;
  };

  // Helper to convert hex to rgba
  const hexToRgba = (hex: string, alpha: number) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r},${g},${b},${alpha})`;
  };

  return (
    <div
      className="pointer-events-none fixed -z-40 rounded-full mix-blend-screen"
      style={{
        left: x,
        top: y,
        width: size,
        height: size,
        opacity: opacity,
        background: getGradient(),
        filter: `blur(${blur})`
      }}
    >
      {mirror && (
        <div 
          className="absolute inset-0 scale-x-[-1]" 
          style={{ background: "inherit" }} 
        />
      )}
    </div>
  );
}

export default function BackgroundBlobs() {
  const { theme } = useTheme();

  // Define colors based on theme
  const blobColors = theme === 'dark'
    ? {
        primary: '#4C85F6',
        secondary: '#BCC5FF',
        accent: '#A020F0'
      }
    : {
        primary: '#c9ada7',
        secondary: '#9a8c98',
        accent: '#4a4e69'
      };

  return (
    <>
      {/* Theme-aware background */}
      <div className="fixed inset-0 -z-50 bg-background overflow-hidden" />

      <XYBlob
        x="-20%"
        y="0%"
        size="40vw"
        color={blobColors.primary}
        opacity={theme === 'dark' ? 0.7 : 0.3}
        blur="150px"
      />

      <XYBlob
        x="80%"
        y="40%"
        size="30vw"
        color={blobColors.secondary}
        opacity={theme === 'dark' ? 0.6 : 0.25}
        blur="150px"
        mirror={true}
      />

      <XYBlob
        x="22%"
        y="65%"
        size="60vw"
        color={blobColors.accent}
        opacity={theme === 'dark' ? 0.5 : 0.2}
        blur="60px"
      />

      {/* You can add more blobs here as needed */}
    </>
  );
}
