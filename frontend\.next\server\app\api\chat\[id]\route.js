(()=>{var e={};e.id=950,e.ids=[950],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{},97314:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>k,routeModule:()=>u,serverHooks:()=>g,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var o={};s.r(o),s.d(o,{GET:()=>l});var n=s(96559),r=s(48088),a=s(37719),c=s(32190);let i="wss://localhost:8000/ws";class h{constructor(){this.socket=null,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectTimeout=null,this.messageQueue=[],this.onMessageCallback=null,this.onStatusChangeCallback=null,this.notifyStatusChange("disconnected")}connect(e){if(this.socket?.readyState===WebSocket.OPEN)return void console.log("WebSocket already connected");try{this.notifyStatusChange("connecting");let t=e?`${i}/${e}`:i;console.log("Connecting WebSocket to",t),this.socket=new WebSocket(t),this.socket.onopen=this.handleOpen.bind(this),this.socket.onmessage=this.handleMessage.bind(this),this.socket.onclose=this.handleClose.bind(this),this.socket.onerror=this.handleError.bind(this)}catch(e){console.error("Failed to connect to WebSocket:",e),this.notifyStatusChange("error"),this.attemptReconnect()}}disconnect(){this.socket&&(this.socket.close(),this.socket=null),this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.notifyStatusChange("disconnected")}sendMessage(e){console.log("[DEBUG] Sending WebSocket message:",e),this.socket?.readyState===WebSocket.OPEN?this.socket.send(JSON.stringify(e)):(console.log("WebSocket not connected, queueing message"),this.messageQueue.push(e),this.connect())}onMessage(e){this.onMessageCallback=e}onStatusChange(e){this.onStatusChangeCallback=e}handleOpen(){for(console.log("WebSocket connected"),this.reconnectAttempts=0,this.notifyStatusChange("connected");this.messageQueue.length>0;){let e=this.messageQueue.shift();e&&this.sendMessage(e)}}handleMessage(e){try{console.log("[DEBUG] Raw WebSocket message received:",e.data);let t=JSON.parse(e.data);console.log("[DEBUG] Parsed WebSocket message:",t),this.onMessageCallback&&this.onMessageCallback(t)}catch(e){console.error("Error parsing WebSocket message:",e)}}handleClose(e){console.log(`WebSocket closed: ${e.code} ${e.reason}`),this.socket=null,this.notifyStatusChange("disconnected"),this.attemptReconnect()}handleError(e){console.error("WebSocket error:",e),this.notifyStatusChange("error"),this.socket?.close()}attemptReconnect(){if(this.reconnectAttempts>=this.maxReconnectAttempts)return void console.log("Max reconnect attempts reached");let e=Math.min(1e3*Math.pow(2,this.reconnectAttempts),3e4);console.log(`Attempting to reconnect in ${e}ms`),this.reconnectTimeout=setTimeout(()=>{this.reconnectAttempts++,this.connect()},e)}notifyStatusChange(e){this.onStatusChangeCallback&&this.onStatusChangeCallback(e)}}async function l(e,t){let s=t.params.id;try{let e=await fetch(`http://localhost:8000/chat/${s}`);if(!e.ok)return c.NextResponse.json({error:`Failed to fetch chat with ID ${s}`},{status:e.status});let t=await e.json();return c.NextResponse.json(t)}catch(e){return console.error(`Error fetching chat ${s}:`,e),c.NextResponse.json({error:"Internal server error"},{status:500})}}new h;let u=new n.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/chat/[id]/route",pathname:"/api/chat/[id]",filename:"route",bundlePath:"app/api/chat/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\KairosAI\\Kairos_t0\\frontend\\app\\api\\chat\\[id]\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:g}=u;function k(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),o=t.X(0,[447,580],()=>s(97314));module.exports=o})();