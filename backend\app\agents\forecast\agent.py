from typing import Dict, Any
import pandas as pd
from ..base_agent import Agent
from ...utils.forecast_utils import generate_forecast
from ...utils.llm_utils import get_llm_response

class ForecastAgent(Agent):
    """An agent that handles time series forecasting using Nixtla."""
    
    def __init__(self):
        description = "An agent that handles time series forecasting using Nixtla."
        super().__init__("forecast", description)
        
    async def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the input data and generate forecasts.
        
        Args:
            input_data: Dictionary containing input data with DataFrame and target column
            
        Returns:
            Dictionary containing the forecast results
        """
        try:
            df = input_data.get("data")
            target_column = input_data.get("target_column", "sales")
            forecast_horizon = input_data.get("forecast_horizon", 30)
            
            if df is None:
                raise ValueError("No data provided for forecasting")
            
            # Generate forecast using Nixtla
            forecast_results = await generate_forecast(
                data=df,
                target_column=target_column,
                forecast_horizon=forecast_horizon
            )
            
            # Use LLM to analyze the forecast results
            system_prompt = "You are a forecasting expert. Analyze the provided forecast results and provide insights."
            llm_prompt = f"Please analyze these forecast results and provide key insights: {forecast_results.to_dict()}"
            llm_analysis = await get_llm_response(llm_prompt, system_prompt)
            
            return {
                "message": "Successfully generated forecast",
                "status": "success",
                "forecast_results": forecast_results.to_dict(),
                "llm_analysis": llm_analysis,
                "next_agent": "analyzer"
            }
        except Exception as e:
            return {
                "message": f"Error generating forecast: {str(e)}",
                "status": "error",
                "next_agent": None
            } 