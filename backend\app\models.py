from pydantic import BaseModel
from typing import Optional, List, Dict, Any

class Message(BaseModel):
    message: str
    file_path: Optional[str] = None
    slug: Optional[str] = None

class ChatMessage(BaseModel):
    type: str  # 'user' or 'ai'
    content: str
    chart: Optional[Any] = None
    thought: Optional[str] = None
    causality: Optional[Any] = None
    insights: Optional[str] = None
    timestamp: Optional[str] = None
    external_contexts: Optional[List[Any]] = None
    next_agent: Optional[str] = None
    status: Optional[str] = None

class ChatHistory(BaseModel):
    messages: List[ChatMessage] = []