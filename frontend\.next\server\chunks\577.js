"use strict";exports.id=577,exports.ids=[577],exports.modules={56577:(e,t,s)=>{s.d(t,{default:()=>j});var l=s(60687),r=s(43210),a=s(16189);let o=({selectedFile:e,isDragging:t,fileInputRef:s,onDragOver:r,onDragLeave:a,onDrop:o,onBrowseClick:n,onFileInputChange:i,onClose:c,onUpload:x})=>(0,l.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-black/60 backdrop-blur-sm",onClick:c}),(0,l.jsxs)("div",{className:"relative z-10 glass-card w-full max-w-md p-6 border-t-2 border-r-2 border-kairosYellow shadow-[0_0_15px_rgba(255,246,141,0.5)]",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsx)("h3",{className:"text-xl font-medium text-kairosYellow",children:"Upload File"}),(0,l.jsx)("button",{type:"button","aria-label":"Close",onClick:c,className:"text-gray-400 hover:text-kairosYellow",children:(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,l.jsx)("div",{className:`border-2 border-dashed rounded-lg p-8 text-center mb-4 transition-colors ${t?"border-kairosYellow bg-kairosYellow/10":"border-gray-600"}`,onDragOver:r,onDragLeave:a,onDrop:o,children:e?(0,l.jsxs)("div",{className:"flex flex-col items-center",children:[(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-kairosYellow mb-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,l.jsx)("p",{className:"text-gray-300 mb-1",children:e.name}),(0,l.jsxs)("p",{className:"text-gray-500 text-sm",children:[(e.size/1024).toFixed(2)," KB"]})]}):(0,l.jsxs)("div",{className:"flex flex-col items-center",children:[(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-gray-500 mb-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})}),(0,l.jsx)("p",{className:"text-gray-300 mb-1",children:"Drag & drop your file here"}),(0,l.jsx)("p",{className:"text-gray-500 text-sm",children:"or"}),(0,l.jsx)("button",{onClick:n,className:"mt-2 px-4 py-2 text-sm text-kairosYellow border border-kairosYellow rounded hover:bg-kairosYellow/10 transition-colors",children:"Browse Files"}),(0,l.jsx)("input",{"aria-label":"File upload",title:"Select a file to upload",type:"file",ref:s,onChange:i,className:"hidden"})]})}),(0,l.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,l.jsx)("button",{onClick:c,className:"px-4 py-2 border border-gray-600 text-gray-300 rounded hover:bg-black/30 transition-colors",children:"Cancel"}),(0,l.jsx)("button",{onClick:x,disabled:!e,style:e?{backgroundColor:"#46fcb0",color:"black"}:void 0,className:`px-4 py-2 rounded font-medium ${!e?"bg-gray-700 text-gray-400 cursor-not-allowed":""} transition-colors`,children:"Upload"})]})]})]}),n=({formFields:e,onFieldChange:t,onClose:s,onSubmit:r,inSidebar:a=!1})=>(0,l.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-black/60 backdrop-blur-sm",onClick:s}),(0,l.jsxs)("div",{className:"relative z-10 glass-card w-full max-w-md p-6 border-t-2 border-r-2 border-kairosYellow shadow-[0_0_15px_rgba(255,246,141,0.5)]",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsx)("h3",{className:"text-xl font-medium text-kairosYellow",children:"Key-Value Pairs"}),(0,l.jsx)("button",{type:"button","aria-label":"Close",onClick:s,className:"text-gray-400 hover:text-kairosYellow",children:(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,l.jsx)("div",{className:"space-y-4",children:e.map((e,s)=>(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("label",{className:"block text-gray-400 text-xs mb-1",children:"Key"}),(0,l.jsx)("input",{type:"text",value:e.key,onChange:e=>t(s,"key",e.target.value),className:"w-full bg-gray-800/50 border border-gray-700 rounded px-3 py-2 text-sm text-gray-200 placeholder-gray-500 focus:outline-none focus:border-kairosYellow",placeholder:"Enter key"})]}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("label",{className:"block text-gray-400 text-xs mb-1",children:"Value"}),(0,l.jsx)("input",{type:"text",value:e.value,onChange:e=>t(s,"value",e.target.value),className:"w-full bg-gray-800/50 border border-gray-700 rounded px-3 py-2 text-sm text-gray-200 placeholder-gray-500 focus:outline-none focus:border-kairosYellow",placeholder:"Enter value"})]})]},s))}),(0,l.jsxs)("div",{className:"flex justify-end mt-6 space-x-3",children:[(0,l.jsx)("button",{type:"button",onClick:s,className:"px-4 py-2 border border-gray-600 text-gray-300 rounded hover:bg-black/30 transition-colors",children:"Cancel"}),(0,l.jsx)("button",{type:"button",onClick:r,className:"px-4 py-2 rounded font-medium bg-kairosGreen text-black hover:bg-opacity-80 transition-colors",children:"Submit"})]})]})]});function i({uploadingFiles:e,onRemoveFile:t}){if(0===e.length)return null;let s=e=>{let t=e.split(".").pop()?.toLowerCase();return["jpg","jpeg","png","gif","webp"].includes(t||"")?(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,l.jsx)("rect",{x:"3",y:"3",width:"18",height:"18",rx:"2",ry:"2"}),(0,l.jsx)("circle",{cx:"8.5",cy:"8.5",r:"1.5"}),(0,l.jsx)("polyline",{points:"21 15 16 10 5 21"})]}):["pdf"].includes(t||"")?(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,l.jsx)("path",{d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"}),(0,l.jsx)("polyline",{points:"14 2 14 8 20 8"}),(0,l.jsx)("line",{x1:"16",y1:"13",x2:"8",y2:"13"}),(0,l.jsx)("line",{x1:"16",y1:"17",x2:"8",y2:"17"}),(0,l.jsx)("polyline",{points:"10 9 9 9 8 9"})]}):(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,l.jsx)("path",{d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"}),(0,l.jsx)("polyline",{points:"14 2 14 8 20 8"})]})};return(0,l.jsx)("div",{className:"mt-2 flex flex-wrap gap-2",children:e.map((e,r)=>(0,l.jsxs)("div",{className:"flex items-center bg-gray-800/50 rounded px-2 py-1 text-xs",children:[(0,l.jsx)("span",{className:"text-kairosYellow mr-1",children:s(e.file.name)}),(0,l.jsx)("span",{className:"text-gray-300 truncate max-w-[100px]",children:e.file.name}),e.complete?(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1 text-kairosGreen",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:(0,l.jsx)("path",{d:"M20 6L9 17l-5-5"})}):(0,l.jsx)("div",{className:"ml-2 w-16 h-1 bg-gray-700 rounded-full overflow-hidden",children:(0,l.jsx)("div",{className:"h-full bg-kairosGreen rounded-full transition-all duration-300",style:{width:`${e.progress}%`}})}),(0,l.jsx)("button",{onClick:()=>t(e.file.name),className:"ml-1 text-gray-400 hover:text-gray-200 transition-colors","aria-label":"Remove file",children:(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3.5 w-3.5",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,l.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,l.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]},r))})}var c=s(15400);function x(){let{chartSelection:e,setChartSelection:t}=(0,c.Y)();if(!e.point&&!e.range)return null;let s=()=>{t({})};return(0,l.jsxs)("div",{className:"mt-2 flex flex-wrap gap-2",children:[e.point&&(0,l.jsxs)("div",{className:"flex items-center bg-gray-800/50 rounded px-2 py-1 text-xs",children:[(0,l.jsx)("span",{className:"text-kairosYellow mr-1",children:(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,l.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,l.jsx)("circle",{cx:"12",cy:"12",r:"3"})]})}),(0,l.jsxs)("span",{className:"text-gray-300 truncate max-w-[150px]",children:["Point: ",e.point.name," (",e.point.value,")"]}),(0,l.jsx)("button",{type:"button",onClick:s,className:"ml-1 text-gray-400 hover:text-gray-200 transition-colors","aria-label":"Clear selection",children:(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3.5 w-3.5",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,l.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,l.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),e.range&&(0,l.jsxs)("div",{className:"flex items-center bg-gray-800/50 rounded px-2 py-1 text-xs",children:[(0,l.jsx)("span",{className:"text-kairosYellow mr-1",children:(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:(0,l.jsx)("path",{d:"M3 6h18M3 12h18M3 18h18"})})}),(0,l.jsx)("span",{className:"text-gray-300 truncate max-w-[150px]",children:e.range.start===e.range.end?`Point: ${e.range.start}`:`Range: ${e.range.start} to ${e.range.end}`}),(0,l.jsx)("button",{type:"button",onClick:s,className:"ml-1 text-gray-400 hover:text-gray-200 transition-colors","aria-label":"Clear selection",children:(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3.5 w-3.5",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,l.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,l.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]})]})}let d=({buttonRef:e,menuRef:t,showMenu:s,onToggleMenu:r,onFileUpload:a,onServerConnection:o,direction:n="top",inSidebar:i=!1})=>(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("button",{ref:e,className:"w-7 h-7 rounded-full border border-gray-600 flex items-center justify-center text-gray-400 hover:text-gray-200 hover:bg-gray-800 transition-colors","aria-label":"Add attachment",onClick:r,children:(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,l.jsx)("line",{x1:"12",y1:"5",x2:"12",y2:"19"}),(0,l.jsx)("line",{x1:"5",y1:"12",x2:"19",y2:"12"})]})}),s&&(0,l.jsxs)("div",{ref:t,className:`absolute ${"top"===n?"bottom-full mb-2":"top-full mt-2"} left-0 p-2 w-40 rounded-md z-10 ${i?"bg-gray-900 border border-gray-700":"bg-gray-800 border border-gray-700 shadow-lg"}`,children:[(0,l.jsxs)("button",{onClick:a,className:"w-full text-left px-3 py-2 text-sm text-gray-300 hover:bg-white/10 rounded transition-colors flex items-center",children:[(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,l.jsx)("path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}),(0,l.jsx)("polyline",{points:"17 8 12 3 7 8"}),(0,l.jsx)("line",{x1:"12",y1:"3",x2:"12",y2:"15"})]}),"Upload File"]}),(0,l.jsxs)("button",{onClick:o,className:"w-full text-left px-3 py-2 text-sm text-gray-300 hover:bg-white/10 rounded transition-colors flex items-center",children:[(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,l.jsx)("rect",{x:"2",y:"3",width:"20",height:"14",rx:"2",ry:"2"}),(0,l.jsx)("line",{x1:"8",y1:"21",x2:"16",y2:"21"}),(0,l.jsx)("line",{x1:"12",y1:"17",x2:"12",y2:"21"})]}),"Connect Server"]})]})]});var h=s(49384),u=s(82348);function m(...e){return(0,u.QP)((0,h.$)(e))}var g=s(76020),p=s(72777);let w=({className:e})=>(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:m("w-6 h-6 ",e),children:(0,l.jsx)("path",{d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})}),y=({className:e})=>(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",className:m("w-6 h-6 ",e),children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z",clipRule:"evenodd"})}),f=({loadingStates:e,value:t=0})=>(0,l.jsx)("div",{className:"flex relative justify-start max-w-xl mx-auto flex-col mt-40",children:e.map((e,s)=>{let r=Math.max(1-.2*Math.abs(s-t),0);return(0,l.jsxs)(g.P.div,{className:m("text-left flex gap-2 mb-4"),initial:{opacity:0,y:-(40*t)},animate:{opacity:r,y:-(40*t)},transition:{duration:.5},children:[(0,l.jsxs)("div",{children:[s>t&&(0,l.jsx)(w,{className:"text-black dark:text-white"}),s<=t&&(0,l.jsx)(y,{className:m("text-black dark:text-white",t===s&&"text-black dark:text-lime-500 opacity-100")})]}),(0,l.jsx)("span",{className:m("text-black dark:text-white",t===s&&"text-black dark:text-lime-500 opacity-100"),children:e.text})]},s)})}),v=({loadingStates:e,loading:t,duration:s=2e3,loop:a=!0})=>{let[o,n]=(0,r.useState)(0),[i,c]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{if(t)c(!0);else if(i){let e=setTimeout(()=>{c(!1),n(0)},1e3);return()=>clearTimeout(e)}else n(0)},[t,i]),(0,r.useEffect)(()=>{if(!i)return;let t=setTimeout(()=>{n(t=>a||t!==e.length-1?a?t===e.length-1?0:t+1:Math.min(t+1,e.length-1):t)},s);return()=>clearTimeout(t)},[o,i,a,e.length,s]),(0,l.jsx)(p.N,{mode:"wait",children:(t||i)&&(0,l.jsxs)(g.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0,transition:{duration:.5}},className:"w-full h-full fixed inset-0 z-[100] flex items-center justify-center backdrop-blur-2xl",children:[(0,l.jsx)("div",{className:"h-96 relative",children:(0,l.jsx)(f,{value:o,loadingStates:e})}),(0,l.jsx)("div",{className:"bg-gradient-to-t inset-x-0 z-20 bottom-0 bg-white dark:bg-black h-full absolute [mask-image:radial-gradient(900px_at_center,transparent_30%,white)]"})]})})};function j({onSend:e=e=>{console.log("Default onSend handler:",e)},placeholder:t="let's explore, what do you want to know?",navigateTo:s,skipNavigation:h=!1,autoGrow:u=!1,maxHeight:m="120px",inSidebar:g=!1}){let p=(0,a.useRouter)(),[w,y]=(0,r.useState)(""),f=(0,r.useRef)(null),{uploadFile:j,uploadedFiles:b,removeUploadedFile:k,clearUploadedFiles:N,chartSelection:C,setChartSelection:L,currentSlug:M,sendMessage:S}=(0,c.Y)(),B=()=>{if(u&&f.current){f.current.style.height="0px";let e=Math.max(Math.min(f.current.scrollHeight,parseInt(m)),24);f.current.style.height=`${e}px`}},[W,Y]=(0,r.useState)(!1),[F,D]=(0,r.useState)(!1),[z,R]=(0,r.useState)(!1),[$,_]=(0,r.useState)(!1),T=(0,r.useRef)(null),P=(0,r.useRef)(null),E=(0,r.useRef)(null),[K,U]=(0,r.useState)(!1),[I,G]=(0,r.useState)(null),[H,V]=(0,r.useState)([]),[Z,A]=(0,r.useState)([{key:"",value:""},{key:"",value:""},{key:"",value:""},{key:"",value:""}]),O=()=>{if(w.trim()){if(h){console.log("Processing message in chat panel:",w),e&&e(w),y("");return}console.log("Send button clicked on main page, showing loader"),_(!0),y(""),setTimeout(()=>{S(w),setTimeout(()=>{_(!1),console.log("Loader complete, currentSlug:",M),M&&("/"===window.location.pathname||"/chat"===window.location.pathname)&&(console.log("Manually redirecting to chat page with slug:",M),p.push(`/chats/${M}`))},3e3)},500)}},Q=e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),O())},q=async()=>{if(I){V(e=>[...e,{file:I,progress:0,complete:!1}]),D(!1);try{let e=0,t=setInterval(()=>{(e+=5)<=90&&V(t=>t.map(t=>t.file.name===I.name?{...t,progress:e}:t))},200),s=await j(I);clearInterval(t),V(e=>e.map(e=>e.file.name===I.name?{...e,progress:100,complete:!0}:e)),console.log("File uploaded successfully:",s),G(null)}catch(e){console.error("Error uploading file:",e),V(e=>e.map(e=>e.file.name===I.name?{...e,progress:0,complete:!1}:e)),alert(`Failed to upload file: ${e instanceof Error?e.message:"Unknown error"}`)}finally{G(null)}}};return(0,l.jsxs)("div",{className:"relative w-full max-w-2xl",children:[(0,l.jsx)(v,{loadingStates:[{text:"Processing your message"},{text:"Analyzing content"},{text:"Searching knowledge base"},{text:"Generating response"},{text:"Finalizing results"}],loading:$,duration:2e3}),(0,l.jsxs)("div",{className:"glass-card px-5 py-4 flex flex-col border-t-2 border-r-2 border-kairosYellow shadow-[0_0_15px_rgba(255,246,141,0.5)]",children:[(0,l.jsxs)("div",{className:"flex items-center w-full",children:[u?(0,l.jsx)("textarea",{ref:f,value:w,onChange:e=>y(e.target.value),onKeyDown:Q,onFocus:B,onBlur:B,placeholder:t,className:"flex-grow bg-transparent outline-none text-gray-200 placeholder-gray-500 text-sm resize-none overflow-y-auto min-h-[24px]",style:{maxHeight:m,scrollbarWidth:"thin",scrollbarColor:"rgba(180, 180, 180, 0.1) rgba(0, 0, 0, 0.05)"},rows:1}):(0,l.jsx)("input",{type:"text",value:w,onChange:e=>y(e.target.value),onKeyDown:Q,placeholder:t,className:"flex-grow bg-transparent outline-none text-gray-200 placeholder-gray-500 text-sm"}),(0,l.jsx)("button",{type:"button",onClick:O,className:"ml-3 text-red-400 hover:text-red-300 transition-colors","aria-label":"Send message",children:(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,l.jsx)("path",{d:"M22 2L11 13"}),(0,l.jsx)("path",{d:"M22 2L15 22L11 13L2 9L22 2Z"})]})})]}),(0,l.jsx)(i,{uploadingFiles:H,onRemoveFile:e=>{V(t=>t.filter(t=>t.file.name!==e)),k(e)}}),(0,l.jsx)(x,{}),(0,l.jsxs)("div",{className:"flex items-center mt-3",children:[(0,l.jsx)(d,{buttonRef:P,menuRef:T,showMenu:W,onToggleMenu:()=>Y(!W),onFileUpload:()=>{D(!0),Y(!1)},onServerConnection:()=>{console.log("Server connection clicked"),Y(!1)},direction:"top",inSidebar:g}),(0,l.jsx)("button",{type:"button",className:"w-7 h-7 rounded-full border border-gray-600 flex items-center justify-center text-gray-400 hover:text-gray-200 hover:bg-gray-800 transition-colors ml-2","aria-label":"Options",onClick:()=>{R(!0)},children:(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,l.jsx)("line",{x1:"5",y1:"12",x2:"19",y2:"12"}),(0,l.jsx)("line",{x1:"5",y1:"8",x2:"19",y2:"8"}),(0,l.jsx)("line",{x1:"5",y1:"16",x2:"19",y2:"16"})]})})]})]}),F&&(0,l.jsx)(o,{selectedFile:I,isDragging:K,fileInputRef:E,onDragOver:e=>{e.preventDefault(),U(!0)},onDragLeave:e=>{e.preventDefault(),U(!1)},onDrop:e=>{if(e.preventDefault(),U(!1),e.dataTransfer.files&&e.dataTransfer.files.length>0){let t=e.dataTransfer.files[0];G(t),console.log("File dropped:",t.name)}},onBrowseClick:()=>{E.current&&E.current.click()},onFileInputChange:e=>{if(e.target.files&&e.target.files.length>0){let t=e.target.files[0];G(t),console.log("File selected:",t.name)}},onClose:()=>{G(null),D(!1)},onUpload:q}),z&&(0,l.jsx)(n,{formFields:Z,onFieldChange:(e,t,s)=>{let l=[...Z];l[e][t]=s,A(l)},onClose:()=>{R(!1)},onSubmit:()=>{console.log("Form submitted:",Z),R(!1)},inSidebar:g})]})}}};