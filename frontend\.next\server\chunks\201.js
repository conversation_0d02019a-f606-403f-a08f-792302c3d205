exports.id=201,exports.ids=[201],exports.modules={5949:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},8359:(e,t,s)=>{"use strict";s.d(t,{D:()=>i,N:()=>a});var n=s(60687),o=s(43210);let r=(0,o.createContext)(void 0);function i(){let e=(0,o.useContext)(r);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}function a({children:e}){let[t,s]=(0,o.useState)("light"),i=e=>{s(e),localStorage.setItem("kairos-theme",e)};return(0,n.jsx)(r.Provider,{value:{theme:t,toggleTheme:()=>{i("light"===t?"dark":"light")},setTheme:i},children:e})}},15400:(e,t,s)=>{"use strict";s.d(t,{k:()=>d,Y:()=>u});var n=s(60687),o=s(43210),r=s(16189);let i="wss://localhost:8000/ws";class a{constructor(){this.socket=null,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectTimeout=null,this.messageQueue=[],this.onMessageCallback=null,this.onStatusChangeCallback=null,this.notifyStatusChange("disconnected")}connect(e){if(this.socket?.readyState===WebSocket.OPEN)return void console.log("WebSocket already connected");try{this.notifyStatusChange("connecting");let t=e?`${i}/${e}`:i;console.log("Connecting WebSocket to",t),this.socket=new WebSocket(t),this.socket.onopen=this.handleOpen.bind(this),this.socket.onmessage=this.handleMessage.bind(this),this.socket.onclose=this.handleClose.bind(this),this.socket.onerror=this.handleError.bind(this)}catch(e){console.error("Failed to connect to WebSocket:",e),this.notifyStatusChange("error"),this.attemptReconnect()}}disconnect(){this.socket&&(this.socket.close(),this.socket=null),this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.notifyStatusChange("disconnected")}sendMessage(e){console.log("[DEBUG] Sending WebSocket message:",e),this.socket?.readyState===WebSocket.OPEN?this.socket.send(JSON.stringify(e)):(console.log("WebSocket not connected, queueing message"),this.messageQueue.push(e),this.connect())}onMessage(e){this.onMessageCallback=e}onStatusChange(e){this.onStatusChangeCallback=e}handleOpen(){for(console.log("WebSocket connected"),this.reconnectAttempts=0,this.notifyStatusChange("connected");this.messageQueue.length>0;){let e=this.messageQueue.shift();e&&this.sendMessage(e)}}handleMessage(e){try{console.log("[DEBUG] Raw WebSocket message received:",e.data);let t=JSON.parse(e.data);console.log("[DEBUG] Parsed WebSocket message:",t),this.onMessageCallback&&this.onMessageCallback(t)}catch(e){console.error("Error parsing WebSocket message:",e)}}handleClose(e){console.log(`WebSocket closed: ${e.code} ${e.reason}`),this.socket=null,this.notifyStatusChange("disconnected"),this.attemptReconnect()}handleError(e){console.error("WebSocket error:",e),this.notifyStatusChange("error"),this.socket?.close()}attemptReconnect(){if(this.reconnectAttempts>=this.maxReconnectAttempts)return void console.log("Max reconnect attempts reached");let e=Math.min(1e3*Math.pow(2,this.reconnectAttempts),3e4);console.log(`Attempting to reconnect in ${e}ms`),this.reconnectTimeout=setTimeout(()=>{this.reconnectAttempts++,this.connect()},e)}notifyStatusChange(e){this.onStatusChangeCallback&&this.onStatusChangeCallback(e)}}let l=new a;async function c(e){let t=new FormData;t.append("file",e);let s=await fetch("http://localhost:8000/upload",{method:"POST",body:t});if(!s.ok)throw Error(`Upload failed: ${s.statusText}`);return s.json()}let h=(0,o.createContext)(void 0);function d({children:e}){(0,r.useRouter)();let[t,s]=(0,o.useState)([]),[i,a]=(0,o.useState)(""),[d,u]=(0,o.useState)(!1),[g,m]=(0,o.useState)("disconnected"),[p,b]=(0,o.useState)(null),[k,f]=(0,o.useState)(null),[v,x]=(0,o.useState)({}),[S,y]=(0,o.useState)([]),[C,P]=(0,o.useState)([]),[w,j]=(0,o.useState)([]),[A,W]=(0,o.useState)(""),[E,M]=(0,o.useState)(null),$=async e=>{try{let t=await c(e);return t.filename&&y(s=>[...s,{name:e.name,path:t.filename}]),t.filename}catch(e){throw console.error("Error uploading file:",e),e}};return(0,n.jsx)(h.Provider,{value:{messages:t,thinking:i,isThinking:d,connectionStatus:g,chartData:p,causalityData:k,chartSelection:v,uploadedFiles:S,externalContexts:C,selectedExternalContexts:w,insights:A,currentSlug:E,sendMessage:e=>{s(t=>[...t,{type:"user",content:e}]),u(!0),a("Analyzing query...");let t={message:e};S.length>0&&(t.files_path=S.map(e=>e.path));let n={};(p||v?.point||v?.range)&&(n.chart={data:p},v&&(n.chart.selection=v),console.log("[DEBUG] Adding chart context to message:",n.chart)),Object.keys(n).length>0&&(t.model_context=n),w.length>0&&(t.external_contexts=w),l.sendMessage(t)},uploadFile:$,removeUploadedFile:e=>{y(t=>t.filter(t=>t.name!==e))},clearMessages:()=>{s([]),a(""),u(!1),b(null)},clearUploadedFiles:()=>{y([])},setChartSelection:x,setSelectedExternalContexts:j,connectWithSlug:e=>{console.log("[DEBUG] Connecting with slug:",e),E!==e?(M(e),l.connect(e)):console.log("[DEBUG] Already connected with slug:",e)}},children:e})}function u(){let e=(0,o.useContext)(h);if(void 0===e)throw Error("useChat must be used within a ChatProvider");return e}},24509:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},43966:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var n=s(60687),o=s(42832),r=s.n(o),i=s(62046),a=s.n(i);s(76080);var l=s(16189);s(43210);var c=s(8359);function h({x:e,y:t,size:s,color:o,opacity:r=.7,blur:i="80px",mirror:a=!1}){let l=(e,t)=>{let s=parseInt(e.slice(1,3),16),n=parseInt(e.slice(3,5),16),o=parseInt(e.slice(5,7),16);return`rgba(${s},${n},${o},${t})`};return(0,n.jsx)("div",{className:"pointer-events-none fixed -z-40 rounded-full mix-blend-screen",style:{left:e,top:t,width:s,height:s,opacity:r,background:(()=>{let e=l(o,.4);return`radial-gradient(circle at 50% 50%, ${o} 0%, ${e} 30%, transparent 70%)`})(),filter:`blur(${i})`},children:a&&(0,n.jsx)("div",{className:"absolute inset-0 scale-x-[-1]",style:{background:"inherit"}})})}function d(){let{theme:e}=(0,c.D)(),t="dark"===e?{primary:"#4C85F6",secondary:"#BCC5FF",accent:"#A020F0"}:{primary:"#c9ada7",secondary:"#9a8c98",accent:"#4a4e69"};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"fixed inset-0 -z-50 bg-background overflow-hidden"}),(0,n.jsx)(h,{x:"-20%",y:"0%",size:"40vw",color:t.primary,opacity:"dark"===e?.7:.3,blur:"150px"}),(0,n.jsx)(h,{x:"80%",y:"40%",size:"30vw",color:t.secondary,opacity:"dark"===e?.6:.25,blur:"150px",mirror:!0}),(0,n.jsx)(h,{x:"22%",y:"65%",size:"60vw",color:t.accent,opacity:"dark"===e?.5:.2,blur:"60px"})]})}var u=s(15400);function g({children:e}){let t=(0,l.usePathname)(),s=t?.startsWith("/chats");return(0,n.jsxs)("html",{lang:"en",className:`${r().variable} ${a().variable}`,suppressHydrationWarning:!0,children:[(0,n.jsxs)("head",{children:[(0,n.jsx)("title",{children:"Kairos AI"}),(0,n.jsx)("meta",{name:"description",content:"Kairos AI - Time-aware intelligence for forecasting and decision-making"}),(0,n.jsx)("link",{rel:"icon",href:"/kairoslogo.png"}),(0,n.jsx)("link",{rel:"apple-touch-icon",href:"/kairoslogo.png"})]}),(0,n.jsx)("body",{children:(0,n.jsxs)(c.N,{children:[!s&&(0,n.jsx)(d,{}),(0,n.jsx)(u.k,{children:e})]})})]})}},58014:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});let n=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\KairosAI\\\\Kairos_t0\\\\frontend\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\KairosAI\\Kairos_t0\\frontend\\app\\layout.tsx","default")},68189:(e,t,s)=>{Promise.resolve().then(s.bind(s,43966))},76080:()=>{},98045:(e,t,s)=>{Promise.resolve().then(s.bind(s,58014))}};