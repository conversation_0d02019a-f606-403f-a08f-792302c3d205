import requests
import json
import argparse
from pathlib import Path
from typing import Dict, Any
import logging
from app.utils.logging_utils import log_agent_interaction

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BackendClient:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        
    def process_data(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Send data to the backend for processing.
        
        Args:
            message (Dict[str, Any]): Message to process
            
        Returns:
            Dict[str, Any]: Processed result
        """
        try:
            response = requests.post(
                f"{self.base_url}/process",
                json=message
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error processing data: {str(e)}")
            raise
            
    def upload_file(self, file_path: str) -> Dict[str, Any]:
        """
        Upload a file to the backend.
        
        Args:
            file_path (str): Path to the file to upload
            
        Returns:
            Dict[str, Any]: Upload result
        """
        try:
            with open(file_path, 'rb') as f:
                files = {'file': (Path(file_path).name, f)}
                response = requests.post(
                    f"{self.base_url}/upload",
                    files=files
                )
                response.raise_for_status()
                return response.json()
        except (requests.exceptions.RequestException, IOError) as e:
            logger.error(f"Error uploading file: {str(e)}")
            raise

def main():
    parser = argparse.ArgumentParser(description='Interact with the backend API')
    parser.add_argument('--url', default='http://localhost:8000', help='Backend API URL')
    parser.add_argument('--action', choices=['process', 'upload'], required=True, help='Action to perform')
    parser.add_argument('--message', help='Message to process (JSON string)')
    parser.add_argument('--file', help='File to upload')
    
    args = parser.parse_args()
    client = BackendClient(args.url)
    
    try:
        if args.action == 'process':
            if not args.message:
                raise ValueError("Message is required for process action")
            
            message = json.loads(args.message)
            result = client.process_data(message)
            
            # Log the interaction
            log_agent_interaction(
                agent_name="client",
                input_data=message,
                output_data=result
            )
            
            print("Processing Result:")
            print(json.dumps(result, indent=2))
            
        elif args.action == 'upload':
            if not args.file:
                raise ValueError("File path is required for upload action")
                
            result = client.upload_file(args.file)
            print("Upload Result:")
            print(json.dumps(result, indent=2))
            
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        raise

if __name__ == "__main__":
    main() 