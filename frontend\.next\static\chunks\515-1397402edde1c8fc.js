(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[515],{4257:e=>{e.exports={style:{fontFamily:"'Space Grotesk', 'Space Grotesk Fallback'",fontStyle:"normal"},className:"__className_6f5d21"}},5500:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(12115);let a=r.forwardRef(function(e,t){let{title:n,titleId:a,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},o),n?r.createElement("title",{id:a},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))})},24823:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(12115);let a=r.forwardRef(function(e,t){let{title:n,titleId:a,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},o),n?r.createElement("title",{id:a},n):null,r.createElement("path",{d:"M12.75 12.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM7.5 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM8.25 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM9.75 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM10.5 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM12 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM12.75 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM14.25 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM15 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM16.5 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM15 12.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM16.5 13.5a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z"}),r.createElement("path",{fillRule:"evenodd",d:"M6.75 2.25A.75.75 0 0 1 7.5 3v1.5h9V3A.75.75 0 0 1 18 3v1.5h.75a3 3 0 0 1 3 3v11.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V7.5a3 3 0 0 1 3-3H6V3a.75.75 0 0 1 .75-.75Zm13.5 9a1.5 1.5 0 0 0-1.5-1.5H5.25a1.5 1.5 0 0 0-1.5 1.5v7.5a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5v-7.5Z",clipRule:"evenodd"}))})},36207:e=>{e.exports={style:{fontFamily:"'Playfair Display', 'Playfair Display Fallback'",fontStyle:"normal"},className:"__className_68cd5c"}},37124:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(12115);let a=r.forwardRef(function(e,t){let{title:n,titleId:a,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},o),n?r.createElement("title",{id:a},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"}))})},43770:function(e,t,n){(function(e,t,n){"use strict";function r(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var a=r(t),o=r(n);function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach(function(t){var r,a;r=t,a=n[t],(r=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(r))in e?Object.defineProperty(e,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[r]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function u(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.includes(n)||({}).propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}var c=["animationData","loop","autoplay","initialSegment","onComplete","onLoopComplete","onEnterFrame","onSegmentStart","onConfigReady","onDataReady","onDataFailed","onLoadedImages","onDOMLoaded","onDestroy","lottieRef","renderer","name","assetsPath","rendererSettings"],f=function(e,t){var r,l=e.animationData,f=e.loop,d=e.autoplay,m=e.initialSegment,p=e.onComplete,y=e.onLoopComplete,v=e.onEnterFrame,g=e.onSegmentStart,h=e.onConfigReady,b=e.onDataReady,w=e.onDataFailed,S=e.onLoadedImages,E=e.onDOMLoaded,A=e.onDestroy;e.lottieRef,e.renderer,e.name,e.assetsPath,e.rendererSettings;var O=u(e,c),j=function(e){if(Array.isArray(e))return e}(r=n.useState(!1))||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],s=!0,u=!1;try{o=(n=n.call(e)).next,!1;for(;!(s=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){u=!0,a=e}finally{try{if(!s&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw a}}return l}}(r,2)||function(e,t){if(e){if("string"==typeof e)return i(e,2);var n=({}).toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?i(e,t):void 0}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),k=j[0],L=j[1],M=n.useRef(),R=n.useRef(null),P=function(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(R.current){null==(t=M.current)||t.destroy();var r=s(s(s({},e),n),{},{container:R.current});return M.current=a.default.loadAnimation(r),L(!!M.current),function(){var e;null==(e=M.current)||e.destroy(),M.current=void 0}}};return n.useEffect(function(){var e=P();return function(){return null==e?void 0:e()}},[l,f]),n.useEffect(function(){M.current&&(M.current.autoplay=!!d)},[d]),n.useEffect(function(){if(M.current){if(!m)return void M.current.resetSegments(!0);Array.isArray(m)&&m.length&&((M.current.currentRawFrame<m[0]||M.current.currentRawFrame>m[1])&&(M.current.currentRawFrame=m[0]),M.current.setSegment(m[0],m[1]))}},[m]),n.useEffect(function(){var e=[{name:"complete",handler:p},{name:"loopComplete",handler:y},{name:"enterFrame",handler:v},{name:"segmentStart",handler:g},{name:"config_ready",handler:h},{name:"data_ready",handler:b},{name:"data_failed",handler:w},{name:"loaded_images",handler:S},{name:"DOMLoaded",handler:E},{name:"destroy",handler:A}].filter(function(e){return null!=e.handler});if(e.length){var t=e.map(function(e){var t;return null==(t=M.current)||t.addEventListener(e.name,e.handler),function(){var t;null==(t=M.current)||t.removeEventListener(e.name,e.handler)}});return function(){t.forEach(function(e){return e()})}}},[p,y,v,g,h,b,w,S,E,A]),{View:o.default.createElement("div",s({style:t,ref:R},O)),play:function(){var e;null==(e=M.current)||e.play()},stop:function(){var e;null==(e=M.current)||e.stop()},pause:function(){var e;null==(e=M.current)||e.pause()},setSpeed:function(e){var t;null==(t=M.current)||t.setSpeed(e)},goToAndStop:function(e,t){var n;null==(n=M.current)||n.goToAndStop(e,t)},goToAndPlay:function(e,t){var n;null==(n=M.current)||n.goToAndPlay(e,t)},setDirection:function(e){var t;null==(t=M.current)||t.setDirection(e)},playSegments:function(e,t){var n;null==(n=M.current)||n.playSegments(e,t)},setSubframe:function(e){var t;null==(t=M.current)||t.setSubframe(e)},getDuration:function(e){var t;return null==(t=M.current)?void 0:t.getDuration(e)},destroy:function(){var e;null==(e=M.current)||e.destroy(),M.current=void 0},animationContainerRef:R,animationLoaded:k,animationItem:M.current}},d=function(e){var t=e.wrapperRef,r=e.animationItem,a=e.mode,o=e.actions;n.useEffect(function(){var e,n,i,l,s,u=t.current;if(u&&r&&o.length)switch(r.stop(),a){case"scroll":return e=null,n=function(){var t,n,a,i=(n=(t=u.getBoundingClientRect()).top,a=t.height,(window.innerHeight-n)/(window.innerHeight+a)),l=o.find(function(e){var t=e.visibility;return t&&i>=t[0]&&i<=t[1]});if(l){if("seek"===l.type&&l.visibility&&2===l.frames.length){var s=l.frames[0]+Math.ceil((i-l.visibility[0])/(l.visibility[1]-l.visibility[0])*l.frames[1]);r.goToAndStop(s-r.firstFrame-1,!0)}"loop"===l.type&&(null===e||e!==l.frames?(r.playSegments(l.frames,!0),e=l.frames):r.isPaused&&(r.playSegments(l.frames,!0),e=l.frames)),"play"===l.type&&r.isPaused&&(r.resetSegments(!0),r.play()),"stop"===l.type&&r.goToAndStop(l.frames[0]-r.firstFrame-1,!0)}},document.addEventListener("scroll",n),function(){document.removeEventListener("scroll",n)};case"cursor":return i=function(e,t){var n=e,a=t;if(-1!==n&&-1!==a){var i,l,s,c,f,d=(i=n,l=a,c=(s=u.getBoundingClientRect()).top,f=s.left,{x:(i-f)/s.width,y:(l-c)/s.height});n=d.x,a=d.y}var m=o.find(function(e){var t=e.position;return t&&Array.isArray(t.x)&&Array.isArray(t.y)?n>=t.x[0]&&n<=t.x[1]&&a>=t.y[0]&&a<=t.y[1]:!(!t||Number.isNaN(t.x)||Number.isNaN(t.y))&&n===t.x&&a===t.y});if(m){if("seek"===m.type&&m.position&&Array.isArray(m.position.x)&&Array.isArray(m.position.y)&&2===m.frames.length){var p=(n-m.position.x[0])/(m.position.x[1]-m.position.x[0]),y=(a-m.position.y[0])/(m.position.y[1]-m.position.y[0]);r.playSegments(m.frames,!0),r.goToAndStop(Math.ceil((p+y)/2*(m.frames[1]-m.frames[0])),!0)}"loop"===m.type&&r.playSegments(m.frames,!0),"play"===m.type&&(r.isPaused&&r.resetSegments(!1),r.playSegments(m.frames)),"stop"===m.type&&r.goToAndStop(m.frames[0],!0)}},l=function(e){i(e.clientX,e.clientY)},s=function(){i(-1,-1)},u.addEventListener("mousemove",l),u.addEventListener("mouseout",s),function(){u.removeEventListener("mousemove",l),u.removeEventListener("mouseout",s)}}},[a,r])},m=function(e){var t=e.actions,n=e.mode,r=e.lottieObj,a=r.animationItem,o=r.View;return d({actions:t,animationItem:a,mode:n,wrapperRef:r.animationContainerRef}),o},p=["style","interactivity"];Object.defineProperty(e,"LottiePlayer",{enumerable:!0,get:function(){return a.default}}),e.default=function(e){var t,r,a,o=e.style,i=e.interactivity,l=f(u(e,p),o),s=l.View,c=l.play,d=l.stop,y=l.pause,v=l.setSpeed,g=l.goToAndStop,h=l.goToAndPlay,b=l.setDirection,w=l.playSegments,S=l.setSubframe,E=l.getDuration,A=l.destroy,O=l.animationContainerRef,j=l.animationLoaded,k=l.animationItem;return n.useEffect(function(){e.lottieRef&&(e.lottieRef.current={play:c,stop:d,pause:y,setSpeed:v,goToAndPlay:h,goToAndStop:g,setDirection:b,playSegments:w,setSubframe:S,getDuration:E,destroy:A,animationContainerRef:O,animationLoaded:j,animationItem:k})},[null==(t=e.lottieRef)?void 0:t.current]),m({lottieObj:{View:s,play:c,stop:d,pause:y,setSpeed:v,goToAndStop:g,goToAndPlay:h,setDirection:b,playSegments:w,setSubframe:S,getDuration:E,destroy:A,animationContainerRef:O,animationLoaded:j,animationItem:k},actions:null!=(r=null==i?void 0:i.actions)?r:[],mode:null!=(a=null==i?void 0:i.mode)?a:"scroll"})},e.useLottie=f,e.useLottieInteractivity=m,Object.defineProperty(e,"__esModule",{value:!0})})(t,n(48834),n(12115))},48666:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(12115);let a=r.forwardRef(function(e,t){let{title:n,titleId:a,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},o),n?r.createElement("title",{id:a},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 18v-5.25m0 0a6.01 6.01 0 0 0 1.5-.189m-1.5.189a6.01 6.01 0 0 1-1.5-.189m3.75 7.478a12.06 12.06 0 0 1-4.5 0m3.75 2.383a14.406 14.406 0 0 1-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 1 0-7.517 0c.85.493 1.509 1.333 1.509 2.316V18"}))})},78030:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(12115);let a=r.forwardRef(function(e,t){let{title:n,titleId:a,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},o),n?r.createElement("title",{id:a},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17.25 6.75 22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3-4.5 16.5"}))})},81131:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(12115);let a=r.forwardRef(function(e,t){let{title:n,titleId:a,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},o),n?r.createElement("title",{id:a},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"}))})},89282:e=>{e.exports={style:{fontFamily:"'Montserrat', 'Montserrat Fallback'",fontStyle:"normal"},className:"__className_eef8f4"}},89416:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(12115);let a=r.forwardRef(function(e,t){let{title:n,titleId:a,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},o),n?r.createElement("title",{id:a},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))})},93238:e=>{e.exports={style:{fontFamily:"'Poppins', 'Poppins Fallback'",fontStyle:"normal"},className:"__className_44151c"}}}]);