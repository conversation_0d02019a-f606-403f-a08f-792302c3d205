"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[711],{48711:(e,t,l)=>{l.r(t),l.d(t,{default:()=>j});var n=l(12115),o=l(38637),a=l.n(o),s=l(54641);let{string:i,array:r,object:u,number:c,bool:d,oneOfType:p,any:y,func:h}=a(),m={id:i,className:i,style:p([i,u]),elements:p([r,y]),stylesheet:p([r,y]),layout:p([u,y]),pan:p([u,y]),zoom:c,panningEnabled:d,userPanningEnabled:d,minZoom:c,maxZoom:c,zoomingEnabled:d,userZoomingEnabled:d,boxSelectionEnabled:d,autoungrabify:d,autolock:d,autounselectify:d,get:h,toJson:h,diff:h,forEach:h,cy:h,headless:d,styleEnabled:d,hideEdgesOnViewport:d,textureOnViewport:d,motionBlur:d,motionBlurOpacity:c,wheelSensitivity:c,pixelRatio:p([i,u])},b=(e,t)=>{if((null==e||null==t)&&(null!=e||null!=t))return!0;if(e===t)return!1;if("object"!=typeof e||"object"!=typeof t)return e!==t;let l=Object.keys(e),n=Object.keys(t),o=l=>e[l]!==t[l];return l.length!==n.length||!(!l.some(o)&&!n.some(o))},f=(e,t)=>null!=e?e[t]:null,g={diff:b,get:f,toJson:e=>e,forEach:(e,t)=>e.forEach(t),elements:[{data:{id:"a",label:"Example node A"}},{data:{id:"b",label:"Example node B"}},{data:{id:"e",source:"a",target:"b"}}],stylesheet:[{selector:"node",style:{label:"data(label)"}}],zoom:1,pan:{x:0,y:0}},E=(e,t,l,n)=>l(f(e,n),f(t,n)),w=(e,t,l,n,o,a,s)=>{e.batch(()=>{(n===b||E(t,l,n,"elements"))&&B(e,f(t,"elements"),f(l,"elements"),o,a,s,n),E(t,l,n,"stylesheet")&&k(e,f(t,"stylesheet"),f(l,"stylesheet"),o),["zoom","minZoom","maxZoom","zoomingEnabled","userZoomingEnabled","pan","panningEnabled","userPanningEnabled","boxSelectionEnabled","autoungrabify","autolock","autounselectify"].forEach(a=>{E(t,l,n,a)&&x(e,a,f(t,a),f(l,a),o)})}),E(t,l,n,"layout")&&O(e,f(t,"layout"),f(l,"layout"),o)},x=(e,t,l,n,o)=>{e[t](o(n))},O=(e,t,l,n)=>{let o=n(l);null!=o&&e.layout(o).run()},k=(e,t,l,n)=>{let o=e.style();null!=o&&o.fromJson(n(l)).update()},B=(e,t,l,n,o,a,s)=>{let i=[],r=e.collection(),u=[],c={},d={},p=e=>o(o(e,"data"),"id");a(l,e=>{d[p(e)]=e}),null!=t&&a(t,t=>{let l=p(t);c[l]=t,null!=d[l]||r.merge(e.getElementById(l))}),a(l,e=>{let t=p(e),l=c[t];null!=c[t]?u.push({ele1:l,ele2:e}):i.push(n(e))}),r.length>0&&e.remove(r),i.length>0&&e.add(i),u.forEach(({ele1:t,ele2:l})=>C(e,t,l,n,o,s))},C=(e,t,l,n,o,a)=>{let s=o(o(l,"data"),"id"),i=e.getElementById(s),r={};["data","position","selected","selectable","locked","grabbable","classes"].forEach(e=>{let s=o(l,e);a(s,o(t,e))&&(r[e]=n(s))});let u=o(l,"scratch");a(u,o(t,"scratch"))&&i.scratch(n(u)),Object.keys(r).length>0&&i.json(r)};class j extends n.Component{static get propTypes(){return m}static get defaultProps(){return g}static normalizeElements(e){if(null!=e.length)return e;{let{nodes:t,edges:l}=e;return null==t&&(t=[]),null==l&&(l=[]),t.concat(l)}}constructor(e){super(e),this.displayName="CytoscapeComponent",this.containerRef=n.createRef()}componentDidMount(){let e=this.containerRef.current,{global:t,headless:l,styleEnabled:n,hideEdgesOnViewport:o,textureOnViewport:a,motionBlur:i,motionBlurOpacity:r,wheelSensitivity:u,pixelRatio:c}=this.props,d=this._cy=new s.default({container:e,headless:l,styleEnabled:n,hideEdgesOnViewport:o,textureOnViewport:a,motionBlur:i,motionBlurOpacity:r,wheelSensitivity:u,pixelRatio:c});t&&(window[t]=d),this.updateCytoscape(null,this.props)}updateCytoscape(e,t){let l=this._cy,{diff:n,toJson:o,get:a,forEach:s}=t;w(l,e,t,n,o,a,s),null!=t.cy&&t.cy(l)}componentDidUpdate(e){this.updateCytoscape(e,this.props)}componentWillUnmount(){this._cy.destroy()}render(){let{id:e,className:t,style:l}=this.props;return n.createElement("div",{ref:this.containerRef,id:e,className:t,style:l})}}}}]);