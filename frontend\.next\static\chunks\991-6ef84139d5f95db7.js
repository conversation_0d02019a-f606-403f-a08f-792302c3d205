"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[991],{80991:(e,t,r)=>{r.d(t,{default:()=>j});var l=r(95155),s=r(12115),n=r(35695);let a=e=>{let{selectedFile:t,isDragging:r,fileInputRef:s,onDragOver:n,onDragLeave:a,onDrop:o,onBrowseClick:i,onFileInputChange:c,onClose:x,onUpload:d}=e;return(0,l.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-black/60 backdrop-blur-sm",onClick:x}),(0,l.jsxs)("div",{className:"relative z-10 glass-card w-full max-w-md p-6 border-t-2 border-r-2 border-kairosYellow shadow-[0_0_15px_rgba(255,246,141,0.5)]",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsx)("h3",{className:"text-xl font-medium text-kairosYellow",children:"Upload File"}),(0,l.jsx)("button",{type:"button","aria-label":"Close",onClick:x,className:"text-gray-400 hover:text-kairosYellow",children:(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,l.jsx)("div",{className:"border-2 border-dashed rounded-lg p-8 text-center mb-4 transition-colors ".concat(r?"border-kairosYellow bg-kairosYellow/10":"border-gray-600"),onDragOver:n,onDragLeave:a,onDrop:o,children:t?(0,l.jsxs)("div",{className:"flex flex-col items-center",children:[(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-kairosYellow mb-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,l.jsx)("p",{className:"text-gray-300 mb-1",children:t.name}),(0,l.jsxs)("p",{className:"text-gray-500 text-sm",children:[(t.size/1024).toFixed(2)," KB"]})]}):(0,l.jsxs)("div",{className:"flex flex-col items-center",children:[(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-gray-500 mb-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})}),(0,l.jsx)("p",{className:"text-gray-300 mb-1",children:"Drag & drop your file here"}),(0,l.jsx)("p",{className:"text-gray-500 text-sm",children:"or"}),(0,l.jsx)("button",{onClick:i,className:"mt-2 px-4 py-2 text-sm text-kairosYellow border border-kairosYellow rounded hover:bg-kairosYellow/10 transition-colors",children:"Browse Files"}),(0,l.jsx)("input",{"aria-label":"File upload",title:"Select a file to upload",type:"file",ref:s,onChange:c,className:"hidden"})]})}),(0,l.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,l.jsx)("button",{onClick:x,className:"px-4 py-2 border border-gray-600 text-gray-300 rounded hover:bg-black/30 transition-colors",children:"Cancel"}),(0,l.jsx)("button",{onClick:d,disabled:!t,style:t?{backgroundColor:"#46fcb0",color:"black"}:void 0,className:"px-4 py-2 rounded font-medium ".concat(t?"":"bg-gray-700 text-gray-400 cursor-not-allowed"," transition-colors"),children:"Upload"})]})]})]})},o=e=>{let{formFields:t,onFieldChange:r,onClose:s,onSubmit:n,inSidebar:a=!1}=e;return(0,l.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-black/60 backdrop-blur-sm",onClick:s}),(0,l.jsxs)("div",{className:"relative z-10 glass-card w-full max-w-md p-6 border-t-2 border-r-2 border-kairosYellow shadow-[0_0_15px_rgba(255,246,141,0.5)]",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsx)("h3",{className:"text-xl font-medium text-kairosYellow",children:"Key-Value Pairs"}),(0,l.jsx)("button",{type:"button","aria-label":"Close",onClick:s,className:"text-gray-400 hover:text-kairosYellow",children:(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,l.jsx)("div",{className:"space-y-4",children:t.map((e,t)=>(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("label",{className:"block text-gray-400 text-xs mb-1",children:"Key"}),(0,l.jsx)("input",{type:"text",value:e.key,onChange:e=>r(t,"key",e.target.value),className:"w-full bg-gray-800/50 border border-gray-700 rounded px-3 py-2 text-sm text-gray-200 placeholder-gray-500 focus:outline-none focus:border-kairosYellow",placeholder:"Enter key"})]}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("label",{className:"block text-gray-400 text-xs mb-1",children:"Value"}),(0,l.jsx)("input",{type:"text",value:e.value,onChange:e=>r(t,"value",e.target.value),className:"w-full bg-gray-800/50 border border-gray-700 rounded px-3 py-2 text-sm text-gray-200 placeholder-gray-500 focus:outline-none focus:border-kairosYellow",placeholder:"Enter value"})]})]},t))}),(0,l.jsxs)("div",{className:"flex justify-end mt-6 space-x-3",children:[(0,l.jsx)("button",{type:"button",onClick:s,className:"px-4 py-2 border border-gray-600 text-gray-300 rounded hover:bg-black/30 transition-colors",children:"Cancel"}),(0,l.jsx)("button",{type:"button",onClick:n,className:"px-4 py-2 rounded font-medium bg-kairosGreen text-black hover:bg-opacity-80 transition-colors",children:"Submit"})]})]})]})};function i(e){let{uploadingFiles:t,onRemoveFile:r}=e;if(0===t.length)return null;let s=e=>{var t;let r=null==(t=e.split(".").pop())?void 0:t.toLowerCase();return["jpg","jpeg","png","gif","webp"].includes(r||"")?(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,l.jsx)("rect",{x:"3",y:"3",width:"18",height:"18",rx:"2",ry:"2"}),(0,l.jsx)("circle",{cx:"8.5",cy:"8.5",r:"1.5"}),(0,l.jsx)("polyline",{points:"21 15 16 10 5 21"})]}):["pdf"].includes(r||"")?(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,l.jsx)("path",{d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"}),(0,l.jsx)("polyline",{points:"14 2 14 8 20 8"}),(0,l.jsx)("line",{x1:"16",y1:"13",x2:"8",y2:"13"}),(0,l.jsx)("line",{x1:"16",y1:"17",x2:"8",y2:"17"}),(0,l.jsx)("polyline",{points:"10 9 9 9 8 9"})]}):(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,l.jsx)("path",{d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"}),(0,l.jsx)("polyline",{points:"14 2 14 8 20 8"})]})};return(0,l.jsx)("div",{className:"mt-2 flex flex-wrap gap-2",children:t.map((e,t)=>(0,l.jsxs)("div",{className:"flex items-center bg-gray-800/50 rounded px-2 py-1 text-xs",children:[(0,l.jsx)("span",{className:"text-kairosYellow mr-1",children:s(e.file.name)}),(0,l.jsx)("span",{className:"text-gray-300 truncate max-w-[100px]",children:e.file.name}),e.complete?(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1 text-kairosGreen",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:(0,l.jsx)("path",{d:"M20 6L9 17l-5-5"})}):(0,l.jsx)("div",{className:"ml-2 w-16 h-1 bg-gray-700 rounded-full overflow-hidden",children:(0,l.jsx)("div",{className:"h-full bg-kairosGreen rounded-full transition-all duration-300",style:{width:"".concat(e.progress,"%")}})}),(0,l.jsx)("button",{onClick:()=>r(e.file.name),className:"ml-1 text-gray-400 hover:text-gray-200 transition-colors","aria-label":"Remove file",children:(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3.5 w-3.5",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,l.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,l.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]},t))})}var c=r(47110);function x(){let{chartSelection:e,setChartSelection:t}=(0,c.Y)();if(!e.point&&!e.range)return null;let r=()=>{t({})};return(0,l.jsxs)("div",{className:"mt-2 flex flex-wrap gap-2",children:[e.point&&(0,l.jsxs)("div",{className:"flex items-center bg-gray-800/50 rounded px-2 py-1 text-xs",children:[(0,l.jsx)("span",{className:"text-kairosYellow mr-1",children:(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,l.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,l.jsx)("circle",{cx:"12",cy:"12",r:"3"})]})}),(0,l.jsxs)("span",{className:"text-gray-300 truncate max-w-[150px]",children:["Point: ",e.point.name," (",e.point.value,")"]}),(0,l.jsx)("button",{type:"button",onClick:r,className:"ml-1 text-gray-400 hover:text-gray-200 transition-colors","aria-label":"Clear selection",children:(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3.5 w-3.5",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,l.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,l.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),e.range&&(0,l.jsxs)("div",{className:"flex items-center bg-gray-800/50 rounded px-2 py-1 text-xs",children:[(0,l.jsx)("span",{className:"text-kairosYellow mr-1",children:(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:(0,l.jsx)("path",{d:"M3 6h18M3 12h18M3 18h18"})})}),(0,l.jsx)("span",{className:"text-gray-300 truncate max-w-[150px]",children:e.range.start===e.range.end?"Point: ".concat(e.range.start):"Range: ".concat(e.range.start," to ").concat(e.range.end)}),(0,l.jsx)("button",{type:"button",onClick:r,className:"ml-1 text-gray-400 hover:text-gray-200 transition-colors","aria-label":"Clear selection",children:(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3.5 w-3.5",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,l.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,l.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]})]})}let d=e=>{let{buttonRef:t,menuRef:r,showMenu:s,onToggleMenu:n,onFileUpload:a,onServerConnection:o,direction:i="top",inSidebar:c=!1}=e;return(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("button",{ref:t,className:"w-7 h-7 rounded-full border border-gray-600 flex items-center justify-center text-gray-400 hover:text-gray-200 hover:bg-gray-800 transition-colors","aria-label":"Add attachment",onClick:n,children:(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,l.jsx)("line",{x1:"12",y1:"5",x2:"12",y2:"19"}),(0,l.jsx)("line",{x1:"5",y1:"12",x2:"19",y2:"12"})]})}),s&&(0,l.jsxs)("div",{ref:r,className:"absolute ".concat("top"===i?"bottom-full mb-2":"top-full mt-2"," left-0 p-2 w-40 rounded-md z-10 ").concat(c?"bg-gray-900 border border-gray-700":"bg-gray-800 border border-gray-700 shadow-lg"),children:[(0,l.jsxs)("button",{onClick:a,className:"w-full text-left px-3 py-2 text-sm text-gray-300 hover:bg-white/10 rounded transition-colors flex items-center",children:[(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,l.jsx)("path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}),(0,l.jsx)("polyline",{points:"17 8 12 3 7 8"}),(0,l.jsx)("line",{x1:"12",y1:"3",x2:"12",y2:"15"})]}),"Upload File"]}),(0,l.jsxs)("button",{onClick:o,className:"w-full text-left px-3 py-2 text-sm text-gray-300 hover:bg-white/10 rounded transition-colors flex items-center",children:[(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,l.jsx)("rect",{x:"2",y:"3",width:"20",height:"14",rx:"2",ry:"2"}),(0,l.jsx)("line",{x1:"8",y1:"21",x2:"16",y2:"21"}),(0,l.jsx)("line",{x1:"12",y1:"17",x2:"12",y2:"21"})]}),"Connect Server"]})]})]})};var u=r(52596),h=r(39688);function m(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,h.QP)((0,u.$)(t))}var g=r(16997),p=r(45084);let w=e=>{let{className:t}=e;return(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:m("w-6 h-6 ",t),children:(0,l.jsx)("path",{d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})})},f=e=>{let{className:t}=e;return(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",className:m("w-6 h-6 ",t),children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z",clipRule:"evenodd"})})},y=e=>{let{loadingStates:t,value:r=0}=e;return(0,l.jsx)("div",{className:"flex relative justify-start max-w-xl mx-auto flex-col mt-40",children:t.map((e,t)=>{let s=Math.max(1-.2*Math.abs(t-r),0);return(0,l.jsxs)(g.P.div,{className:m("text-left flex gap-2 mb-4"),initial:{opacity:0,y:-(40*r)},animate:{opacity:s,y:-(40*r)},transition:{duration:.5},children:[(0,l.jsxs)("div",{children:[t>r&&(0,l.jsx)(w,{className:"text-black dark:text-white"}),t<=r&&(0,l.jsx)(f,{className:m("text-black dark:text-white",r===t&&"text-black dark:text-lime-500 opacity-100")})]}),(0,l.jsx)("span",{className:m("text-black dark:text-white",r===t&&"text-black dark:text-lime-500 opacity-100"),children:e.text})]},t)})})},v=e=>{let{loadingStates:t,loading:r,duration:n=2e3,loop:a=!0}=e,[o,i]=(0,s.useState)(0),[c,x]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{if(r)x(!0);else if(c){let e=setTimeout(()=>{x(!1),i(0)},1e3);return()=>clearTimeout(e)}else i(0)},[r,c]),(0,s.useEffect)(()=>{if(!c)return;let e=setTimeout(()=>{i(e=>a||e!==t.length-1?a?e===t.length-1?0:e+1:Math.min(e+1,t.length-1):e)},n);return()=>clearTimeout(e)},[o,c,a,t.length,n]),(0,l.jsx)(p.N,{mode:"wait",children:(r||c)&&(0,l.jsxs)(g.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0,transition:{duration:.5}},className:"w-full h-full fixed inset-0 z-[100] flex items-center justify-center backdrop-blur-2xl",children:[(0,l.jsx)("div",{className:"h-96 relative",children:(0,l.jsx)(y,{value:o,loadingStates:t})}),(0,l.jsx)("div",{className:"bg-gradient-to-t inset-x-0 z-20 bottom-0 bg-white dark:bg-black h-full absolute [mask-image:radial-gradient(900px_at_center,transparent_30%,white)]"})]})})};function j(e){let{onSend:t=e=>{console.log("Default onSend handler:",e)},placeholder:r="let's explore, what do you want to know?",navigateTo:u,skipNavigation:h=!1,autoGrow:m=!1,maxHeight:g="120px",inSidebar:p=!1}=e,w=(0,n.useRouter)(),[f,y]=(0,s.useState)(""),j=(0,s.useRef)(null),{uploadFile:b,uploadedFiles:k,removeUploadedFile:N,clearUploadedFiles:C,chartSelection:L,setChartSelection:M,currentSlug:S,sendMessage:B}=(0,c.Y)(),W=()=>{if(m&&j.current){j.current.style.height="0px";let e=Math.max(Math.min(j.current.scrollHeight,parseInt(g)),24);j.current.style.height="".concat(e,"px")}};(0,s.useEffect)(()=>{m&&W()},[f,m]);let[Y,F]=(0,s.useState)(!1),[_,D]=(0,s.useState)(!1),[E,z]=(0,s.useState)(!1),[R,T]=(0,s.useState)(!1),P=(0,s.useRef)(null),K=(0,s.useRef)(null),U=(0,s.useRef)(null),[I,A]=(0,s.useState)(!1),[G,H]=(0,s.useState)(null),[O,V]=(0,s.useState)([]),[Z,Q]=(0,s.useState)([{key:"",value:""},{key:"",value:""},{key:"",value:""},{key:"",value:""}]);(0,s.useEffect)(()=>{let e=e=>{P.current&&!P.current.contains(e.target)&&K.current&&!K.current.contains(e.target)&&F(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);let $=()=>{if(f.trim()){if(h){console.log("Processing message in chat panel:",f),t&&t(f),y("");return}console.log("Send button clicked on main page, showing loader"),T(!0),y(""),setTimeout(()=>{B(f),setTimeout(()=>{T(!1),console.log("Loader complete, currentSlug:",S),S&&("/"===window.location.pathname||"/chat"===window.location.pathname)&&(console.log("Manually redirecting to chat page with slug:",S),w.push("/chats/".concat(S)))},3e3)},500)}},q=e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),$())},J=async()=>{if(G){V(e=>[...e,{file:G,progress:0,complete:!1}]),D(!1);try{let e=0,t=setInterval(()=>{(e+=5)<=90&&V(t=>t.map(t=>t.file.name===G.name?{...t,progress:e}:t))},200),r=await b(G);clearInterval(t),V(e=>e.map(e=>e.file.name===G.name?{...e,progress:100,complete:!0}:e)),console.log("File uploaded successfully:",r),H(null)}catch(e){console.error("Error uploading file:",e),V(e=>e.map(e=>e.file.name===G.name?{...e,progress:0,complete:!1}:e)),alert("Failed to upload file: ".concat(e instanceof Error?e.message:"Unknown error"))}finally{H(null)}}};return(0,s.useEffect)(()=>{let e=setTimeout(()=>{m&&j.current&&W()},300);if(m&&j.current){let t=new ResizeObserver(()=>{W()});return t.observe(j.current.parentElement||document.body),()=>{clearTimeout(e),t.disconnect()}}return()=>clearTimeout(e)},[m]),(0,l.jsxs)("div",{className:"relative w-full max-w-2xl",children:[(0,l.jsx)(v,{loadingStates:[{text:"Processing your message"},{text:"Analyzing content"},{text:"Searching knowledge base"},{text:"Generating response"},{text:"Finalizing results"}],loading:R,duration:2e3}),(0,l.jsxs)("div",{className:"glass-card px-5 py-4 flex flex-col border-t-2 border-r-2 border-kairosYellow shadow-[0_0_15px_rgba(255,246,141,0.5)]",children:[(0,l.jsxs)("div",{className:"flex items-center w-full",children:[m?(0,l.jsx)("textarea",{ref:j,value:f,onChange:e=>y(e.target.value),onKeyDown:q,onFocus:W,onBlur:W,placeholder:r,className:"flex-grow bg-transparent outline-none text-gray-200 placeholder-gray-500 text-sm resize-none overflow-y-auto min-h-[24px]",style:{maxHeight:g,scrollbarWidth:"thin",scrollbarColor:"rgba(180, 180, 180, 0.1) rgba(0, 0, 0, 0.05)"},rows:1}):(0,l.jsx)("input",{type:"text",value:f,onChange:e=>y(e.target.value),onKeyDown:q,placeholder:r,className:"flex-grow bg-transparent outline-none text-gray-200 placeholder-gray-500 text-sm"}),(0,l.jsx)("button",{type:"button",onClick:$,className:"ml-3 text-red-400 hover:text-red-300 transition-colors","aria-label":"Send message",children:(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,l.jsx)("path",{d:"M22 2L11 13"}),(0,l.jsx)("path",{d:"M22 2L15 22L11 13L2 9L22 2Z"})]})})]}),(0,l.jsx)(i,{uploadingFiles:O,onRemoveFile:e=>{V(t=>t.filter(t=>t.file.name!==e)),N(e)}}),(0,l.jsx)(x,{}),(0,l.jsxs)("div",{className:"flex items-center mt-3",children:[(0,l.jsx)(d,{buttonRef:K,menuRef:P,showMenu:Y,onToggleMenu:()=>F(!Y),onFileUpload:()=>{D(!0),F(!1)},onServerConnection:()=>{console.log("Server connection clicked"),F(!1)},direction:"top",inSidebar:p}),(0,l.jsx)("button",{type:"button",className:"w-7 h-7 rounded-full border border-gray-600 flex items-center justify-center text-gray-400 hover:text-gray-200 hover:bg-gray-800 transition-colors ml-2","aria-label":"Options",onClick:()=>{z(!0)},children:(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,l.jsx)("line",{x1:"5",y1:"12",x2:"19",y2:"12"}),(0,l.jsx)("line",{x1:"5",y1:"8",x2:"19",y2:"8"}),(0,l.jsx)("line",{x1:"5",y1:"16",x2:"19",y2:"16"})]})})]})]}),_&&(0,l.jsx)(a,{selectedFile:G,isDragging:I,fileInputRef:U,onDragOver:e=>{e.preventDefault(),A(!0)},onDragLeave:e=>{e.preventDefault(),A(!1)},onDrop:e=>{if(e.preventDefault(),A(!1),e.dataTransfer.files&&e.dataTransfer.files.length>0){let t=e.dataTransfer.files[0];H(t),console.log("File dropped:",t.name)}},onBrowseClick:()=>{U.current&&U.current.click()},onFileInputChange:e=>{if(e.target.files&&e.target.files.length>0){let t=e.target.files[0];H(t),console.log("File selected:",t.name)}},onClose:()=>{H(null),D(!1)},onUpload:J}),E&&(0,l.jsx)(o,{formFields:Z,onFieldChange:(e,t,r)=>{let l=[...Z];l[e][t]=r,Q(l)},onClose:()=>{z(!1)},onSubmit:()=>{console.log("Form submitted:",Z),z(!1)},inSidebar:p})]})}}}]);