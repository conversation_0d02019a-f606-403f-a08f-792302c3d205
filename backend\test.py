import asyncio
import websockets
import json

async def simulate_conversation():
    uri = "ws://localhost:8000/ws"
    async with websockets.connect(uri) as websocket:
        # Send a message
        message = {
            "message": "Hello, can you help me analyze some data?",
            "file_path": None
        }
        await websocket.send(json.dumps(message))
        
        # Receive response
        response = await websocket.receive()
        print(f"Received: {response}")

# Run the simulation
asyncio.run(simulate_conversation())