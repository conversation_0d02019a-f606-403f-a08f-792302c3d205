from typing import Dict, Any
from ..base_agent import Agent

class ForecastAgent(Agent):
    """A forecast agent that returns its name."""
    
    def __init__(self):
        description = "A forecast agent that returns its name."
        super().__init__("forecast", description)
        
    async def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the input data and return a response.
        
        Args:
            input_data: Dictionary containing input data
            
        Returns:
            Dictionary containing the response
        """
        return {
            "message": "Hello from Forecast Agent!",
            "status": "success",
            "next_agent": "planner"
        } 