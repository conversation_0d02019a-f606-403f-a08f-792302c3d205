(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[658],{13547:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>o});var r=n(95155),u=n(12115),a=n(35695);function t(){let e=(0,a.useSearchParams)().get("id");return((0,u.useEffect)(()=>{e&&window.opener&&window.opener.postMessage({type:"POPOUT_READY",id:e},"*")},[e]),e)?(0,r.jsx)("div",{className:"h-screen w-full overflow-hidden",children:(0,r.jsx)("div",{id:"popout-content-".concat(e),className:"h-full w-full"})}):(0,r.jsx)("div",{children:"Missing popout ID"})}function o(){return(0,r.jsx)(u.Suspense,{fallback:(0,r.jsx)("div",{children:"Loading..."}),children:(0,r.jsx)(t,{})})}},35695:(e,s,n)=>{"use strict";var r=n(18999);n.o(r,"usePathname")&&n.d(s,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(s,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(s,{useSearchParams:function(){return r.useSearchParams}})},70689:(e,s,n)=>{Promise.resolve().then(n.bind(n,13547))}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(70689)),_N_E=e.O()}]);