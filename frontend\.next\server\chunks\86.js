"use strict";exports.id=86,exports.ids=[86],exports.modules={2822:(t,e,i)=>{i.d(e,{L:()=>s});let s=(0,i(43210).createContext)({})},32097:(t,e,i)=>{i.d(e,{E:()=>n});var s=i(43210);let n=i(49567).B?s.useLayoutEffect:s.useEffect},48618:(t,e,i)=>{i.d(e,{t:()=>s});let s=(0,i(43210).createContext)(null)},49384:(t,e,i)=>{function s(){for(var t,e,i=0,s="",n=arguments.length;i<n;i++)(t=arguments[i])&&(e=function t(e){var i,s,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var r=e.length;for(i=0;i<r;i++)e[i]&&(s=t(e[i]))&&(n&&(n+=" "),n+=s)}else for(s in e)e[s]&&(n&&(n+=" "),n+=s);return n}(t))&&(s&&(s+=" "),s+=e);return s}i.d(e,{$:()=>s,A:()=>n});let n=s},49567:(t,e,i)=>{i.d(e,{B:()=>s});let s="undefined"!=typeof window},53038:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"useMergedRef",{enumerable:!0,get:function(){return n}});let s=i(43210);function n(t,e){let i=(0,s.useRef)(null),n=(0,s.useRef)(null);return(0,s.useCallback)(s=>{if(null===s){let t=i.current;t&&(i.current=null,t());let e=n.current;e&&(n.current=null,e())}else t&&(i.current=r(t,s)),e&&(n.current=r(e,s))},[t,e])}function r(t,e){if("function"!=typeof t)return t.current=e,()=>{t.current=null};{let i=t(e);return"function"==typeof i?i:()=>t(null)}}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},53906:(t,e,i)=>{i.d(e,{M:()=>n});var s=i(43210);function n(t){let e=(0,s.useRef)(null);return null===e.current&&(e.current=t()),e.current}},56923:(t,e,i)=>{i.d(e,{Q:()=>s});let s=(0,i(43210).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},76020:(t,e,i)=>{let s;function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function r(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function o(t,e,i,s){if("function"==typeof e){let[n,o]=r(s);e=e(void 0!==i?i:t.custom,n,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,o]=r(s);e=e(void 0!==i?i:t.custom,n,o)}return e}function a(t,e,i){let s=t.getProps();return o(s,e,void 0!==i?i:s.custom,t)}i.d(e,{P:()=>rS});let l=t=>Array.isArray(t),h=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],u={value:null,addProjectionMetrics:null},d={};function c(t,e){let i=!1,s=!0,n={delta:0,timestamp:0,isProcessing:!1},r=()=>i=!0,o=h.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,s=new Set,n=!1,r=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function h(e){o.has(e)&&(d.schedule(e),t()),l++,e(a)}let d={schedule:(t,e=!1,r=!1)=>{let a=r&&n?i:s;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{s.delete(t),o.delete(t)},process:t=>{if(a=t,n){r=!0;return}n=!0,[i,s]=[s,i],i.forEach(h),e&&u.value&&u.value.frameloop[e].push(l),l=0,i.clear(),n=!1,r&&(r=!1,d.process(t))}};return d}(r,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:c,preUpdate:p,update:m,preRender:f,render:y,postRender:g}=o,v=()=>{let r=d.useManualTiming?n.timestamp:performance.now();i=!1,d.useManualTiming||(n.delta=s?1e3/60:Math.max(Math.min(r-n.timestamp,40),1)),n.timestamp=r,n.isProcessing=!0,a.process(n),l.process(n),c.process(n),p.process(n),m.process(n),f.process(n),y.process(n),g.process(n),n.isProcessing=!1,i&&e&&(s=!1,t(v))},x=()=>{i=!0,s=!0,n.isProcessing||t(v)};return{schedule:h.reduce((t,e)=>{let s=o[e];return t[e]=(t,e=!1,n=!1)=>(i||x(),s.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<h.length;e++)o[h[e]].cancel(t)},state:n,steps:o}}let p=t=>t,{schedule:m,cancel:f,state:y,steps:g}=c("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:p,!0);function v(){s=void 0}let x={now:()=>(void 0===s&&x.set(y.isProcessing||d.useManualTiming?y.timestamp:performance.now()),s),set:t=>{s=t,queueMicrotask(v)}};function T(t,e){-1===t.indexOf(e)&&t.push(e)}function w(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class b{constructor(){this.subscriptions=[]}add(t){return T(this.subscriptions,t),()=>w(this.subscriptions,t)}notify(t,e,i){let s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,i);else for(let n=0;n<s;n++){let s=this.subscriptions[n];s&&s(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let P=t=>!isNaN(parseFloat(t)),S={current:void 0};class A{constructor(t,e={}){this.version="__VERSION__",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=x.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=x.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=P(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new b);let i=this.events[t].add(e);return"change"===t?()=>{i(),m.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return S.current&&S.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=x.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function M(t,e){return new A(t,e)}let V=t=>!!(t&&t.getVelocity);function E(t,e){let i=t.getValue("willChange");if(V(i)&&i.add)return i.add(e);if(!i&&d.WillChange){let i=new d.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let D=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),C="data-"+D("framerAppearId"),k=t=>null!==t,R=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],j=new Set(R),L={type:"spring",stiffness:500,damping:25,restSpeed:10},F=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),B={type:"keyframes",duration:.8},O={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},I=(t,{keyframes:e})=>e.length>2?B:j.has(t)?t.startsWith("scale")?F(e[1]):L:O;function U(t,e){return t?.[e]??t?.default??t}let $=t=>1e3*t,N=t=>t/1e3,W={layout:0,mainThread:0,waapi:0},Y=t=>e=>"string"==typeof e&&e.startsWith(t),z=Y("--"),H=Y("var(--"),X=t=>!!H(t)&&K.test(t.split("/*")[0].trim()),K=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,_=(t,e,i)=>i>e?e:i<t?t:i,q={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},G={...q,transform:t=>_(0,1,t)},Z={...q,default:1},Q=t=>Math.round(1e5*t)/1e5,J=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tt=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,te=(t,e)=>i=>!!("string"==typeof i&&tt.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),ti=(t,e,i)=>s=>{if("string"!=typeof s)return s;let[n,r,o,a]=s.match(J);return{[t]:parseFloat(n),[e]:parseFloat(r),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},ts=t=>_(0,255,t),tn={...q,transform:t=>Math.round(ts(t))},tr={test:te("rgb","red"),parse:ti("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+tn.transform(t)+", "+tn.transform(e)+", "+tn.transform(i)+", "+Q(G.transform(s))+")"},to={test:te("#"),parse:function(t){let e="",i="",s="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,s+=s,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:n?parseInt(n,16)/255:1}},transform:tr.transform},ta=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tl=ta("deg"),th=ta("%"),tu=ta("px"),td=ta("vh"),tc=ta("vw"),tp={...th,parse:t=>th.parse(t)/100,transform:t=>th.transform(100*t)},tm={test:te("hsl","hue"),parse:ti("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:s=1})=>"hsla("+Math.round(t)+", "+th.transform(Q(e))+", "+th.transform(Q(i))+", "+Q(G.transform(s))+")"},tf={test:t=>tr.test(t)||to.test(t)||tm.test(t),parse:t=>tr.test(t)?tr.parse(t):tm.test(t)?tm.parse(t):to.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tr.transform(t):tm.transform(t)},ty=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tg="number",tv="color",tx=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tT(t){let e=t.toString(),i=[],s={color:[],number:[],var:[]},n=[],r=0,o=e.replace(tx,t=>(tf.test(t)?(s.color.push(r),n.push(tv),i.push(tf.parse(t))):t.startsWith("var(")?(s.var.push(r),n.push("var"),i.push(t)):(s.number.push(r),n.push(tg),i.push(parseFloat(t))),++r,"${}")).split("${}");return{values:i,split:o,indexes:s,types:n}}function tw(t){return tT(t).values}function tb(t){let{split:e,types:i}=tT(t),s=e.length;return t=>{let n="";for(let r=0;r<s;r++)if(n+=e[r],void 0!==t[r]){let e=i[r];e===tg?n+=Q(t[r]):e===tv?n+=tf.transform(t[r]):n+=t[r]}return n}}let tP=t=>"number"==typeof t?0:t,tS={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(J)?.length||0)+(t.match(ty)?.length||0)>0},parse:tw,createTransformer:tb,getAnimatableNone:function(t){let e=tw(t);return tb(t)(e.map(tP))}};function tA(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tM(t,e){return i=>i>0?e:t}let tV=(t,e,i)=>t+(e-t)*i,tE=()=>{},tD=()=>{},tC=(t,e,i)=>{let s=t*t,n=i*(e*e-s)+s;return n<0?0:Math.sqrt(n)},tk=[to,tr,tm],tR=t=>tk.find(e=>e.test(t));function tj(t){let e=tR(t);if(tE(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tm&&(i=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let n=0,r=0,o=0;if(e/=100){let s=i<.5?i*(1+e):i+e-i*e,a=2*i-s;n=tA(a,s,t+1/3),r=tA(a,s,t),o=tA(a,s,t-1/3)}else n=r=o=i;return{red:Math.round(255*n),green:Math.round(255*r),blue:Math.round(255*o),alpha:s}}(i)),i}let tL=(t,e)=>{let i=tj(t),s=tj(e);if(!i||!s)return tM(t,e);let n={...i};return t=>(n.red=tC(i.red,s.red,t),n.green=tC(i.green,s.green,t),n.blue=tC(i.blue,s.blue,t),n.alpha=tV(i.alpha,s.alpha,t),tr.transform(n))},tF=new Set(["none","hidden"]),tB=(t,e)=>i=>e(t(i)),tO=(...t)=>t.reduce(tB);function tI(t,e){return i=>tV(t,e,i)}function tU(t){return"number"==typeof t?tI:"string"==typeof t?X(t)?tM:tf.test(t)?tL:tW:Array.isArray(t)?t$:"object"==typeof t?tf.test(t)?tL:tN:tM}function t$(t,e){let i=[...t],s=i.length,n=t.map((t,i)=>tU(t)(t,e[i]));return t=>{for(let e=0;e<s;e++)i[e]=n[e](t);return i}}function tN(t,e){let i={...t,...e},s={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(s[n]=tU(t[n])(t[n],e[n]));return t=>{for(let e in s)i[e]=s[e](t);return i}}let tW=(t,e)=>{let i=tS.createTransformer(e),s=tT(t),n=tT(e);return s.indexes.var.length===n.indexes.var.length&&s.indexes.color.length===n.indexes.color.length&&s.indexes.number.length>=n.indexes.number.length?tF.has(t)&&!n.values.length||tF.has(e)&&!s.values.length?function(t,e){return tF.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):tO(t$(function(t,e){let i=[],s={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let r=e.types[n],o=t.indexes[r][s[r]],a=t.values[o]??0;i[n]=a,s[r]++}return i}(s,n),n.values),i):(tE(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tM(t,e))};function tY(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tV(t,e,i):tU(t)(t,e)}let tz=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>m.update(e,t),stop:()=>f(e),now:()=>y.isProcessing?y.timestamp:x.now()}},tH=(t,e,i=10)=>{let s="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)s+=t(e/(n-1))+", ";return`linear(${s.substring(0,s.length-2)})`};function tX(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tK(t,e,i){var s,n;let r=Math.max(e-5,0);return s=i-t(r),(n=e-r)?1e3/n*s:0}let t_={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tq(t,e){return t*Math.sqrt(1-e*e)}let tG=["duration","bounce"],tZ=["stiffness","damping","mass"];function tQ(t,e){return e.some(e=>void 0!==t[e])}function tJ(t=t_.visualDuration,e=t_.bounce){let i,s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:r}=s,o=s.keyframes[0],a=s.keyframes[s.keyframes.length-1],l={done:!1,value:o},{stiffness:h,damping:u,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:t_.velocity,stiffness:t_.stiffness,damping:t_.damping,mass:t_.mass,isResolvedFromDuration:!1,...t};if(!tQ(t,tZ)&&tQ(t,tG))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,n=2*_(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:t_.mass,stiffness:s,damping:n}}else{let i=function({duration:t=t_.duration,bounce:e=t_.bounce,velocity:i=t_.velocity,mass:s=t_.mass}){let n,r;tE(t<=$(t_.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=_(t_.minDamping,t_.maxDamping,o),t=_(t_.minDuration,t_.maxDuration,N(t)),o<1?(n=e=>{let s=e*o,n=s*t;return .001-(s-i)/tq(e,o)*Math.exp(-n)},r=e=>{let s=e*o*t,r=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-s),l=tq(Math.pow(e,2),o);return(s*i+i-r)*a*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(n,r,5/t);if(t=$(t),isNaN(a))return{stiffness:t_.stiffness,damping:t_.damping,duration:t};{let e=Math.pow(a,2)*s;return{stiffness:e,damping:2*o*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:t_.mass}).isResolvedFromDuration=!0}return e}({...s,velocity:-N(s.velocity||0)}),f=p||0,y=u/(2*Math.sqrt(h*d)),g=a-o,v=N(Math.sqrt(h/d)),x=5>Math.abs(g);if(n||(n=x?t_.restSpeed.granular:t_.restSpeed.default),r||(r=x?t_.restDelta.granular:t_.restDelta.default),y<1){let t=tq(v,y);i=e=>a-Math.exp(-y*v*e)*((f+y*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===y)i=t=>a-Math.exp(-v*t)*(g+(f+v*g)*t);else{let t=v*Math.sqrt(y*y-1);i=e=>{let i=Math.exp(-y*v*e),s=Math.min(t*e,300);return a-i*((f+y*v*g)*Math.sinh(s)+t*g*Math.cosh(s))/t}}let T={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let s=0===t?f:0;y<1&&(s=0===t?$(f):tK(i,t,e));let o=Math.abs(a-e)<=r;l.done=Math.abs(s)<=n&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(tX(T),2e4),e=tH(e=>T.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return T}function t0({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:n=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:h=.5,restSpeed:u}){let d,c,p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,y=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,g=i*e,v=p+g,x=void 0===o?v:o(v);x!==v&&(g=x-p);let T=t=>-g*Math.exp(-t/s),w=t=>x+T(t),b=t=>{let e=T(t),i=w(t);m.done=Math.abs(e)<=h,m.value=m.done?x:i},P=t=>{f(m.value)&&(d=t,c=tJ({keyframes:[m.value,y(m.value)],velocity:tK(w,t,m.value),damping:n,stiffness:r,restDelta:h,restSpeed:u}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,b(t),P(t)),void 0!==d&&t>=d)?c.next(t-d):(e||b(t),m)}}}tJ.applyToOptions=t=>{let e=function(t,e=100,i){let s=i({...t,keyframes:[0,e]}),n=Math.min(tX(s),2e4);return{type:"keyframes",ease:t=>s.next(n*t).value/e,duration:N(n)}}(t,100,tJ);return t.ease=e.ease,t.duration=$(e.duration),t.type="keyframes",t};let t1=(t,e,i)=>{let s=e-t;return 0===s?1:(i-t)/s},t2=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function t5(t,e,i,s){if(t===e&&i===s)return p;let n=e=>(function(t,e,i,s,n){let r,o,a=0;do(r=t2(o=e+(i-e)/2,s,n)-t)>0?i=o:e=o;while(Math.abs(r)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:t2(n(t),e,s)}let t3=t5(.42,0,1,1),t9=t5(0,0,.58,1),t4=t5(.42,0,.58,1),t6=t=>Array.isArray(t)&&"number"!=typeof t[0],t8=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t7=t=>e=>1-t(1-e),et=t5(.33,1.53,.69,.99),ee=t7(et),ei=t8(ee),es=t=>(t*=2)<1?.5*ee(t):.5*(2-Math.pow(2,-10*(t-1))),en=t=>1-Math.sin(Math.acos(t)),er=t7(en),eo=t8(en),ea=t=>Array.isArray(t)&&"number"==typeof t[0],el={linear:p,easeIn:t3,easeInOut:t4,easeOut:t9,circIn:en,circInOut:eo,circOut:er,backIn:ee,backInOut:ei,backOut:et,anticipate:es},eh=t=>"string"==typeof t,eu=t=>{if(ea(t)){tD(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,s,n]=t;return t5(e,i,s,n)}return eh(t)?(tD(void 0!==el[t],`Invalid easing type '${t}'`),el[t]):t};function ed({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){var n;let r=t6(s)?s.map(eu):eu(s),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:s,mixer:n}={}){let r=t.length;if(tD(r===e.length,"Both input and output ranges must be the same length"),1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let s=[],n=i||d.mix||tY,r=t.length-1;for(let i=0;i<r;i++){let r=n(t[i],t[i+1]);e&&(r=tO(Array.isArray(e)?e[i]||p:e,r)),s.push(r)}return s}(e,s,n),l=a.length,h=i=>{if(o&&i<t[0])return e[0];let s=0;if(l>1)for(;s<t.length-2&&!(i<t[s+1]);s++);let n=t1(t[s],t[s+1],i);return a[s](n)};return i?e=>h(_(t[0],t[r-1],e)):h}((n=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let n=t1(0,e,s);t.push(tV(i,1,n))}}(e,t.length-1),e}(e),n.map(e=>e*t)),e,{ease:Array.isArray(r)?r:e.map(()=>r||t4).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let ec=t=>null!==t;function ep(t,{repeat:e,repeatType:i="loop"},s,n=1){let r=t.filter(ec),o=n<0||e&&"loop"!==i&&e%2==1?0:r.length-1;return o&&void 0!==s?s:r[o]}let em={decay:t0,inertia:t0,tween:ed,keyframes:ed,spring:tJ};function ef(t){"string"==typeof t.type&&(t.type=em[t.type])}class ey{constructor(){this.count=0,this.updateFinished()}get finished(){return this._finished}updateFinished(){this.count++,this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let eg=t=>t/100;class ev extends ey{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(t=!0)=>{if(t){let{motionValue:t}=this.options;t&&t.updatedAt!==x.now()&&this.tick(x.now())}if(this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:e}=this.options;e&&e()},W.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;ef(t);let{type:e=ed,repeat:i=0,repeatDelay:s=0,repeatType:n,velocity:r=0}=t,{keyframes:o}=t,a=e||ed;a!==ed&&"number"!=typeof o[0]&&(this.mixKeyframes=tO(eg,tY(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===n&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-r})),null===l.calculatedDuration&&(l.calculatedDuration=tX(l));let{calculatedDuration:h}=l;this.calculatedDuration=h,this.resolvedDuration=h+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:s,mixKeyframes:n,mirroredGenerator:r,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:h,repeat:u,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let y=this.currentTime-l*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?y<0:y>s;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let v=this.currentTime,x=i;if(u){let t=Math.min(this.currentTime,s)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,u+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/o)):"mirror"===d&&(x=r)),v=_(0,1,i)*o}let T=g?{done:!1,value:h[0]}:x.next(v);n&&(T.value=n(T.value));let{done:w}=T;g||null===a||(w=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);let b=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return b&&p!==t0&&(T.value=ep(h,this.options,f,this.speed)),m&&m(T.value),b&&this.finish(),T}then(t,e){return this.finished.then(t,e)}get duration(){return N(this.calculatedDuration)}get time(){return N(this.currentTime)}set time(t){t=$(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(x.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=N(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tz,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let s=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=s):null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime||(this.startTime=i??s),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(x.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown()}teardown(){this.notifyFinished(),this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,W.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let ex=t=>180*t/Math.PI,eT=t=>eb(ex(Math.atan2(t[1],t[0]))),ew={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:eT,rotateZ:eT,skewX:t=>ex(Math.atan(t[1])),skewY:t=>ex(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},eb=t=>((t%=360)<0&&(t+=360),t),eP=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),eS=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),eA={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:eP,scaleY:eS,scale:t=>(eP(t)+eS(t))/2,rotateX:t=>eb(ex(Math.atan2(t[6],t[5]))),rotateY:t=>eb(ex(Math.atan2(-t[2],t[0]))),rotateZ:eT,rotate:eT,skewX:t=>ex(Math.atan(t[4])),skewY:t=>ex(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function eM(t){return+!!t.includes("scale")}function eV(t,e){let i,s;if(!t||"none"===t)return eM(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=eA,s=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=ew,s=e}if(!s)return eM(e);let r=i[e],o=s[1].split(",").map(eD);return"function"==typeof r?r(o):o[r]}let eE=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return eV(i,e)};function eD(t){return parseFloat(t.trim())}let eC=t=>t===q||t===tu,ek=new Set(["x","y","z"]),eR=R.filter(t=>!ek.has(t)),ej={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eV(e,"x"),y:(t,{transform:e})=>eV(e,"y")};ej.translateX=ej.x,ej.translateY=ej.y;let eL=new Set,eF=!1,eB=!1,eO=!1;function eI(){if(eB){let t=Array.from(eL).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return eR.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eB=!1,eF=!1,eL.forEach(t=>t.complete(eO)),eL.clear()}function eU(){eL.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eB=!0)})}class e${constructor(t,e,i,s,n,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=n,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(eL.add(this),eF||(eF=!0,m.read(eU),m.resolveKeyframes(eI))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;if(null===t[0]){let n=s?.get(),r=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let s=i.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===n&&s.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eL.delete(this)}cancel(){"scheduled"===this.state&&(eL.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eN=t=>t.startsWith("--");function eW(t){let e;return()=>(void 0===e&&(e=t()),e)}let eY=eW(()=>void 0!==window.ScrollTimeline),ez={},eH=function(t,e){let i=eW(t);return()=>ez[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eX=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,eK={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eX([0,.65,.55,1]),circOut:eX([.55,0,1,.45]),backIn:eX([.31,.01,.66,-.59]),backOut:eX([.33,1.53,.69,.99])};function e_(t){return"function"==typeof t&&"applyToOptions"in t}class eq extends ey{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:s,pseudoElement:n,allowFlatten:r=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!n,this.allowFlatten=r,this.options=t,tD("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return e_(t)&&eH()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:s=0,duration:n=300,repeat:r=0,repeatType:o="loop",ease:a="easeOut",times:l}={},h){let d={[e]:i};l&&(d.offset=l);let c=function t(e,i){if(e)return"function"==typeof e?eH()?tH(e,i):"ease-out":ea(e)?eX(e):Array.isArray(e)?e.map(e=>t(e,i)||eK.easeOut):eK[e]}(a,n);Array.isArray(c)&&(d.easing=c),u.value&&W.waapi++;let p={delay:s,duration:n,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal"};h&&(p.pseudoElement=h);let m=t.animate(d,p);return u.value&&m.finished.finally(()=>{W.waapi--}),m}(e,i,s,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=ep(s,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eN(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()},this.animation.oncancel=()=>this.notifyFinished()}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return N(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return N(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=$(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eY())?(this.animation.timeline=t,p):e(this)}}let eG={anticipate:es,backInOut:ei,circInOut:eo};class eZ extends eq{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eG&&(t.ease=eG[t.ease])}(t),ef(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:s,element:n,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new ev({...r,autoplay:!1}),a=$(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let eQ=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tS.test(t)||"0"===t)&&!t.startsWith("url(")),eJ=new Set(["opacity","clipPath","filter","transform"]),e0=eW(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class e1 extends ey{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:r="loop",keyframes:o,name:a,motionValue:l,element:h,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=x.now();let d={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:n,repeatType:r,name:a,motionValue:l,element:h,...u},c=h?.KeyframeResolver||e$;this.keyframeResolver=new c(o,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),a,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,s){this.keyframeResolver=void 0;let{name:n,type:r,velocity:o,delay:a,isHandoff:l,onUpdate:h}=i;this.resolvedAt=x.now(),!function(t,e,i,s){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],o=eQ(n,e),a=eQ(r,e);return tE(o===a,`You are trying to animate ${e} from "${n}" to "${r}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${r} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||e_(i))&&s)}(t,n,r,o)&&((d.instantAnimations||!a)&&h?.(ep(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let u={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},c=!l&&function(t){let{motionValue:e,name:i,repeatDelay:s,repeatType:n,damping:r,type:o}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return e0()&&i&&eJ.has(i)&&("transform"!==i||!l)&&!a&&!s&&"mirror"!==n&&0!==r&&"inertia"!==o}(u)?new eZ({...u,element:u.motionValue.owner.current}):new ev(u);c.finished.then(()=>this.notifyFinished()).catch(p),this.pendingTimeline&&(this.stopTimeline=c.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=c}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eO=!0,eU(),eI(),eO=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e2=(t,e,i,s={},n,r)=>o=>{let a=U(s,t)||{},l=a.delay||s.delay||0,{elapsed:h=0}=s;h-=$(l);let u={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-h,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:r?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:n,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:h,...u}){return!!Object.keys(u).length}(a)&&Object.assign(u,I(t,u)),u.duration&&(u.duration=$(u.duration)),u.repeatDelay&&(u.repeatDelay=$(u.repeatDelay)),void 0!==u.from&&(u.keyframes[0]=u.from);let c=!1;if(!1!==u.type&&(0!==u.duration||u.repeatDelay)||(u.duration=0,0===u.delay&&(c=!0)),(d.instantAnimations||d.skipAnimations)&&(c=!0,u.duration=0,u.delay=0),u.allowFlatten=!a.type&&!a.ease,c&&!r&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},s){let n=t.filter(k),r=e&&"loop"!==i&&e%2==1?0:n.length-1;return n[r]}(u.keyframes,a);if(void 0!==t)return void m.update(()=>{u.onUpdate(t),u.onComplete()})}return a.isSync?new ev(u):new e1(u)},e5=new Set(["width","height","top","left","right","bottom",...R]);function e3(t,e,{delay:i=0,transitionOverride:s,type:n}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:o,...h}=e;s&&(r=s);let u=[],d=n&&t.animationState&&t.animationState.getState()[n];for(let e in h){let s=t.getValue(e,t.latestValues[e]??null),n=h[e];if(void 0===n||d&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(d,e))continue;let o={delay:i,...U(r||{},e)},a=s.get();if(void 0!==a&&!s.isAnimating&&!Array.isArray(n)&&n===a&&!o.velocity)continue;let l=!1;if(window.MotionHandoffAnimation){let i=t.props[C];if(i){let t=window.MotionHandoffAnimation(i,e,m);null!==t&&(o.startTime=t,l=!0)}}E(t,e),s.start(e2(e,s,n,t.shouldReduceMotion&&e5.has(e)?{type:!1}:o,t,l));let c=s.animation;c&&u.push(c)}return o&&Promise.all(u).then(()=>{m.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:s={},...n}=a(t,e)||{};for(let e in n={...n,...i}){var r;let i=l(r=n[e])?r[r.length-1]||0:r;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,M(i))}}(t,o)})}),u}function e9(t,e,i={}){let s=a(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=s||{};i.transitionOverride&&(n=i.transitionOverride);let r=s?()=>Promise.all(e3(t,s,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:r=0,staggerChildren:o,staggerDirection:a}=n;return function(t,e,i=0,s=0,n=1,r){let o=[],a=(t.variantChildren.size-1)*s,l=1===n?(t=0)=>t*s:(t=0)=>a-t*s;return Array.from(t.variantChildren).sort(e4).forEach((t,s)=>{t.notify("AnimationStart",e),o.push(e9(t,e,{...r,delay:i+l(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,r+s,o,a,i)}:()=>Promise.resolve(),{when:l}=n;if(!l)return Promise.all([r(),o(i.delay)]);{let[t,e]="beforeChildren"===l?[r,o]:[o,r];return t().then(()=>e())}}function e4(t,e){return t.sortNodePosition(e)}function e6(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}function e8(t){return"string"==typeof t||Array.isArray(t)}let e7=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],it=["initial",...e7],ie=it.length,ii=[...e7].reverse(),is=e7.length;function ir(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function io(){return{animate:ir(!0),whileInView:ir(),whileHover:ir(),whileTap:ir(),whileDrag:ir(),whileFocus:ir(),exit:ir()}}class ia{constructor(t){this.isMounted=!1,this.node=t}update(){}}class il extends ia{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>e9(t,e,i)));else if("string"==typeof e)s=e9(t,e,i);else{let n="function"==typeof e?a(t,e,i.custom):e;s=Promise.all(e3(t,n,i))}return s.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=io(),s=!0,r=e=>(i,s)=>{let n=a(t,s,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...s}=n;i={...i,...s,...e}}return i};function o(o){let{props:h}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<ie;t++){let s=it[t],n=e.props[s];(e8(n)||!1===n)&&(i[s]=n)}return i}(t.parent)||{},d=[],c=new Set,p={},m=1/0;for(let e=0;e<is;e++){var f,y;let a=ii[e],g=i[a],v=void 0!==h[a]?h[a]:u[a],x=e8(v),T=a===o?g.isActive:null;!1===T&&(m=e);let w=v===u[a]&&v!==h[a]&&x;if(w&&s&&t.manuallyAnimateOnMount&&(w=!1),g.protectedKeys={...p},!g.isActive&&null===T||!v&&!g.prevProp||n(v)||"boolean"==typeof v)continue;let b=(f=g.prevProp,"string"==typeof(y=v)?y!==f:!!Array.isArray(y)&&!e6(y,f)),P=b||a===o&&g.isActive&&!w&&x||e>m&&x,S=!1,A=Array.isArray(v)?v:[v],M=A.reduce(r(a),{});!1===T&&(M={});let{prevResolvedValues:V={}}=g,E={...V,...M},D=e=>{P=!0,c.has(e)&&(S=!0,c.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in E){let e=M[t],i=V[t];if(p.hasOwnProperty(t))continue;let s=!1;(l(e)&&l(i)?e6(e,i):e===i)?void 0!==e&&c.has(t)?D(t):g.protectedKeys[t]=!0:null!=e?D(t):c.add(t)}g.prevProp=v,g.prevResolvedValues=M,g.isActive&&(p={...p,...M}),s&&t.blockInitialAnimation&&(P=!1);let C=!(w&&b)||S;P&&C&&d.push(...A.map(t=>({animation:t,options:{type:a}})))}if(c.size){let e={};if("boolean"!=typeof h.initial){let i=a(t,Array.isArray(h.initial)?h.initial[0]:h.initial);i&&i.transition&&(e.transition=i.transition)}c.forEach(i=>{let s=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=s??null}),d.push({animation:e})}let g=!!d.length;return s&&(!1===h.initial||h.initial===h.animate)&&!t.manuallyAnimateOnMount&&(g=!1),s=!1,g?e(d):Promise.resolve()}return{animateChanges:o,setActive:function(e,s){if(i[e].isActive===s)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,s)),i[e].isActive=s;let n=o(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=io(),s=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ih=0;class iu extends ia{constructor(){super(...arguments),this.id=ih++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}function id(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}let ic=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function ip(t){return{point:{x:t.pageX,y:t.pageY}}}let im=t=>e=>ic(e)&&t(e,ip(e));function iy(t,e,i,s){return id(t,e,im(i),s)}function ig({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function iv(t){return t.max-t.min}function ix(t,e,i,s=.5){t.origin=s,t.originPoint=tV(e.min,e.max,t.origin),t.scale=iv(i)/iv(e),t.translate=tV(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iT(t,e,i,s){ix(t.x,e.x,i.x,s?s.originX:void 0),ix(t.y,e.y,i.y,s?s.originY:void 0)}function iw(t,e,i){t.min=i.min+e.min,t.max=t.min+iv(e)}function ib(t,e,i){t.min=e.min-i.min,t.max=t.min+iv(e)}function iP(t,e,i){ib(t.x,e.x,i.x),ib(t.y,e.y,i.y)}let iS=()=>({translate:0,scale:1,origin:0,originPoint:0}),iA=()=>({x:iS(),y:iS()}),iM=()=>({min:0,max:0}),iV=()=>({x:iM(),y:iM()});function iE(t){return[t("x"),t("y")]}function iD(t){return void 0===t||1===t}function iC({scale:t,scaleX:e,scaleY:i}){return!iD(t)||!iD(e)||!iD(i)}function ik(t){return iC(t)||iR(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iR(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function ij(t,e,i,s,n){return void 0!==n&&(t=s+n*(t-s)),s+i*(t-s)+e}function iL(t,e=0,i=1,s,n){t.min=ij(t.min,e,i,s,n),t.max=ij(t.max,e,i,s,n)}function iF(t,{x:e,y:i}){iL(t.x,e.translate,e.scale,e.originPoint),iL(t.y,i.translate,i.scale,i.originPoint)}function iB(t,e){t.min=t.min+e,t.max=t.max+e}function iO(t,e,i,s,n=.5){let r=tV(t.min,t.max,n);iL(t,e,i,r,s)}function iI(t,e){iO(t.x,e.x,e.scaleX,e.scale,e.originX),iO(t.y,e.y,e.scaleY,e.scale,e.originY)}function iU(t,e){return ig(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}let i$=({current:t})=>t?t.ownerDocument.defaultView:null;function iN(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iW=(t,e)=>Math.abs(t-e);class iY{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iX(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iW(t.x,e.x)**2+iW(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:n}=y;this.history.push({...s,timestamp:n});let{onStart:r,onMove:o}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iz(e,this.transformPagePoint),m.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=iX("pointercancel"===t.type?this.lastMoveEventInfo:iz(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),s&&s(t,r)},!ic(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;let r=iz(ip(t),this.transformPagePoint),{point:o}=r,{timestamp:a}=y;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iX(r,this.history)),this.removeListeners=tO(iy(this.contextWindow,"pointermove",this.handlePointerMove),iy(this.contextWindow,"pointerup",this.handlePointerUp),iy(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),f(this.updatePoint)}}function iz(t,e){return e?{point:e(t.point)}:t}function iH(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iX({point:t},e){return{point:t,delta:iH(t,iK(e)),offset:iH(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,n=iK(t);for(;i>=0&&(s=t[i],!(n.timestamp-s.timestamp>$(.1)));)i--;if(!s)return{x:0,y:0};let r=N(n.timestamp-s.timestamp);if(0===r)return{x:0,y:0};let o={x:(n.x-s.x)/r,y:(n.y-s.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function iK(t){return t[t.length-1]}function i_(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iq(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function iG(t,e,i){return{min:iZ(t,e),max:iZ(t,i)}}function iZ(t,e){return"number"==typeof t?t:t[e]||0}let iQ={x:!1,y:!1},iJ=new WeakMap;class i0{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iV(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new iY(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(ip(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:n}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(iQ[t])return null;else return iQ[t]=!0,()=>{iQ[t]=!1};return iQ.x||iQ.y?null:(iQ.x=iQ.y=!0,()=>{iQ.x=iQ.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iE(t=>{let e=this.getAxisMotionValue(t).get()||0;if(th.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=iv(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&m.postRender(()=>n(t,e)),E(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:n,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iE(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:i$(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:n}=this.getProps();n&&m.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!i1(t,s,this.currentDirection))return;let n=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?tV(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?tV(i,t,s.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),n.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;t&&iN(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:s,right:n}){return{x:i_(t.x,i,n),y:i_(t.y,e,s)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:iG(t,"left","right"),y:iG(t,"top","bottom")}}(e),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iE(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iN(e))return!1;let s=e.current;tD(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let r=function(t,e,i){let s=iU(t,i),{scroll:n}=e;return n&&(iB(s.x,n.offset.x),iB(s.y,n.offset.y)),s}(s,n.root,this.visualElement.getTransformPagePoint()),o=(t=n.layout.layoutBox,{x:iq(t.x,r.x),y:iq(t.y,r.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=ig(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:n,dragSnapToOrigin:r,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iE(o=>{if(!i1(o,e,this.currentDirection))return;let l=a&&a[o]||{};r&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[o]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,h)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return E(this.visualElement,t),i.start(e2(t,i,0,e,this.visualElement,!1))}stopAnimation(){iE(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iE(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iE(e=>{let{drag:i}=this.getProps();if(!i1(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,n=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:r}=s.layout.layoutBox[e];n.set(t[e]-tV(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iN(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};iE(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=iv(t),n=iv(e);return n>s?i=t1(e.min,e.max-s,t.min):s>n&&(i=t1(t.min,t.max-n,e.min)),_(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iE(e=>{if(!i1(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:r}=this.constraints[e];i.set(tV(n,r,s[e]))})}addListeners(){if(!this.visualElement.current)return;iJ.set(this.visualElement,this);let t=iy(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iN(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),m.read(e);let n=id(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iE(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),s(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:n=!1,dragElastic:r=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:n,dragElastic:r,dragMomentum:o}}}function i1(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i2 extends ia{constructor(t){super(t),this.removeGroupControls=p,this.removeListeners=p,this.controls=new i0(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||p}unmount(){this.removeGroupControls(),this.removeListeners()}}let i5=t=>(e,i)=>{t&&m.postRender(()=>t(e,i))};class i3 extends ia{constructor(){super(...arguments),this.removePointerDownListener=p}onPointerDown(t){this.session=new iY(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:i$(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:i5(t),onStart:i5(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&m.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=iy(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var i9,i4,i6=i(60687),i8=i(43210),i7=i(93905),st=i(2822);let se=(0,i8.createContext)({}),si={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ss(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let sn={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tu.test(t))return t;else t=parseFloat(t);let i=ss(t,e.target.x),s=ss(t,e.target.y);return`${i}% ${s}%`}},sr={},{schedule:so}=c(queueMicrotask,!1);class sa extends i8.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:n}=t;for(let t in sh)sr[t]=sh[t],z(t)&&(sr[t].isCSSVariable=!0);n&&(e.group&&e.group.add(n),i&&i.register&&s&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),si.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:n}=this.props,{projection:r}=i;return r&&(r.isPresent=n,s||t.layoutDependency!==e||void 0===e||t.isPresent!==n?r.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?r.promote():r.relegate()||m.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),so.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function sl(t){let[e,i]=(0,i7.xQ)(),s=(0,i8.useContext)(st.L);return(0,i6.jsx)(sa,{...t,layoutGroup:s,switchLayoutGroup:(0,i8.useContext)(se),isPresent:e,safeToRemove:i})}let sh={borderRadius:{...sn,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:sn,borderTopRightRadius:sn,borderBottomLeftRadius:sn,borderBottomRightRadius:sn,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=tS.parse(t);if(s.length>5)return t;let n=tS.createTransformer(t),r=+("number"!=typeof s[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;s[0+r]/=o,s[1+r]/=a;let l=tV(o,a,.5);return"number"==typeof s[2+r]&&(s[2+r]/=l),"number"==typeof s[3+r]&&(s[3+r]/=l),n(s)}}},su=(t,e)=>t.depth-e.depth;class sd{constructor(){this.children=[],this.isDirty=!1}add(t){T(this.children,t),this.isDirty=!0}remove(t){w(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(su),this.isDirty=!1,this.children.forEach(t)}}function sc(t){return V(t)?t.get():t}let sp=["TopLeft","TopRight","BottomLeft","BottomRight"],sm=sp.length,sf=t=>"string"==typeof t?parseFloat(t):t,sy=t=>"number"==typeof t||tu.test(t);function sg(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let sv=sT(0,.5,er),sx=sT(.5,.95,p);function sT(t,e,i){return s=>s<t?0:s>e?1:i(t1(t,e,s))}function sw(t,e){t.min=e.min,t.max=e.max}function sb(t,e){sw(t.x,e.x),sw(t.y,e.y)}function sP(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function sS(t,e,i,s,n){return t-=e,t=s+1/i*(t-s),void 0!==n&&(t=s+1/n*(t-s)),t}function sA(t,e,[i,s,n],r,o){!function(t,e=0,i=1,s=.5,n,r=t,o=t){if(th.test(e)&&(e=parseFloat(e),e=tV(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=tV(r.min,r.max,s);t===r&&(a-=e),t.min=sS(t.min,e,i,a,n),t.max=sS(t.max,e,i,a,n)}(t,e[i],e[s],e[n],e.scale,r,o)}let sM=["x","scaleX","originX"],sV=["y","scaleY","originY"];function sE(t,e,i,s){sA(t.x,e,sM,i?i.x:void 0,s?s.x:void 0),sA(t.y,e,sV,i?i.y:void 0,s?s.y:void 0)}function sD(t){return 0===t.translate&&1===t.scale}function sC(t){return sD(t.x)&&sD(t.y)}function sk(t,e){return t.min===e.min&&t.max===e.max}function sR(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function sj(t,e){return sR(t.x,e.x)&&sR(t.y,e.y)}function sL(t){return iv(t.x)/iv(t.y)}function sF(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class sB{constructor(){this.members=[]}add(t){T(this.members,t),t.scheduleRender()}remove(t){if(w(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let sO={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},sI=["","X","Y","Z"],sU={visibility:"hidden"},s$=0;function sN(t,e,i,s){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),s&&(s[t]=0))}function sW({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:n}){return class{constructor(t={},i=e?.()){this.id=s$++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,u.value&&(sO.nodes=sO.calculatedTargetDeltas=sO.calculatedProjections=0),this.nodes.forEach(sH),this.nodes.forEach(sQ),this.nodes.forEach(sJ),this.nodes.forEach(sX),u.addProjectionMetrics&&u.addProjectionMetrics(sO)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new sd)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new b),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:i,layout:s,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let i,s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=x.now(),s=({timestamp:n})=>{let r=n-i;r>=250&&(f(s),t(r-e))};return m.setup(s,!0),()=>f(s)}(s,250),si.hasAnimatedSinceResize&&(si.hasAnimatedSinceResize=!1,this.nodes.forEach(sZ))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||n.getDefaultTransition()||s9,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=n.getProps(),l=!this.targetLayout||!sj(this.targetLayout,s),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,h);let e={...U(r,"layout"),onPlay:o,onComplete:a};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||sZ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),f(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(s0),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=i.props[C];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",m,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(s_);return}this.isUpdating||this.nodes.forEach(sq),this.isUpdating=!1,this.nodes.forEach(sG),this.nodes.forEach(sY),this.nodes.forEach(sz),this.clearAllSnapshots();let t=x.now();y.delta=_(0,1e3/60,t-y.timestamp),y.timestamp=t,y.isProcessing=!0,g.update.process(y),g.preRender.process(y),g.render.process(y),y.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,so.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(sK),this.sharedNodes.forEach(s1)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,m.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){m.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iv(this.snapshot.measuredBox.x)||iv(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iV(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!sC(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,r=s!==this.prevTransformTemplateValue;t&&this.instance&&(e||ik(this.latestValues)||r)&&(n(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),s8((e=s).x),s8(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iV();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(nt))){let{scroll:t}=this.root;t&&(iB(e.x,t.offset.x),iB(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iV();if(sb(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:n,options:r}=s;s!==this.root&&n&&r.layoutScroll&&(n.wasRoot&&sb(e,t),iB(e.x,n.offset.x),iB(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let i=iV();sb(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&iI(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),ik(s.latestValues)&&iI(i,s.latestValues)}return ik(this.latestValues)&&iI(i,this.latestValues),i}removeTransform(t){let e=iV();sb(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!ik(i.latestValues))continue;iC(i.latestValues)&&i.updateSnapshot();let s=iV();sb(s,i.measurePageBox()),sE(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return ik(this.latestValues)&&sE(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==y.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:n}=this.options;if(this.layout&&(s||n)){if(this.resolvedRelativeTargetAt=y.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iV(),this.relativeTargetOrigin=iV(),iP(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),sb(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iV(),this.targetWithTransforms=iV()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var r,o,a;this.forceRelativeParentToResolveTarget(),r=this.target,o=this.relativeTarget,a=this.relativeParent.target,iw(r.x,o.x,a.x),iw(r.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sb(this.target,this.layout.layoutBox),iF(this.target,this.targetDelta)):sb(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iV(),this.relativeTargetOrigin=iV(),iP(this.relativeTargetOrigin,this.target,t.target),sb(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}u.value&&sO.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iC(this.parent.latestValues)||iR(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===y.timestamp&&(i=!1),i)return;let{layout:s,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||n))return;sb(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,s=!1){let n,r,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){r=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iI(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,iF(t,r)),s&&ik(n.latestValues)&&iI(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iV());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(sP(this.prevProjectionDelta.x,this.projectionDelta.x),sP(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iT(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===r&&this.treeScale.y===o&&sF(this.projectionDelta.x,this.prevProjectionDelta.x)&&sF(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),u.value&&sO.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iA(),this.projectionDelta=iA(),this.projectionDeltaWithTransform=iA()}setAnimationOrigin(t,e=!1){let i,s=this.snapshot,n=s?s.latestValues:{},r={...this.latestValues},o=iA();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iV(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,d=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(s3));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(s2(o.x,t.x,s),s2(o.y,t.y,s),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,c,p,m,f,y;iP(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,y=s,s5(p.x,m.x,f.x,y),s5(p.y,m.y,f.y,y),i&&(h=this.relativeTarget,c=i,sk(h.x,c.x)&&sk(h.y,c.y))&&(this.isProjectionDirty=!1),i||(i=iV()),sb(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,s,n,r){n?(t.opacity=tV(0,i.opacity??1,sv(s)),t.opacityExit=tV(e.opacity??1,0,sx(s))):r&&(t.opacity=tV(e.opacity??1,i.opacity??1,s));for(let n=0;n<sm;n++){let r=`border${sp[n]}Radius`,o=sg(e,r),a=sg(i,r);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||sy(o)===sy(a)?(t[r]=Math.max(tV(sf(o),sf(a),s),0),(th.test(a)||th.test(o))&&(t[r]+="%")):t[r]=a)}(e.rotate||i.rotate)&&(t.rotate=tV(e.rotate||0,i.rotate||0,s))}(r,n,this.latestValues,s,d,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(!1),this.resumingFrom?.currentAnimation?.stop(!1),this.pendingAnimation&&(f(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=m.update(()=>{si.hasAnimatedSinceResize=!0,W.layout++,this.motionValue||(this.motionValue=M(0)),this.currentAnimation=function(t,e,i){let s=V(t)?t:M(t);return s.start(e2("",s,e,i)),s.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{W.layout--},onComplete:()=>{W.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop(!1)),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:n}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&s7(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||iV();let e=iv(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=iv(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}sb(e,i),iI(e,n),iT(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new sB),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&sN("z",t,s,this.animationValues);for(let e=0;e<sI.length;e++)sN(`rotate${sI[e]}`,t,s,this.animationValues),sN(`skew${sI[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return sU;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=sc(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=sc(t?.pointerEvents)||""),this.hasProjected&&!ik(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let n=s.animationValues||s.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let s="",n=t.x.translate/e.x,r=t.y.translate/e.y,o=i?.z||0;if((n||r||o)&&(s=`translate3d(${n}px, ${r}px, ${o}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:r,skewX:o,skewY:a}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),n&&(s+=`rotateX(${n}deg) `),r&&(s+=`rotateY(${r}deg) `),o&&(s+=`skewX(${o}deg) `),a&&(s+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(s+=`scale(${a}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),i&&(e.transform=i(n,e.transform));let{x:r,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*r.origin}% ${100*o.origin}% 0`,s.animationValues?e.opacity=s===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:e.opacity=s===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,sr){if(void 0===n[t])continue;let{correct:i,applyTo:r,isCSSVariable:o}=sr[t],a="none"===e.transform?n[t]:i(n[t],s);if(r){let t=r.length;for(let i=0;i<t;i++)e[r[i]]=a}else o?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=s===this?sc(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop(!1)),this.root.nodes.forEach(s_),this.root.sharedNodes.clear()}}}function sY(t){t.updateLayout()}function sz(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:s}=t.layout,{animationType:n}=t.options,r=e.source!==t.layout.source;"size"===n?iE(t=>{let s=r?e.measuredBox[t]:e.layoutBox[t],n=iv(s);s.min=i[t].min,s.max=s.min+n}):s7(n,e.layoutBox,i)&&iE(s=>{let n=r?e.measuredBox[s]:e.layoutBox[s],o=iv(i[s]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+o)});let o=iA();iT(o,i,e.layoutBox);let a=iA();r?iT(a,t.applyTransform(s,!0),e.measuredBox):iT(a,i,e.layoutBox);let l=!sC(o),h=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:n,layout:r}=s;if(n&&r){let o=iV();iP(o,e.layoutBox,n.layoutBox);let a=iV();iP(a,i,r.layoutBox),sj(o,a)||(h=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function sH(t){u.value&&sO.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function sX(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function sK(t){t.clearSnapshot()}function s_(t){t.clearMeasurements()}function sq(t){t.isLayoutDirty=!1}function sG(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function sZ(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function sQ(t){t.resolveTargetDelta()}function sJ(t){t.calcProjection()}function s0(t){t.resetSkewAndRotation()}function s1(t){t.removeLeadSnapshot()}function s2(t,e,i){t.translate=tV(e.translate,0,i),t.scale=tV(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function s5(t,e,i,s){t.min=tV(e.min,i.min,s),t.max=tV(e.max,i.max,s)}function s3(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let s9={duration:.45,ease:[.4,0,.1,1]},s4=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),s6=s4("applewebkit/")&&!s4("chrome/")?Math.round:p;function s8(t){t.min=s6(t.min),t.max=s6(t.max)}function s7(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(sL(e)-sL(i)))}function nt(t){return t!==t.root&&t.scroll?.wasRoot}let ne=sW({attachResizeListener:(t,e)=>id(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ni={current:void 0},ns=sW({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ni.current){let t=new ne({});t.mount(window),t.setOptions({layoutScroll:!0}),ni.current=t}return ni.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function nn(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function nr(t){return!("touch"===t.pointerType||iQ.x||iQ.y)}function no(t,e,i){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=s["onHover"+i];n&&m.postRender(()=>n(e,ip(e)))}class na extends ia{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=nn(t,i),o=t=>{if(!nr(t))return;let{target:i}=t,s=e(i,t);if("function"!=typeof s||!i)return;let r=t=>{nr(t)&&(s(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,n)};return s.forEach(t=>{t.addEventListener("pointerenter",o,n)}),r}(t,(t,e)=>(no(this.node,e,"Start"),t=>no(this.node,t,"End"))))}unmount(){}}class nl extends ia{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tO(id(this.node.current,"focus",()=>this.onFocus()),id(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let nh=(t,e)=>!!e&&(t===e||nh(t,e.parentElement)),nu=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),nd=new WeakSet;function nc(t){return e=>{"Enter"===e.key&&t(e)}}function np(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let nm=(t,e)=>{let i=t.currentTarget;if(!i)return;let s=nc(()=>{if(nd.has(i))return;np(i,"down");let t=nc(()=>{np(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>np(i,"cancel"),e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)};function nf(t){return ic(t)&&!(iQ.x||iQ.y)}function ny(t,e,i){let{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=s["onTap"+("End"===i?"":i)];n&&m.postRender(()=>n(e,ip(e)))}class ng extends ia{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=nn(t,i),o=t=>{let s=t.currentTarget;if(!nf(t)||nd.has(s))return;nd.add(s);let r=e(s,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),nd.has(s)&&nd.delete(s),nf(t)&&"function"==typeof r&&r(t,{success:e})},a=t=>{o(t,s===window||s===document||i.useGlobalTarget||nh(s,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return s.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,n),t instanceof HTMLElement)&&(t.addEventListener("focus",t=>nm(t,n)),nu.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}(t,(t,e)=>(ny(this.node,e,"Start"),(t,{success:e})=>ny(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nv=new WeakMap,nx=new WeakMap,nT=t=>{let e=nv.get(t.target);e&&e(t)},nw=t=>{t.forEach(nT)},nb={some:0,all:1};class nP extends ia{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:n}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:nb[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;nx.has(i)||nx.set(i,{});let s=nx.get(i),n=JSON.stringify(e);return s[n]||(s[n]=new IntersectionObserver(nw,{root:t,...e})),s[n]}(e);return nv.set(t,i),s.observe(t),()=>{nv.delete(t),s.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),r=e?i:s;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let nS=(0,i8.createContext)({strict:!1});var nA=i(56923);let nM=(0,i8.createContext)({});function nV(t){return n(t.animate)||it.some(e=>e8(t[e]))}function nE(t){return!!(nV(t)||t.variants)}function nD(t){return Array.isArray(t)?t.join(" "):t}var nC=i(49567);let nk={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nR={};for(let t in nk)nR[t]={isEnabled:e=>nk[t].some(t=>!!e[t])};let nj=Symbol.for("motionComponentSymbol");var nL=i(48618),nF=i(32097);function nB(t,{layout:e,layoutId:i}){return j.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!sr[t]||"opacity"===t)}let nO=(t,e)=>e&&"number"==typeof t?e.transform(t):t,nI={...q,transform:Math.round},nU={borderWidth:tu,borderTopWidth:tu,borderRightWidth:tu,borderBottomWidth:tu,borderLeftWidth:tu,borderRadius:tu,radius:tu,borderTopLeftRadius:tu,borderTopRightRadius:tu,borderBottomRightRadius:tu,borderBottomLeftRadius:tu,width:tu,maxWidth:tu,height:tu,maxHeight:tu,top:tu,right:tu,bottom:tu,left:tu,padding:tu,paddingTop:tu,paddingRight:tu,paddingBottom:tu,paddingLeft:tu,margin:tu,marginTop:tu,marginRight:tu,marginBottom:tu,marginLeft:tu,backgroundPositionX:tu,backgroundPositionY:tu,rotate:tl,rotateX:tl,rotateY:tl,rotateZ:tl,scale:Z,scaleX:Z,scaleY:Z,scaleZ:Z,skew:tl,skewX:tl,skewY:tl,distance:tu,translateX:tu,translateY:tu,translateZ:tu,x:tu,y:tu,z:tu,perspective:tu,transformPerspective:tu,opacity:G,originX:tp,originY:tp,originZ:tu,zIndex:nI,fillOpacity:G,strokeOpacity:G,numOctaves:nI},n$={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nN=R.length;function nW(t,e,i){let{style:s,vars:n,transformOrigin:r}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(j.has(t)){o=!0;continue}if(z(t)){n[t]=i;continue}{let e=nO(i,nU[t]);t.startsWith("origin")?(a=!0,r[t]=e):s[t]=e}}if(!e.transform&&(o||i?s.transform=function(t,e,i){let s="",n=!0;for(let r=0;r<nN;r++){let o=R[r],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=nO(a,nU[o]);if(!l){n=!1;let e=n$[o]||o;s+=`${e}(${t}) `}i&&(e[o]=t)}}return s=s.trim(),i?s=i(e,n?"":s):n&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;s.transformOrigin=`${t} ${e} ${i}`}}let nY=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nz(t,e,i){for(let s in e)V(e[s])||nB(s,i)||(t[s]=e[s])}let nH=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function nX(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||nH.has(t)}let nK=t=>!nX(t);try{!function(t){t&&(nK=e=>e.startsWith("on")?!nX(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let n_=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function nq(t){if("string"!=typeof t||t.includes("-"));else if(n_.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}let nG={offset:"stroke-dashoffset",array:"stroke-dasharray"},nZ={offset:"strokeDashoffset",array:"strokeDasharray"};function nQ(t,{attrX:e,attrY:i,attrScale:s,pathLength:n,pathSpacing:r=1,pathOffset:o=0,...a},l,h,u){if(nW(t,a,h),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=u?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==s&&(d.scale=s),void 0!==n&&function(t,e,i=1,s=0,n=!0){t.pathLength=1;let r=n?nG:nZ;t[r.offset]=tu.transform(-s);let o=tu.transform(e),a=tu.transform(i);t[r.array]=`${o} ${a}`}(d,n,r,o,!1)}let nJ=()=>({...nY(),attrs:{}}),n0=t=>"string"==typeof t&&"svg"===t.toLowerCase();var n1=i(53906);let n2=t=>(e,i)=>{let s=(0,i8.useContext)(nM),r=(0,i8.useContext)(nL.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,s,r){return{latestValues:function(t,e,i,s){let r={},a=s(t,{});for(let t in a)r[t]=sc(a[t]);let{initial:l,animate:h}=t,u=nV(t),d=nE(t);e&&d&&!u&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===h&&(h=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===l)?h:l;if(p&&"boolean"!=typeof p&&!n(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let s=o(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(i,s,r,t),renderState:e()}})(t,e,s,r);return i?a():(0,n1.M)(a)};function n5(t,e,i){let{style:s}=t,n={};for(let r in s)(V(s[r])||e.style&&V(e.style[r])||nB(r,t)||i?.getValue(r)?.liveStyle!==void 0)&&(n[r]=s[r]);return n}let n3={useVisualState:n2({scrapeMotionValuesFromProps:n5,createRenderState:nY})};function n9(t,e,i){let s=n5(t,e,i);for(let i in t)(V(t[i])||V(e[i]))&&(s[-1!==R.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}let n4={useVisualState:n2({scrapeMotionValuesFromProps:n9,createRenderState:nJ})},n6={current:null},n8={current:!1},n7=new WeakMap,rt=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),re=t=>/^0[^.\s]+$/u.test(t),ri=t=>e=>e.test(t),rs=[q,tu,th,tl,tc,td,{test:t=>"auto"===t,parse:t=>t}],rn=t=>rs.find(ri(t)),rr=[...rs,tf,tS],ro=t=>rr.find(ri(t)),ra=new Set(["brightness","contrast","saturate","opacity"]);function rl(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(J)||[];if(!s)return t;let n=i.replace(s,""),r=+!!ra.has(e);return s!==i&&(r*=100),e+"("+r+n+")"}let rh=/\b([a-z-]*)\(.*?\)/gu,ru={...tS,getAnimatableNone:t=>{let e=t.match(rh);return e?e.map(rl).join(" "):t}},rd={...nU,color:tf,backgroundColor:tf,outlineColor:tf,fill:tf,stroke:tf,borderColor:tf,borderTopColor:tf,borderRightColor:tf,borderBottomColor:tf,borderLeftColor:tf,filter:ru,WebkitFilter:ru},rc=t=>rd[t];function rp(t,e){let i=rc(t);return i!==ru&&(i=tS),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let rm=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class rf{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:n,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=e$,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=x.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,m.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=r;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=nV(e),this.isVariantNode=nE(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in u){let e=u[t];void 0!==a[t]&&V(e)&&e.set(a[t],!1)}}mount(t){this.current=t,n7.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),n8.current||function(){if(n8.current=!0,nC.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>n6.current=t.matches;t.addListener(e),e()}else n6.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||n6.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),f(this.notifyUpdate),f(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=j.has(t);s&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&m.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in nR){let e=nR[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iV()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<rm.length;e++){let i=rm[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let n=e[s],r=i[s];if(V(n))t.addValue(s,n);else if(V(r))t.addValue(s,M(n,{owner:t}));else if(r!==n)if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(s);t.addValue(s,M(void 0!==e?e:n,{owner:t}))}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=M(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(rt(i)||re(i))?i=parseFloat(i):!ro(i)&&tS.test(e)&&(i=rp(t,e)),this.setBaseTarget(t,V(i)?i.get():i)),V(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let s=o(this.props,i,this.presenceContext?.custom);s&&(e=s[t])}if(i&&void 0!==e)return e;let s=this.getBaseTargetFromProps(this.props,t);return void 0===s||V(s)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new b),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}let ry=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,rg=new Set(["auto","none","0"]);class rv extends e${constructor(t,e,i,s,n){super(t,e,i,s,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&X(s=s.trim())){let n=function t(e,i,s=1){tD(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,r]=function(t){let e=ry.exec(t);if(!e)return[,];let[,i,s,n]=e;return[`--${i??s}`,n]}(e);if(!n)return;let o=window.getComputedStyle(i).getPropertyValue(n);if(o){let t=o.trim();return rt(t)?parseFloat(t):t}return X(r)?t(r,i,s+1):r}(s,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!e5.has(i)||2!==t.length)return;let[s,n]=t,r=rn(s),o=rn(n);if(r!==o)if(eC(r)&&eC(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else ej[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;(null===t[e]||("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||re(s)))&&i.push(e)}i.length&&function(t,e,i){let s,n=0;for(;n<t.length&&!s;){let e=t[n];"string"==typeof e&&!rg.has(e)&&tT(e).values.length&&(s=t[n]),n++}if(s&&i)for(let n of e)t[n]=rp(i,s)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ej[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);let n=i.length-1,r=i[n];i[n]=ej[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}class rx extends rf{constructor(){super(...arguments),this.KeyframeResolver=rv}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;V(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function rT(t,{style:e,vars:i},s,n){for(let r in Object.assign(t.style,e,n&&n.getProjectionStyles(s)),i)t.style.setProperty(r,i[r])}class rw extends rx{constructor(){super(...arguments),this.type="html",this.renderInstance=rT}readValueFromInstance(t,e){if(j.has(e))return eE(t,e);{let i=window.getComputedStyle(t),s=(z(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return iU(t,e)}build(t,e,i){nW(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return n5(t,e,i)}}let rb=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class rP extends rx{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iV}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(j.has(e)){let t=rc(e);return t&&t.default||0}return e=rb.has(e)?e:D(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return n9(t,e,i)}build(t,e,i){nQ(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,s){for(let i in rT(t,e,void 0,s),e.attrs)t.setAttribute(rb.has(i)?i:D(i),e.attrs[i])}mount(t){this.isSVGTag=n0(t.tagName),super.mount(t)}}let rS=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}((i9={animation:{Feature:il},exit:{Feature:iu},inView:{Feature:nP},tap:{Feature:ng},focus:{Feature:nl},hover:{Feature:na},pan:{Feature:i3},drag:{Feature:i2,ProjectionNode:ns,MeasureLayout:sl},layout:{ProjectionNode:ns,MeasureLayout:sl}},i4=(t,e)=>nq(t)?new rP(e):new rw(e,{allowProjection:t!==i8.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:s,Component:n}){function r(t,r){var o,a,l;let h,u={...(0,i8.useContext)(nA.Q),...t,layoutId:function({layoutId:t}){let e=(0,i8.useContext)(st.L).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:d}=u,c=function(t){let{initial:e,animate:i}=function(t,e){if(nV(t)){let{initial:e,animate:i}=t;return{initial:!1===e||e8(e)?e:void 0,animate:e8(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,i8.useContext)(nM));return(0,i8.useMemo)(()=>({initial:e,animate:i}),[nD(e),nD(i)])}(t),p=s(t,d);if(!d&&nC.B){a=0,l=0,(0,i8.useContext)(nS).strict;let t=function(t){let{drag:e,layout:i}=nR;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:e?.isEnabled(t)||i?.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(u);h=t.MeasureLayout,c.visualElement=function(t,e,i,s,n){let{visualElement:r}=(0,i8.useContext)(nM),o=(0,i8.useContext)(nS),a=(0,i8.useContext)(nL.t),l=(0,i8.useContext)(nA.Q).reducedMotion,h=(0,i8.useRef)(null);s=s||o.renderer,!h.current&&s&&(h.current=s(t,{visualState:e,parent:r,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let u=h.current,d=(0,i8.useContext)(se);u&&!u.projection&&n&&("html"===u.type||"svg"===u.type)&&function(t,e,i,s){let{layoutId:n,layout:r,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:r,alwaysMeasureLayout:!!o||a&&iN(a),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:s,crossfade:u,layoutScroll:l,layoutRoot:h})}(h.current,i,n,d);let c=(0,i8.useRef)(!1);(0,i8.useInsertionEffect)(()=>{u&&c.current&&u.update(i,a)});let p=i[C],m=(0,i8.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,nF.E)(()=>{u&&(c.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),so.render(u.render),m.current&&u.animationState&&u.animationState.animateChanges())}),(0,i8.useEffect)(()=>{u&&(!m.current&&u.animationState&&u.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),u}(n,p,u,e,t.ProjectionNode)}return(0,i6.jsxs)(nM.Provider,{value:c,children:[h&&c.visualElement?(0,i6.jsx)(h,{visualElement:c.visualElement,...u}):null,i(n,t,(o=c.visualElement,(0,i8.useCallback)(t=>{t&&p.onMount&&p.onMount(t),o&&(t?o.mount(t):o.unmount()),r&&("function"==typeof r?r(t):iN(r)&&(r.current=t))},[o])),p,d,c.visualElement)]})}t&&function(t){for(let e in t)nR[e]={...nR[e],...t[e]}}(t),r.displayName=`motion.${"string"==typeof n?n:`create(${n.displayName??n.name??""})`}`;let o=(0,i8.forwardRef)(r);return o[nj]=n,o}({...nq(t)?n4:n3,preloadedFeatures:i9,useRender:function(t=!1){return(e,i,s,{latestValues:n},r)=>{let o=(nq(e)?function(t,e,i,s){let n=(0,i8.useMemo)(()=>{let i=nJ();return nQ(i,e,n0(s),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};nz(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return nz(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,i8.useMemo)(()=>{let i=nY();return nW(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,n,r,e),a=function(t,e,i){let s={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(nK(n)||!0===i&&nX(n)||!e&&!nX(n)||t.draggable&&n.startsWith("onDrag"))&&(s[n]=t[n]);return s}(i,"string"==typeof e,t),l=e!==i8.Fragment?{...a,...o,ref:s}:{},{children:h}=i,u=(0,i8.useMemo)(()=>V(h)?h.get():h,[h]);return(0,i8.createElement)(e,{...l,children:u})}}(e),createVisualElement:i4,Component:t})}))},93905:(t,e,i)=>{i.d(e,{xQ:()=>r});var s=i(43210),n=i(48618);function r(t=!0){let e=(0,s.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,s.useId)();(0,s.useEffect)(()=>{if(t)return a(l)},[t]);let h=(0,s.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,h]:[!0]}}};