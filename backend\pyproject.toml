[project]
name = "backend"
version = "0.1.0"
description = ""
authors = [
    {name = "<PERSON><PERSON><PERSON>",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10,<3.14"
dependencies = [
    "fastapi (>=0.115.12,<0.116.0)",
    "uvicorn[standard] (>=0.34.2,<0.35.0)",
    "python-dotenv (>=1.1.0,<2.0.0)",
    "ag2[openai] (>=0.9,<0.10)",
    "pandas (>=2.2.3,<3.0.0)",
    "openai (>=1.12.0,<2.0.0)",
    "nixtla (>=0.1.0,<0.2.0)",
    "python-dotenv (>=1.0.0,<2.0.0)"
]

[tool.poetry]
package-mode = false


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
