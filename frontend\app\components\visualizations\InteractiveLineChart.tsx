'use client';

import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceArea,
  ReferenceDot,
  Legend
} from 'recharts';
import { useChat } from '../../contexts/ChatContext';
import { ArrowPathIcon, XMarkIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';

// Types
interface DataPoint {
  name: string;
  value: number;
  forecast?: number | null;
  arimaForecast?: number | null;
  emaForecast?: number | null;
  date?: string;
}

interface ChartState {
  selection: {
    start: string | null;
    end: string | null;
    isSelecting: boolean;
  };
  zoom: {
    xMin: number;
    xMax: number;
    yMin: number;
    yMax: number;
  };
}

// Sample data generation
const generateData = (): DataPoint[] => {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  return months.map((month, i) => {
    const baseValue = 500 + Math.sin(i / 3) * 300;
    const variation = Math.sin(i / 2) * 200;
    const noise = Math.random() * 150 - 75;
    return {
      name: month,
      value: Math.max(0, Math.round(baseValue + variation + noise))
    };
  });
};

// Generate time series data
const generateTimeSeriesData = (): DataPoint[] => {
  const data = [];
  const now = new Date();
  
  for (let i = 0; i < 100; i++) {
    const date = new Date(now);
    date.setDate(now.getDate() - (100 - i));
    const dateStr = date.toISOString().split('T')[0];
    
    // Generate base value with trend and seasonality
    const trend = i * 0.5; // Upward trend
    const seasonality = Math.sin(i / 10) * 20; // Seasonal pattern
    const noise = Math.random() * 10 - 5; // Random noise
    const value = 100 + trend + seasonality + noise;
    
    // Generate forecasts for different models
    // Main forecast (slightly optimistic)
    const forecast = i > 70 ? value * 1.05 + (Math.random() * 5 - 2.5) : null;
    
    // ARIMA-like forecast (more conservative, follows trend)
    const arimaForecast = i > 70 ? 
      value * 0.92 + (trend * 0.7) + (Math.random() * 3 - 1.5) : null;
    
    // EMA-like forecast (smoother, more responsive to recent changes)
    const emaForecast = i > 70 ? 
      value * 0.95 + (seasonality * 1.2) + (Math.random() * 4 - 2) : null;
    
    data.push({
      name: dateStr,
      date: dateStr,
      value: value,
      forecast: forecast,
      arimaForecast: arimaForecast,
      emaForecast: emaForecast
    });
  }
  
  return data;
};

// Custom tooltip component
const CustomTooltip = ({ active, payload, label, selectedPoint, isSelected }: any) => {
  if (!active || !payload?.length) return null;

  // Calculate if this is the selected point
  const isCurrentPointSelected = isSelected && 
    selectedPoint?.name === label && 
    selectedPoint?.value === payload[0].value;

  return (
    <div className={`p-2 rounded border shadow-lg text-xs transition-all duration-150 ${
      isCurrentPointSelected 
        ? "bg-indigo-900 border-indigo-500 text-white scale-110" 
        : "bg-black/80 border-white/20 text-white"
    }`}>
      <p className="font-bold">{label}</p>
      {payload.map((entry: any, index: number) => (
        <p key={`value-${index}`} className={isCurrentPointSelected && index === 0 ? "text-indigo-300" : `text-[${entry.color}]`}>
          {entry.name}: {entry.value?.toFixed(2)}
        </p>
      ))}
      {isCurrentPointSelected && (
        <div className="mt-1 text-xs text-indigo-200">Selected</div>
      )}
    </div>
  );
};

const InteractiveLineChart = () => {
  // Get chart data and selection functions from context
  const { chartData, setChartSelection, chartSelection } = useChat();
  
  // Store the selected point in local state
  const [selectedPoint, setSelectedPoint] = useState<DataPoint | null>(null);
  
  // State for showing comparison models
  const [showComparison, setShowComparison] = useState(false);

  // Add key state for animation
  const [animationKey, setAnimationKey] = useState(0);
  const [comparisonKey, setComparisonKey] = useState(0);

  // State to store the actual data used for rendering
  const [data, setData] = useState(() => {
    return chartData || generateTimeSeriesData();
  });

  // Add state for drawing animation
  const [actualDrawingProgress, setActualDrawingProgress] = useState(0);
  const [forecastDrawingProgress, setForecastDrawingProgress] = useState(0);

  const chartRef = useRef<any>(null);

  // Update data when chartData changes
  useEffect(() => {
    if (chartData) {
      // Only restart animation if it's a completely new dataset
      // or if we're switching from sample data to real data
      // const shouldAnimate = !data || 
      //   (data.length === 0 && chartData.length > 0) 
        // ||(data[0]?.name !== chartData[0]?.name); // Check if it's a different dataset
      const shouldAnimate = true;

      setData(chartData);
      
      if (shouldAnimate) {
        setAnimationKey(prev => prev + 1);
      }
    }
  }, [chartData, data]);

  // Update comparison key when showComparison changes
  useEffect(() => {
    setComparisonKey(prev => prev + 1);
  }, [showComparison]);

  // Sync local selected point with context
  useEffect(() => {
    if (chartSelection?.point) {
      setSelectedPoint(chartSelection.point);
    } else {
      setSelectedPoint(null);
    }
  }, [chartSelection]);

  // Chart state
  const [state, setState] = useState<ChartState>({
    selection: {
      start: null,
      end: null,
      isSelecting: false
    },
    zoom: {
      xMin: 0,
      xMax: data.length - 1,
      yMin: 0,
      yMax: Math.max(...data.map((d: any) => d.value)) + 100
    }
  });

  // Calculate visible data - show all data instead of zoomed subset
  const visibleData = useMemo(() => {
    return data;
  }, [data]);

  // For performance, memoize the highlighted data
  const transformedData = useMemo(() => {
    if (!selectedPoint) return visibleData;
    return visibleData.map((point: any) => ({
      ...point,
      _selected: point.name === selectedPoint.name && point.value === selectedPoint.value
    }));
  }, [visibleData, selectedPoint]);

  // Calculate Y domain for selected range - no longer used for zooming
  const calculateYDomain = useCallback((start: string, end: string): [number, number] => {
    // We'll keep this function for future use, but won't use it for zooming
    const startIdx = data.findIndex((d: any) => d.name === start);
    const endIdx = data.findIndex((d: any) => d.name === end);

    if (startIdx === -1 || endIdx === -1) return [0, 1000];

    const minIdx = Math.min(startIdx, endIdx);
    const maxIdx = Math.max(startIdx, endIdx);
    const slice = data.slice(minIdx, maxIdx + 1);

    // @ts-ignore
    const min = Math.min(...slice.map((d: any) => d.value));
    // @ts-ignore
    const max = Math.max(...slice.map((d: any) => d.value));
    const padding = (max - min) * 0.1;

    return [Math.floor(min - padding), Math.ceil(max + padding)];
  }, [data]);

  // Effect for drawing animation
  useEffect(() => {
    setActualDrawingProgress(0);
    setForecastDrawingProgress(0);
    
    const duration = 1500; // 1.5 seconds for each line
    const startTime = Date.now();
    
    const animate = () => {
      const currentTime = Date.now();
      const elapsed = currentTime - startTime;
      
      // Animate actual line first
      const actualProgress = Math.min(elapsed / duration, 1);
      setActualDrawingProgress(actualProgress);
      
      // Start forecast animation after actual line is done
      if (actualProgress >= 1) {
        const forecastElapsed = elapsed - duration;
        const forecastProgress = Math.min(forecastElapsed / duration, 1);
        setForecastDrawingProgress(forecastProgress);
      }
      
      if (actualProgress < 1 || forecastDrawingProgress < 1) {
        requestAnimationFrame(animate);
      }
    };
    
    requestAnimationFrame(animate);
  }, [data]); // Re-run animation when data changes

  // Calculate dash array and offset for drawing animation
  const getDrawingProps = (length: number, progress: number) => {
    const dashLength = length * progress;
    return {
      strokeDasharray: length,
      strokeDashoffset: length - dashLength
    };
  };

  // Event handlers for selection
  const handleMouseDown = useCallback((e: any) => {
    if (!e?.activeLabel) return;

    setState(prev => ({
      ...prev,
      selection: {
        start: e.activeLabel,
        end: null,
        isSelecting: true
      }
    }));
  }, []);

  const handleMouseMove = useCallback((e: any) => {
    if (!state.selection.isSelecting || !e?.activeLabel) return;

    setState(prev => ({
      ...prev,
      selection: {
        ...prev.selection,
        end: e.activeLabel
      }
    }));
  }, [state.selection.isSelecting]);

  const handleMouseUp = useCallback(() => {
    setState((prev: any) => {
      if (!prev.selection.start || !prev.selection.end) {
        return {
          ...prev,
          selection: {
            start: null,
            end: null,
            isSelecting: false
          }
        };
      }

      // Get the selected range values
      const startIdx = data.findIndex((d: any) => d.name === prev.selection.start);
      const endIdx = data.findIndex((d: any) => d.name === prev.selection.end);

      if (startIdx !== -1 && endIdx !== -1) {
        // Check if this is a single point selection (start equals end)
        if (prev.selection.start === prev.selection.end) {
          // Handle as a point selection instead of a range
          const point = data[startIdx];
          
          // Update the context with just the point
          setTimeout(() => {
            setChartSelection({
              point: {
                name: point.name,
                value: point.value
              }
            });
          }, 0);
        } else {
          // It's a proper range, store selection info
          const selectionInfo = {
            start: prev.selection.start,
            end: prev.selection.end
          };
          
          // Update the context with the range
          setTimeout(() => {
            setChartSelection({
              range: selectionInfo,
              // Keep the point selection
              point: selectedPoint
            });
          }, 0);
        }
      }

      // Return state without changing zoom
      return {
        ...prev,
        selection: {
          ...prev.selection,
          isSelecting: false
        }
      };
    });
  }, [data, setChartSelection, selectedPoint]);

  // Handle point click
  const handlePointClick = useCallback((data: any) => {
    const point = data.payload as DataPoint;
    
    // If clicking on the already selected point, deselect it
    if (selectedPoint?.name === point.name && selectedPoint?.value === point.value) {
      setSelectedPoint(null);
      // @ts-ignore
      setChartSelection((prev: any) => ({
        ...prev,
        point: undefined
      }));
      return;
    }
    
    // Otherwise, select the point
    setSelectedPoint(point);
    
    // Update the context, preserving any range selection
    // @ts-ignore
    setChartSelection((prev: any) => ({
      ...prev,
      point: {
        name: point.name,
        value: point.value
      }
    }));
  }, [selectedPoint, setChartSelection]);

  // Reset view - no longer changes zoom
  const resetView = useCallback(() => {
    // Clear the selection in the context
    setChartSelection({});
    
    // Clear local state
    setSelectedPoint(null);

    setState({
      selection: {
        start: null,
        end: null,
        isSelecting: false
      },
      zoom: {
        xMin: 0,
        xMax: data.length - 1,
        yMin: 0,
        yMax: Math.max(...data.map((d: any) => d.value)) + 100
      }
    });
  }, [data, setChartSelection]);

  // Clear selection only
  const clearSelection = useCallback(() => {
    // Clear the selection in the context
    setChartSelection({});
    
    // Clear local state
    setSelectedPoint(null);
    
    // Reset selection state but keep zoom
    setState(prev => ({
      ...prev,
      selection: {
        start: null,
        end: null,
        isSelecting: false
      }
    }));
  }, [setChartSelection]);

  // Dot renderer with conditional styling based on selection
  const renderDot = useCallback((props: any) => {
    const { cx, cy, payload, index } = props;
    
    // Don't render dot if value is not available
    if (payload.value === undefined || payload.value === null) {
      return null;
    }
    
    // Check if this point is selected
    const isPointSelected = selectedPoint?.name === payload.name && 
                           selectedPoint?.value === payload.value;
    
    if (isPointSelected) {
      return (
        <circle 
          key={`dot-${index}-${payload.name}`}
          cx={cx} 
          cy={cy} 
          r={6} 
          fill="#4f46e5" 
          stroke="#ffffff" 
          strokeWidth={2}
        />
      );
    }
    
    // Default dot
    return (
      <circle 
        key={`dot-${index}-${payload.name}`}
        cx={cx} 
        cy={cy} 
        r={2} 
        fill="#8884d8" 
        stroke="#8884d8" 
        strokeWidth={1}
      />
    );
  }, [selectedPoint]);

  // Custom active dot component
  const renderActiveDot = useCallback((props: any) => {
    const { cx, cy, stroke, payload, index, dataKey } = props;
    
    // Don't render active dot if value is not available
    if (payload.value === undefined || payload.value === null) {
      return null;
    }
    
    // Check if this point is selected
    const isPointSelected = selectedPoint?.name === payload.name && 
                           selectedPoint?.value === payload.value;
    
    return (
      <circle 
        key={`active-dot-${index}-${payload.name}`}
        cx={cx} 
        cy={cy} 
        r={isPointSelected ? 8 : 6} 
        stroke={isPointSelected ? "#ffffff" : "white"} 
        strokeWidth={isPointSelected ? 3 : 2} 
        fill={isPointSelected ? "#4f46e5" : "#ff7300"} 
        style={{ cursor: 'pointer' }}
        onClick={() => handlePointClick(props)}
      />
    );
  }, [selectedPoint, handlePointClick]);

  // ARIMA dot renderer
  const renderArimaDot = useCallback((props: any) => {
    const { cx, cy, payload, index } = props;
    
    // Don't render dot if ARIMA forecast is not available
    if (payload.arimaForecast === undefined || payload.arimaForecast === null) {
      return null;
    }
    
    // Check if this point is selected
    const isPointSelected = selectedPoint?.name === payload.name && 
                           selectedPoint?.value === payload.arimaForecast;
    
    if (isPointSelected) {
      return (
        <circle 
          key={`arima-dot-${index}-${payload.name}`}
          cx={cx} 
          cy={cy} 
          r={6} 
          fill="#ff7300" 
          stroke="#ffffff" 
          strokeWidth={2}
        />
      );
    }
    
    // Default ARIMA dot
    return (
      <circle 
        key={`arima-dot-${index}-${payload.name}`}
        cx={cx} 
        cy={cy} 
        r={2} 
        fill="#ff7300" 
        stroke="#ff7300" 
        strokeWidth={1}
      />
    );
  }, [selectedPoint]);

  // ARIMA active dot component
  const renderArimaActiveDot = useCallback((props: any) => {
    const { cx, cy, stroke, payload, index } = props;
    
    // Don't render active dot if ARIMA forecast is not available
    if (payload.arimaForecast === undefined || payload.arimaForecast === null) {
      return null;
    }
    
    // Check if this point is selected
    const isPointSelected = selectedPoint?.name === payload.name && 
                           selectedPoint?.value === payload.arimaForecast;
    
    return (
      <circle 
        key={`arima-active-dot-${index}-${payload.name}`}
        cx={cx} 
        cy={cy} 
        r={isPointSelected ? 8 : 6} 
        stroke={isPointSelected ? "#ffffff" : "white"} 
        strokeWidth={isPointSelected ? 3 : 2} 
        fill={isPointSelected ? "#ff7300" : "#ff7300"} 
        style={{ cursor: 'pointer' }}
        onClick={() => handlePointClick({...props, payload: {...props.payload, value: props.payload.arimaForecast}})}
      />
    );
  }, [selectedPoint, handlePointClick]);

  // EMA dot renderer
  const renderEmaDot = useCallback((props: any) => {
    const { cx, cy, payload, index } = props;
    
    // Don't render dot if EMA forecast is not available
    if (payload.emaForecast === undefined || payload.emaForecast === null) {
      return null;
    }
    
    // Check if this point is selected
    const isPointSelected = selectedPoint?.name === payload.name && 
                           selectedPoint?.value === payload.emaForecast;
    
    if (isPointSelected) {
      return (
        <circle 
          key={`ema-dot-${index}-${payload.name}`}
          cx={cx} 
          cy={cy} 
          r={6} 
          fill="#0088fe" 
          stroke="#ffffff" 
          strokeWidth={2}
        />
      );
    }
    
    // Default EMA dot
    return (
      <circle 
        key={`ema-dot-${index}-${payload.name}`}
        cx={cx} 
        cy={cy} 
        r={2} 
        fill="#0088fe" 
        stroke="#0088fe" 
        strokeWidth={1}
      />
    );
  }, [selectedPoint]);

  // EMA active dot component
  const renderEmaActiveDot = useCallback((props: any) => {
    const { cx, cy, stroke, payload, index } = props;
    
    // Don't render active dot if EMA forecast is not available
    if (payload.emaForecast === undefined || payload.emaForecast === null) {
      return null;
    }
    
    // Check if this point is selected
    const isPointSelected = selectedPoint?.name === payload.name && 
                           selectedPoint?.value === payload.emaForecast;
    
    return (
      <circle 
        key={`ema-active-dot-${index}-${payload.name}`}
        cx={cx} 
        cy={cy} 
        r={isPointSelected ? 8 : 6} 
        stroke={isPointSelected ? "#ffffff" : "white"} 
        strokeWidth={isPointSelected ? 3 : 2} 
        fill={isPointSelected ? "#0088fe" : "#0088fe"} 
        style={{ cursor: 'pointer' }}
        onClick={() => handlePointClick({...props, payload: {...props.payload, value: props.payload.emaForecast}})}
      />
    );
  }, [selectedPoint, handlePointClick]);

  // Forecast dot renderer
  const renderForecastDot = useCallback((props: any) => {
    const { cx, cy, payload, index } = props;
    
    // Don't render dot if forecast value is not available
    if (payload.forecast === undefined || payload.forecast === null) {
      return null;
    }
    
    // Check if this point is selected
    const isPointSelected = selectedPoint?.name === payload.name && 
                           selectedPoint?.value === payload.forecast;
    
    if (isPointSelected) {
      return (
        <circle 
          key={`forecast-dot-${index}-${payload.name}`}
          cx={cx} 
          cy={cy} 
          r={6} 
          fill="#82ca9d" 
          stroke="#ffffff" 
          strokeWidth={2}
        />
      );
    }
    
    // Default forecast dot
    return (
      <circle 
        key={`forecast-dot-${index}-${payload.name}`}
        cx={cx} 
        cy={cy} 
        r={2} 
        fill="#82ca9d" 
        stroke="#82ca9d" 
        strokeWidth={1}
      />
    );
  }, [selectedPoint]);

  // Forecast active dot component
  const renderForecastActiveDot = useCallback((props: any) => {
    const { cx, cy, stroke, payload, index } = props;
    
    // Don't render active dot if forecast value is not available
    if (payload.forecast === undefined || payload.forecast === null) {
      return null;
    }
    
    // Check if this point is selected
    const isPointSelected = selectedPoint?.name === payload.name && 
                           selectedPoint?.value === payload.forecast;
    
    return (
      <circle 
        key={`forecast-active-dot-${index}-${payload.name}`}
        cx={cx} 
        cy={cy} 
        r={isPointSelected ? 8 : 6} 
        stroke={isPointSelected ? "#ffffff" : "white"} 
        strokeWidth={isPointSelected ? 3 : 2} 
        fill={isPointSelected ? "#82ca9d" : "#82ca9d"} 
        style={{ cursor: 'pointer' }}
        onClick={() => handlePointClick({...props, payload: {...props.payload, value: props.payload.forecast}})}
      />
    );
  }, [selectedPoint, handlePointClick]);

  // Format date for X-axis
  const formatXAxis = (tickItem: string) => {
    // Check if it's a date string (YYYY-MM-DD format)
    if (/^\d{4}-\d{2}-\d{2}$/.test(tickItem)) {
      const date = new Date(tickItem);
      return `${date.getMonth() + 1}/${date.getDate()}`;
    }
    return tickItem; // Return as is if not a date
  };

  return (
    <div className="glass-card flex-1 p-4 m-1 flex flex-col overflow-hidden h-full">
      {/* Header */}
      <div className="flex justify-between items-center mb-2 text-white px-1">
        <h4 className="text-base font-semibold font-inknut text-red-400">
        <span className='text-white/60'>Time Series </span>{chartData ? 'AI Generated Chart' : "Forecast"}
        </h4>
        <div className="flex items-center space-x-2">
          {selectedPoint && (
            <div className="bg-indigo-900/80 px-3 py-1 rounded border border-indigo-600/40 text-white text-xs">
              <span className="font-medium text-indigo-300">{selectedPoint.name}:</span> 
              <span className="ml-1">{typeof selectedPoint.value === 'number' ? selectedPoint.value.toFixed(2) : selectedPoint.value}</span>
            </div>
          )}
          <button
            type="button"
            onClick={() => setShowComparison(!showComparison)}
            className={`p-1.5 rounded bg-white/5 hover:bg-white/10 text-white transition-all duration-200 shadow hover:scale-110 hover:shadow-lg group relative border border-white/10 backdrop-blur-sm ${showComparison ? 'bg-white/10' : ''}`}
          >
            {showComparison ? (
              <EyeIcon className="w-4 h-4" />
            ) : (
              <EyeSlashIcon className="w-4 h-4" />
            )}
            <span className="absolute top-full mt-1 left-1/2 -translate-x-1/2 bg-gray-800/90 backdrop-blur-sm text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap border border-white/10">
              {showComparison ? 'Show Main Forecast' : 'Show Comparison Models'}
            </span>
          </button>
          <button
            type="button"
            onClick={clearSelection}
            className="p-1.5 rounded bg-white/5 hover:bg-white/10 text-white transition-all duration-200 shadow hover:scale-110 hover:shadow-lg group relative border border-white/10 backdrop-blur-sm"
          >
            <XMarkIcon className="w-4 h-4" />
            <span className="absolute top-full mt-1 left-1/2 -translate-x-1/2 bg-gray-800/90 backdrop-blur-sm text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap border border-white/10">
              Clear Selection
            </span>
          </button>
          <button
            type="button"
            onClick={resetView}
            className="p-1.5 rounded bg-white/5 hover:bg-white/10 text-white transition-all duration-200 shadow hover:scale-110 hover:shadow-lg group relative border border-white/10 backdrop-blur-sm"
          >
            <ArrowPathIcon className="w-4 h-4" />
            <span className="absolute top-full mt-1 left-1 -translate-x-1/2 bg-gray-800/90 backdrop-blur-sm text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap border border-white/10">
              Reset View
            </span>
          </button>
        </div>
      </div>

      {/* Instructions */}
      <div className="text-xs text-white/70 px-1 mb-2">
        Click on a point to select it, or click and drag to select a range.
      </div>

      {/* Chart Area */}
      <div className="flex-1 overflow-hidden select-none touch-none" onContextMenu={(e) => e.preventDefault()}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            key="main-chart"
            ref={chartRef}
            data={transformedData}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            margin={{ top: 5, right: 20, left: 10, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.15)" />
            <XAxis
              dataKey="name"
              type="category"
              tick={{ fill: 'rgba(255, 255, 255, 0.7)', fontSize: 10 }}
              stroke="rgba(255, 255, 255, 0.3)"
              tickFormatter={formatXAxis}
            />
            <YAxis
              type="number"
              domain={['auto', 'auto']}
              tick={{ fill: 'rgba(255, 255, 255, 0.7)', fontSize: 10 }}
              stroke="rgba(255, 255, 255, 0.3)"
              allowDecimals={false}
              width={50}
            />
            <Tooltip 
              content={<CustomTooltip selectedPoint={selectedPoint} isSelected={!!selectedPoint} />}
              wrapperStyle={{ zIndex: 1000 }}
            />
            <Legend 
              verticalAlign="top" 
              height={36}
              wrapperStyle={{
                paddingTop: '10px',
                fontSize: '12px',
                color: 'rgba(255, 255, 255, 0.7)'
              }}
            />
            
            {/* Main data line */}
            <Line
              type="monotone"
              dataKey="value"
              stroke="#8884d8"
              strokeWidth={2}
              name="Actual"
              // @ts-ignore
              dot={renderDot}
              // @ts-ignore
              activeDot={renderActiveDot}
              isAnimationActive={true}
              animationDuration={1000}
              animationEasing="ease-in-out"
              key={`actual-line-${animationKey}`}
            />
            
            {/* Kairos forecast */}
            <Line
              type="monotone"
              dataKey="forecast"
              stroke="#82ca9d"
              strokeWidth={showComparison ? 2 : 2}
              name="Kairos Forecast"
              // @ts-ignore
              dot={renderForecastDot}
              // @ts-ignore
              activeDot={renderForecastActiveDot}
              isAnimationActive={true}
              animationDuration={1000}
              animationBegin={animationKey !== comparisonKey ? 1000 : 0}
              animationEasing="ease-in-out"
              key={`forecast-line-${animationKey}-${comparisonKey}`}
              connectNulls={true}
            />
            
            {/* ARIMA forecast - only shown in comparison mode */}
            {showComparison && (
              <Line 
                type="monotone" 
                dataKey="arimaForecast" 
                stroke="#ff7300" 
                name="ARIMA" 
                strokeWidth={2}
                // @ts-ignore
                dot={renderArimaDot}
                // @ts-ignore
                activeDot={renderArimaActiveDot}
                isAnimationActive={true}
                animationDuration={1000}
                animationBegin={animationKey !== comparisonKey ? 1000 : 0}
                animationEasing="ease-in-out"
                key={`arima-forecast-line-${animationKey}-${comparisonKey}`}
                connectNulls={true}
              />
            )}
            
            {/* EMA forecast - only shown in comparison mode */}
            {showComparison && (
              <Line 
                type="monotone" 
                dataKey="emaForecast" 
                stroke="#0088fe" 
                name="EMA" 
                strokeWidth={2}
                // @ts-ignore
                dot={renderEmaDot}
                // @ts-ignore
                activeDot={renderEmaActiveDot}
                isAnimationActive={true}
                animationDuration={1000}
                animationBegin={animationKey !== comparisonKey ? 1000 : 0}
                animationEasing="ease-in-out"
                key={`ema-forecast-line-${animationKey}-${comparisonKey}`}
                connectNulls={true}
              />
            )}

            {/* Selection area */}
            {state.selection.start && state.selection.end && (
              <ReferenceArea
                x1={state.selection.start}
                x2={state.selection.end}
                stroke="rgba(255, 115, 0, 0.7)"
                fill="rgba(255, 115, 0, 0.2)"
                strokeOpacity={1}
              />
            )}
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default InteractiveLineChart;
