import React from 'react';

interface FormModalProps {
  formFields: { key: string; value: string }[];
  onFieldChange: (index: number, field: 'key' | 'value', value: string) => void;
  onClose: () => void;
  onSubmit: () => void;
  inSidebar?: boolean;
}

const FormModal: React.FC<FormModalProps> = ({
  formFields,
  onFieldChange,
  onClose,
  onSubmit,
  inSidebar = false
}) => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop with blur */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={onClose}
      ></div>

      {/* Modal content */}
      <div className="relative z-10 glass-card w-full max-w-md p-6 border-t-2 border-r-2 border-kairosYellow shadow-[0_0_15px_rgba(255,246,141,0.5)]">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-medium text-kairosYellow">Key-Value Pairs</h3>
          <button
            type="button"
            aria-label="Close"
            onClick={onClose}
            className="text-gray-400 hover:text-kairosYellow"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Form fields */}
        <div className="space-y-4">
          {formFields.map((field, index) => (
            <div key={index} className="flex space-x-2">
              <div className="flex-1">
                <label className="block text-gray-400 text-xs mb-1">Key</label>
                <input
                  type="text"
                  value={field.key}
                  onChange={(e) => onFieldChange(index, 'key', e.target.value)}
                  className="w-full bg-gray-800/50 border border-gray-700 rounded px-3 py-2 text-sm text-gray-200 placeholder-gray-500 focus:outline-none focus:border-kairosYellow"
                  placeholder="Enter key"
                />
              </div>
              <div className="flex-1">
                <label className="block text-gray-400 text-xs mb-1">Value</label>
                <input
                  type="text"
                  value={field.value}
                  onChange={(e) => onFieldChange(index, 'value', e.target.value)}
                  className="w-full bg-gray-800/50 border border-gray-700 rounded px-3 py-2 text-sm text-gray-200 placeholder-gray-500 focus:outline-none focus:border-kairosYellow"
                  placeholder="Enter value"
                />
              </div>
            </div>
          ))}
        </div>

        {/* Action buttons */}
        <div className="flex justify-end mt-6 space-x-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-600 text-gray-300 rounded hover:bg-black/30 transition-colors"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={onSubmit}
            className="px-4 py-2 rounded font-medium bg-kairosGreen text-black hover:bg-opacity-80 transition-colors"
          >
            Submit
          </button>
        </div>
      </div>
    </div>
  );
};

export default FormModal;

