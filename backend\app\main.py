import asyncio
from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException, WebSocket, WebSocketDisconnect, Query, APIRouter, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import shutil
from pathlib import Path
import uuid
import logging
import traceback
from datetime import datetime

from .team import DynamicWorkflowTeam
from .models import Message, ChatMessage, ChatHistory
import json
from app.stories import stories
import string
import random
import os
import pickle
import smtplib
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from pydantic import BaseModel, EmailStr


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = FastAPI()

# Create required directories
UPLOAD_DIR = Path("uploads")
LOGS_DIR = Path("logs")
UPLOAD_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)


# Load chat histories on startup
@app.on_event("startup")
async def startup_event():
    global chat_histories
    logger.info("Loading chat histories from disk...")
    chat_histories = load_chat_histories()
    logger.info(f"Loaded {len(chat_histories)} chat histories")


team = DynamicWorkflowTeam()

# Store active WebSocket connections
active_connections: list[WebSocket] = []

# Store WebSocket connections by slug
slug_connections: dict[str, list[WebSocket]] = {}

# Directory for storing chat histories
CHAT_HISTORY_DIR = Path("chat_histories")
CHAT_HISTORY_DIR.mkdir(exist_ok=True)

# Store chat histories by slug
chat_histories = {}

def generate_slug(length=8):
    """Generate a random slug for chat identification."""
    characters = string.ascii_lowercase + string.digits
    return ''.join(random.choice(characters) for _ in range(length))

def save_chat_history(slug: str, history: ChatHistory):
    """Save chat history to disk."""
    try:
        file_path = CHAT_HISTORY_DIR / f"{slug}.json"
        with open(file_path, "w") as f:
            # Convert to dict for JSON serialization
            history_dict = {
                "messages": [msg.model_dump() for msg in history.messages]
            }
            json.dump(history_dict, f, indent=2)
        logger.info(f"Saved chat history for slug: {slug}")
    except Exception as e:
        logger.error(f"Error saving chat history for slug {slug}: {str(e)}")

def load_chat_histories():
    """Load all chat histories from disk."""
    try:
        histories = {}
        for file_path in CHAT_HISTORY_DIR.glob("*.json"):
            slug = file_path.stem
            try:
                with open(file_path, "r") as f:
                    history_dict = json.load(f)
                    # Convert back to ChatHistory object
                    history = ChatHistory()
                    for msg_dict in history_dict.get("messages", []):
                        history.messages.append(ChatMessage(**msg_dict))
                    histories[slug] = history
                    logger.info(f"Loaded chat history for slug: {slug}")
            except Exception as e:
                logger.error(f"Error loading chat history for slug {slug}: {str(e)}")
        return histories
    except Exception as e:
        logger.error(f"Error loading chat histories: {str(e)}")
        return {}

@app.get("/")
async def root():
    return {"message": "Hello World from REST API!"}

# test websocket connection
@app.websocket("/")
async def test_websocket(websocket: WebSocket):
    await websocket.accept()
    await websocket.send_text("Hello World from WebSocket!")
    await websocket.close()



def send_notification_email(user_email: str):
    """Send email notification when someone joins the waitlist"""
    try:
        # Email configuration - you'll need to set these environment variables
        smtp_server = os.getenv("SMTP_SERVER", "smtp.gmail.com")
        smtp_port = int(os.getenv("SMTP_PORT", "587"))
        sender_email = os.getenv("SENDER_EMAIL", "")
        sender_password = os.getenv("SENDER_PASSWORD", "")
        notification_email = os.getenv("NOTIFICATION_EMAIL", "")

        if not all([sender_email, sender_password, notification_email]):
            print("Email configuration missing, skipping notification")
            return

        # Create message
        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = notification_email
        msg['Subject'] = "New Kairos AI Waitlist Signup"

        body = f"""
        New user joined the Kairos AI waitlist!

        Email: {user_email}
        Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

        Best regards,
        Kairos AI System
        """

        msg.attach(MIMEText(body, 'plain'))

        # Send email
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(sender_email, sender_password)
        text = msg.as_string()
        server.sendmail(sender_email, notification_email, text)
        server.quit()

        print(f"Notification email sent for new signup: {user_email}")

    except Exception as e:
        print(f"Failed to send notification email: {str(e)}")

@app.post("/waitlist")
async def waitlist(request: Request):
    try:
        data = await request.json()
        email = data.get("email")
        if not email:
            raise HTTPException(status_code=400, detail="Missing email")

        # Create the file if it doesn't exist and append the email
        with open("waitlist.txt", "a") as f:
            f.write(email.strip() + "\n")

        # Send notification email
        send_notification_email(email)

        return {"message": "Email added to waitlist"}
    except Exception as e:
        raise HTTPException(status_code=500, detail="Could not save email")

@app.post("/process")
async def process_data(message: Message):
    """
    Process data through the agent team using REST API.

    Args:
        message: Message object containing the data to process


    Returns:
        JSONResponse: The processed result
    """
    try:

        logger.info(f"Processing request: {message.model_dump()}")

        # Add user message to chat history if slug is provided
        if message.slug and message.slug in chat_histories:
            # Add user message to chat history
            chat_histories[message.slug].messages.append(
                ChatMessage(
                    type="user",
                    content=message.message,
                    timestamp=datetime.now().isoformat()
                )
            )
            # Save chat history after adding user message
            save_chat_history(message.slug, chat_histories[message.slug])
            logger.info(f"Added user message to chat history for slug: {message.slug}")

        # Process through agent team
        result = await team.process(message)

        # Add AI response to chat history if slug is provided
        if message.slug and message.slug in chat_histories:
            # Create a ChatMessage from the result with all fields
            chat_message = ChatMessage(
                type="ai",
                content=result.get("message", ""),
                thought=result.get("thought"),
                chart=result.get("chart"),
                causality=result.get("causality"),
                insights=result.get("insights"),
                external_contexts=result.get("external_contexts"),
                next_agent=result.get("next_agent"),
                status=result.get("status", "success"),
                timestamp=datetime.now().isoformat()
            )

            # Add to chat history
            chat_histories[message.slug].messages.append(chat_message)

            # Save chat history
            save_chat_history(message.slug, chat_histories[message.slug])
            logger.info(f"Added AI response to chat history for slug: {message.slug}")

        logger.info(f"Processing completed successfully")

        return JSONResponse(
            status_code=200,
            content=result
        )
    except Exception as e:
        error_msg = f"Error processing data: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)

        # Log detailed error to file
        error_log_path = LOGS_DIR / f"error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        with open(error_log_path, 'w') as f:
            f.write(f"Timestamp: {datetime.now().isoformat()}\n")
            f.write(f"Request: {message.model_dump()}\n")
            f.write(f"Error: {error_msg}\n")


        raise HTTPException(
            status_code=500,
            detail={
                "error": str(e),
                "error_type": type(e).__name__,
                "error_log": str(error_log_path)
            }
        )

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """Endpoint to handle file uploads."""
    try:
        logger.info(f"Processing file upload: {file.filename}")
        # Generate a random filename while preserving the extension
        file_extension = Path(file.filename).suffix
        random_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = UPLOAD_DIR / random_filename

        # Save the file
        with file_path.open("wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        logger.info(f"File uploaded successfully: {random_filename}")
        return JSONResponse(
            status_code=200,
            content={
                "message": "File uploaded successfully",
                "filename": random_filename,
            }
        )
    except Exception as e:
        error_msg = f"Error uploading file: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)

        # Log detailed error to file
        error_log_path = LOGS_DIR / f"error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        with open(error_log_path, 'w') as f:
            f.write(f"Timestamp: {datetime.now().isoformat()}\n")
            f.write(f"File: {file.filename}\n")
            f.write(f"Error: {error_msg}\n")

        return JSONResponse(
            status_code=500,
            content={
                "message": f"Error uploading file: {str(e)}",
                "error_type": type(e).__name__,
                "error_log": str(error_log_path)
            }
        )
    finally:
        file.file.close()

@app.get("/chat/{slug}")
async def get_chat_history(slug: str):
    """Get chat history for a specific slug."""
    if slug in chat_histories:
        return JSONResponse(
            status_code=200,
            content={"messages": [msg.model_dump() for msg in chat_histories[slug].messages]}
        )
    else:
        return JSONResponse(
            status_code=404,
            content={"error": f"Chat with slug {slug} not found"}
        )

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    
    # Get query parameters
    slug = websocket.query_params.get("slug")
    
    # Add to active connections
    active_connections.append(websocket)
    
    # Helper function to send chat history
    async def send_history(history_slug):
        if history_slug not in chat_histories:
            return
            
        messages = chat_histories[history_slug].messages
        
        # Find latest AI message
        latest_ai_message = next((msg for msg in reversed(messages) if msg.type == "ai"), None)
        
        # Prepare response
        response = {
            "history": [msg.model_dump() for msg in messages],
            "status": "success"
        }
        
        # Add AI specific fields if available
        if latest_ai_message:
            for field in ["chart", "causality", "insights", "external_contexts", "thought"]:
                response[field] = getattr(latest_ai_message, field, None)
                
            logger.info(f"Sending latest data for {history_slug}: " +
                       f"chart={bool(latest_ai_message.chart)}, " +
                       f"causality={bool(latest_ai_message.causality)}, " +
                       f"insights={bool(latest_ai_message.insights)}, " +
                       f"external_contexts={bool(latest_ai_message.external_contexts)}")
            
        await websocket.send_json(response)
    
    # Handle initial connection with slug
    if slug:
        logger.info(f"WebSocket connected with slug: {slug}")
        if slug not in slug_connections:
            slug_connections[slug] = []
        slug_connections[slug].append(websocket)
        
        # Send history if available
        if slug in chat_histories:
            logger.info(f"Sending chat history for slug: {slug}")
            await send_history(slug)
    else:
        logger.info("WebSocket connected without slug")
    
    try:
        while True:
            data = await websocket.receive_text()
            try:
                message_data = json.loads(data)
                user_message = message_data.get("message", "")
                message_slug = message_data.get("slug")
                
                # Handle slug update if provided in the message
                if message_slug and message_slug != slug:
                    # Remove from old slug connections
                    if slug and slug in slug_connections and websocket in slug_connections[slug]:
                        slug_connections[slug].remove(websocket)
                    
                    # Update the slug
                    slug = message_slug
                    logger.info(f"Updated slug for connection: {slug}")
                    
                    # Add to new slug connections
                    if slug not in slug_connections:
                        slug_connections[slug] = []
                    if websocket not in slug_connections[slug]:
                        slug_connections[slug].append(websocket)
                    
                    # Send history for updated slug
                    if slug in chat_histories:
                        logger.info(f"Sending chat history for updated slug: {slug}")
                        await send_history(slug)
                    
                    # If this is just a slug update with no message, continue
                    if not user_message:
                        continue
                
                # Generate a slug if not provided
                if not slug:
                    slug = generate_slug()
                    logger.info(f"Generated new slug: {slug}")
                    
                    # Create a new chat history
                    if slug not in chat_histories:
                        chat_histories[slug] = ChatHistory()
                        logger.info(f"Created new chat history for slug: {slug}")
                    
                    # Add to slug connections
                    if slug not in slug_connections:
                        slug_connections[slug] = []
                    slug_connections[slug].append(websocket)
                    
                    # Send the slug to the client
                    await websocket.send_json({
                        "slug": slug,
                        "status": "success"
                    })
                
                # Special case for "solar" demo
                if user_message == "solar":
                    # Add user message to chat history
                    if slug in chat_histories:
                        chat_histories[slug].messages.append(
                            ChatMessage(
                                type="user",
                                content=user_message,
                                timestamp=datetime.now().isoformat()
                            )
                        )
                        save_chat_history(slug, chat_histories[slug])
                    
                    # Send solar demo messages
                    for item in stories["solar flare"]:
                        # Store in chat history
                        if slug in chat_histories:
                            chat_histories[slug].messages.append(
                                ChatMessage(
                                    type="ai",
                                    content=item.get("message", ""),
                                    thought=item.get("thought"),
                                    chart=item.get("chart"),
                                    causality=item.get("causality"),
                                    insights=item.get("insights"),
                                    external_contexts=item.get("external_contexts"),
                                    timestamp=datetime.now().isoformat(),
                                    status="success"
                                )
                            )
                            save_chat_history(slug, chat_histories[slug])
                        
                        await asyncio.sleep(1)
                        await websocket.send_json(item)
                    continue
                
                # Process regular message
                if user_message and slug in chat_histories:
                    # Add user message to history
                    chat_histories[slug].messages.append(
                        ChatMessage(
                            type="user",
                            content=user_message,
                            timestamp=datetime.now().isoformat()
                        )
                    )
                    save_chat_history(slug, chat_histories[slug])
                    
                    # Process message and create response
                    ai_response = f"You said: {user_message}"
                    
                    # Create response object
                    response_obj = {
                        "message": ai_response,
                        "status": "success",
                        "thought": None,
                        "chart": None,
                        "causality": None,
                        "insights": None,
                        "external_contexts": None,
                        "next_agent": None
                    }
                    
                    # Add AI response to history
                    chat_histories[slug].messages.append(
                        ChatMessage(
                            type="ai",
                            content=ai_response,
                            timestamp=datetime.now().isoformat(),
                            status="success",
                            **{k: v for k, v in response_obj.items() if k != "message" and k != "status"}
                        )
                    )
                    
                    save_chat_history(slug, chat_histories[slug])
                    await websocket.send_json(response_obj)
                    
            except json.JSONDecodeError:
                logger.error(f"Invalid JSON received: {data}")
                await websocket.send_json({"error": "Invalid JSON format"})
            except Exception as e:
                error_msg = f"Error processing WebSocket message: {str(e)}\n{traceback.format_exc()}"
                logger.error(error_msg)
                await websocket.send_json({"error": str(e)})
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected, slug: {slug}")
        cleanup_connection()
    except Exception as e:
        logger.error(f"WebSocket error: {str(e)}")
        cleanup_connection()
        
    # Helper function to clean up connections
    def cleanup_connection():
        # Only remove the websocket from active connections
        if websocket in active_connections:
            active_connections.remove(websocket)
        
        # Remove websocket from slug connections, but preserve the chat histories
        if slug and slug in slug_connections and websocket in slug_connections[slug]:
            slug_connections[slug].remove(websocket)
            # Just log empty connections but don't delete the slug mapping
            # This ensures chat history is preserved when reconnecting with the same slug
            if not slug_connections[slug]:
                logger.info(f"Slug {slug} has no active connections but history is preserved")

# @app.websocket("/ws")
# async def websocket_endpoint(websocket: WebSocket):
#     await websocket.accept()
#     active_connections.append(websocket)
#     try:
#         while True:
#             data = await websocket.receive_text()
#             try:
#                 message = json.loads(data)
#                 # Process through agent team
#                 # result = await team.process(Message(**message))

#                 # Format the response for the frontend
#                 # formatted_response = {
#                 #     "message": "Hi how are oyu",
#                 #     "thought": "nah bro please",
#                 #     "chart" : [],
#                 #     "status": "success"
#                 # }
#                 print(message)
#                 print(stories)
#                 if( message["message"] == "solar" ):
#                     for i in stories["solar flare"]:
#                         await asyncio.sleep(1)
#                         await websocket.send_json(i)


#                 # # Extract messages from each agent's result
#                 # # for agent_name, agent_result in result.items():
#                 #     print(agent_name,agent_result.keys())
#                 #     if isinstance(agent_result, dict):
#                 #         # Handle forecast agent response
#                 #         if "llm_analysis" in agent_result:
#                 #             formatted_response["message"] = agent_result["llm_analysis"]
#                 #         # Handle analyzer agent response
#                 #         elif "analysis" in agent_result:
#                 #             formatted_response["message"] = agent_result["analysis"]
#                 #         # Handle any other message field
#                 #         elif "message" in agent_result:
#                 #             formatted_response["message"] = agent_result["message"]
#                 #         if "history" in agent_result.keys():
#                 #             history_dict=agent_result["history"]
#                 #             history_list=[]
#                 #             for i in range(len(history_dict["ds"])):
#                 #                 temp_dict={"name":"Kairos","value":history_dict["y"][i],"date":str(history_dict["ds"][i])}
#                 #                 history_list.append(temp_dict)
#                 #             formatted_response["chart"] += history_list

#                 #         # Handle any thinking/processing state
#                 #         if "forecast" in agent_result.keys():
#                 #             print("forecast")
#                 #             forecast_list=[]
#                 #             for datapoints in agent_result["forecast"]:
#                 #                 temp_dict={"name":"Kairos","date":datapoints['ds'],"forecast":datapoints['TimeGPT']}
#                 #                 forecast_list.append(temp_dict)
#                 #             formatted_response["chart"] += forecast_list
#                 #         if "status" in agent_result and agent_result["status"] == "processing":
#                 #             formatted_response["thought"] = "Processing..."

#                 # Send response back to the client
#                 # print(formatted_response)
#                 # await websocket.send_json(formatted_response)
#             except json.JSONDecodeError:
#                 logger.error(f"Invalid JSON received: {data}")
#                 await websocket.send_json({"error": "Invalid JSON format"})
#             except Exception as e:
#                 error_msg = f"Error processing WebSocket message: {str(e)}\n{traceback.format_exc()}"
#                 logger.error(error_msg)
#                 await websocket.send_json({"error": str(e)})
#     except WebSocketDisconnect:
#         active_connections.remove(websocket)
#     except Exception as e:
#         logger.error(f"WebSocket error: {str(e)}")
#         if websocket in active_connections:
#             active_connections.remove(websocket)

# Do not run this file directly, run the run.sh script instead