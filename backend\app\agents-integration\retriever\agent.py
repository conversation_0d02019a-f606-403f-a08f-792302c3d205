from typing import Dict, Any
from ..base_agent import Agent

class RetrieverAgent(Agent):
    """A retriever agent that returns its name."""
    
    def __init__(self):
        description = "A retriever agent that returns its name."
        super().__init__("retriever", description)
        
    async def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the input data and return a response.
        
        Args:
            input_data: Dictionary containing input data
            
        Returns:
            Dictionary containing the response
        """
        print(f"Retriever Agent received input: {input_data}")
        file_path = input_data["file_path"]
        message_text = input_data["message"]

        return {
            "message": "Hello from Retriever Agent!",
            "status": "success",
            "next_agent": "planner"
        } 