'use client';

import React, { useEffect, Suspense } from 'react';
import { Layout } from 'flexlayout-react';
import { useSearchParams } from 'next/navigation';

function PopoutPageContent() {
  const searchParams = useSearchParams();
  const id = searchParams.get('id');
  
  useEffect(() => {
    // Notify parent window that this popout is ready
    if (id && window.opener) {
      window.opener.postMessage({ type: 'POPOUT_READY', id }, '*');
    }
  }, [id]);
  
  if (!id) {
    return <div>Missing popout ID</div>;
  }
  
  return (
    <div className="h-screen w-full overflow-hidden">
      <div id={`popout-content-${id}`} className="h-full w-full" />
    </div>
  );
}

export default function PopoutPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PopoutPageContent />
    </Suspense>
  );
}
