import logging
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any
import numpy as np
import pandas as pd

# Create logs directory if it doesn't exist
LOG_DIR = Path("logs")
LOG_DIR.mkdir(exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_DIR / f"agent_interactions_{datetime.now().strftime('%Y%m%d')}.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("agent_interactions")

def _convert_to_serializable(obj):
    """Convert numpy/pandas types to Python native types."""
    if isinstance(obj, (np.integer, np.int64)):
        return int(obj)
    elif isinstance(obj, (np.float64, np.float32)):
        return float(obj)
    elif isinstance(obj, (np.ndarray,)):
        return obj.tolist()
    elif isinstance(obj, pd.DataFrame):
        return obj.to_dict(orient='records')
    elif isinstance(obj, pd.Series):
        return obj.to_dict()
    elif isinstance(obj, dict):
        return {str(k): _convert_to_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [_convert_to_serializable(item) for item in obj]
    return obj

def log_agent_interaction(agent_name: str, input_data: Dict[str, Any], output_data: Dict[str, Any]) -> None:
    """
    Log agent interactions with proper serialization.
    
    Args:
        agent_name: Name of the agent
        input_data: Input data dictionary
        output_data: Output data dictionary
    """
    try:
        # Convert data to serializable format
        serializable_input = _convert_to_serializable(input_data)
        serializable_output = _convert_to_serializable(output_data)
        
        interaction = {
            "timestamp": datetime.now().isoformat(),
            "agent": agent_name,
            "input": serializable_input,
            "output": serializable_output
        }
        
        # Log the interaction
        logger.info(f"Agent Interaction: {json.dumps(interaction, indent=2)}")
        
        # Also save to a JSON file for easier analysis
        interaction_file = LOG_DIR / f"interactions_{datetime.now().strftime('%Y%m%d')}.json"
        try:
            if interaction_file.exists():
                with open(interaction_file, 'r') as f:
                    interactions = json.load(f)
            else:
                interactions = []
            
            interactions.append(interaction)
            
            with open(interaction_file, 'w') as f:
                json.dump(interactions, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving interaction to JSON: {str(e)}")
    except Exception as e:
        logger.error(f"Error logging agent interaction: {str(e)}")
        # Log a simplified version without the problematic data
        simplified_interaction = {
            "timestamp": datetime.now().isoformat(),
            "agent": agent_name,
            "error": f"Error serializing interaction: {str(e)}"
        }
        logger.info(f"Simplified Agent Interaction: {json.dumps(simplified_interaction, indent=2)}") 