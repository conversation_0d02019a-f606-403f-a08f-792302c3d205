from typing import Dict, Any
from ..base_agent import Agent

class PlannerAgent(Agent):
    """A planner agent that determines the workflow sequence."""
    
    def __init__(self):
        description = "A planner agent that determines the workflow sequence."
        super().__init__("planner", description)
        
    async def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the input data and determine the next agent to call.
        
        Args:
            input_data: Dictionary containing input data
            
        Returns:
            Dictionary containing the response and next agent recommendation
        """
        # Check if we have analysis results
        if "analysis" in input_data:
            # If we have analysis, we're done
            return {
                "message": input_data.get("message", ""),
                "status": "success",
                "analysis": input_data.get("analysis", ""),
                "next_agent": None  # End the workflow
            }
        # Check if we have data from retriever
        elif "data" in input_data:
            # If we have data, send it to analyzer
            return {
                "message": input_data.get("message", ""),
                "status": "success",
                "data": input_data.get("data", {}),  # Pass along the retrieved data
                "next_agent": "analyzer"  # Send to analyzer
            }
        else:
            # If no data yet, start with retriever
            return {
                "message": input_data.get("message", ""),
                "status": "success",
                "next_agent": "retriever"  # Start with retriever
            } 