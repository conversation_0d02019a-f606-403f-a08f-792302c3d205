import { NextResponse } from 'next/server';
import { BACKEND_API_URL } from '../../../utils/websocket';

// @ts-expect-error Async route handler context typing is broken in Next.js app dir
export async function GET(request, context) {
  const id = context.params.id;
  
  try {
    // Fetch chat data from the backend
    const response = await fetch(`${BACKEND_API_URL}/chat/${id}`);
    
    if (!response.ok) {
      return NextResponse.json(
        { error: `Failed to fetch chat with ID ${id}` },
        { status: response.status }
      );
    }
    
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error fetching chat ${id}:`, error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
