import os
import logging
import json
from typing import Any, Dict, List, Iterator, AsyncIterator, Optional, Union
import litellm
from litellm import completion
from litellm.caching.caching import Cache

logger = logging.getLogger(__name__)


class LLMManager:
    """
    Lightweight wrapper around OpenAI and AzureOpenAI clients.

    Configuration via environment variables (LLM_ prefixes):
      - MODEL: model name (required)
      - API_KEY: API key
      - DEPLOYMENT_TYPE: 'gemini' or 'openai' or 'anthropic'
      - Additional vars are forwarded to the client.
    """

    def __init__(self, **overrides: Any) -> None:
        # Load environment settings
        env = os.environ
        defaults: Dict[str, Any] = {}
        for prefix in ("LLM_", ):
            for key, val in env.items():
                if key.startswith(prefix):
                    name = key[len(prefix) :].lower()
                    defaults.setdefault(name, val)

        # Merge overrides
        self._defaults = {**defaults, **overrides}

        # Required: model
        model = self._defaults.get("model")
        if not model:
            raise ValueError("Model must be set via env var or override")
        self.model = model

        # Determine deployment
        deployment = self._defaults.get("deployment_type", "").lower()
        if deployment == "gemini":
            api_key = self._defaults.get("api_key")
            os.environ["GEMINI_API_KEY"] = api_key
            self.model = f'gemini/{self.model}'
        elif deployment == "openai":
            api_key = self._defaults.get("api_key")
            os.environ["OPENAI_API_KEY"] = api_key
        elif deployment == "anthropic":
            api_key = self._defaults.get("api_key")
            os.environ["ANTHROPIC_API_KEY"] = api_key
            self.model = f'anthropic/{self.model}'
        else:
            raise ValueError(f"Invalid deployment type: {deployment}")
        self._defaults.pop("api_key", None)
        self._defaults.pop("model", None)
        self._defaults.pop("deployment_type", None)
        # litellm.cache = Cache()

    def chat(
        self,
        messages: List[Dict[str, str]],
        **kwargs: Any,
    ) -> Union[str, Dict]:
        """
        Run a chat completion.
        If stream=True, returns an iterator of chunks; otherwise returns the final content.
        """
        params = {
            "model": self.model,
            "messages": messages,
            "caching": True,
            **self._defaults,
            **kwargs,
        }
        logger.debug("Chat params: %s", params)
        resp = completion(**params)
        finish_reason = resp.choices[0].finish_reason
        if finish_reason == "tool_calls":
            # Handle function call
            tool_calls = []
            for tool_call in resp.choices[0].message.tool_calls:
                tool_calls.append(
                    {
                        "id": tool_call.id,
                        "name": tool_call.function.name,
                        "args": tool_call.function.arguments,
                    }
                )
            return tool_calls
        elif finish_reason == "stop":
            # Handle normal stop
            return resp.choices[0].message.content
        else:
            # Handle other cases (e.g., error)
            logger.error("Unexpected finish reason: %s", resp.choices[0].finish_reason)
            raise ValueError(
                f"Unexpected finish reason: {resp.choices[0].finish_reason}"
            )

    @staticmethod
    def sm(msg: str) -> Dict[str, str]:
        return {"role": "system", "content": msg}

    @staticmethod
    def um(msg: str) -> Dict[str, str]:
        return {"role": "user", "content": msg}

    @staticmethod
    def am(msg: str) -> Dict[str, str]:
        return {"role": "assistant", "content": msg}

    @staticmethod
    def function(
        name: str, description: str, params: Dict[str, Any], required: List[str]
    ) -> Dict[str, Any]:
        return {
            "type": "function",
            "function": {
                "name": name,
                "description": description,
                "parameters": {
                    "type": "object",
                    "properties": params,
                    "required": required,
                },
            },
        }