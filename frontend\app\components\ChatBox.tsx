"use client";

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import FileUploadModal from './chat/FileUploadModal';
import FormModal from './chat/FormModal';
import UploadingFilesList from './chat/UploadingFilesList';
import SelectedChartContext from './SelectedChartContext';
import PlusMenu from './chat/PlusMenu';
import { MultiStepLoader } from './ui/multi-step-loader';
import { useChat } from '../contexts/ChatContext';

interface ChatBoxProps {
  onSend?: (message: string) => void;
  placeholder?: string;
  navigateTo?: string;
  skipNavigation?: boolean; // New prop to skip navigation and loader
  autoGrow?: boolean;
  maxHeight?: string;
  inSidebar?: boolean;
}

export default function ChatBox({
  onSend = (message: string) => {
    console.log("Default onSend handler:", message);
  },
  placeholder = "let's explore, what do you want to know?",
  navigateTo,
  skipNavigation = false,
  autoGrow = false,
  maxHeight = '120px',
  inSidebar = false
}: ChatBoxProps) {
  // Add router
  const router = useRouter();
  const [message, setMessage] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Use the chat context but don't automatically clear files or selections
  const { uploadFile, uploadedFiles, removeUploadedFile, clearUploadedFiles, chartSelection, setChartSelection, currentSlug, sendMessage: contextSendMessage } = useChat();

  // Auto-resize textarea function
  const autoResize = () => {
    if (autoGrow && textareaRef.current) {
      // First reset the height completely to get accurate scrollHeight
      textareaRef.current.style.height = '0px';

      // Then set height based on scrollHeight, with minimum of 24px
      const scrollHeight = textareaRef.current.scrollHeight;
      const newHeight = Math.max(
        Math.min(scrollHeight, parseInt(maxHeight)),
        24
      );

      textareaRef.current.style.height = `${newHeight}px`;
    }
  };

  // Call autoResize whenever message changes
  useEffect(() => {
    if (autoGrow) {
      autoResize();
    }
  }, [message, autoGrow]);

  const [showPlusMenu, setShowPlusMenu] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showFormModal, setShowFormModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadingFiles, setUploadingFiles] = useState<{file: File, progress: number, complete: boolean}[]>([]);
  const [formFields, setFormFields] = useState([
    { key: '', value: '' },
    { key: '', value: '' },
    { key: '', value: '' },
    { key: '', value: '' }
  ]);

  // Update the loadingStates array with your custom messages
  const loadingStates = [
    { text: "Processing your message" },
    { text: "Analyzing content" },
    { text: "Searching knowledge base" },
    { text: "Generating response" },
    { text: "Finalizing results" }
  ];

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setShowPlusMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSend = () => {
    if (message.trim()) {
      // If skipNavigation is true, use the context's sendMessage directly
      if (skipNavigation) {
        console.log("Processing message in chat panel:", message);

        // Call the onSend prop (which typically uses the context's sendMessage)
        if (onSend) {
          onSend(message);
        }

        // Only clear the message input, not the uploaded files
        setMessage('');
        return;
      }

      // For the main chat page, we want to use our own logic
      console.log("Send button clicked on main page, showing loader");
      setLoading(true); // Show the multi-step loader

      // Clear the message input first
      const messageToSend = message;
      setMessage('');

      // Set a timeout to ensure the loader is visible before sending the message
      // This helps with the animation timing
      setTimeout(() => {
        // Use the context's sendMessage to ensure slug generation
        contextSendMessage(messageToSend);

        // Keep the loader visible for a bit longer to ensure animation completes
        // The ChatContext will handle the redirect after receiving the slug
        setTimeout(() => {
          setLoading(false); // Hide the loader
          console.log("Loader complete, currentSlug:", currentSlug);

          // If we have a slug by now but haven't redirected yet, use router to redirect
          if (currentSlug && (window.location.pathname === '/' || window.location.pathname === '/chat')) {
            console.log("Manually redirecting to chat page with slug:", currentSlug);
            router.push(`/chats/${currentSlug}`);
          }
        }, 3000); // Increased to 3 seconds to ensure animation completes
      }, 500); // Small delay before sending message
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleFileUpload = () => {
    setShowUploadModal(true);
    setShowPlusMenu(false);
  };

  const handleServerConnection = () => {
    // Server connection logic here
    console.log("Server connection clicked");
    setShowPlusMenu(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      setSelectedFile(file);
      console.log("File dropped:", file.name);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      setSelectedFile(file);
      console.log("File selected:", file.name);
    }
  };

  const handleBrowseClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleUploadSubmit = async () => {
    if (selectedFile) {
      // Add file to uploading files with 0% progress
      setUploadingFiles(prev => [...prev, {
        file: selectedFile,
        progress: 0,
        complete: false
      }]);

      // Close the modal
      setShowUploadModal(false);

      try {
        // Start progress animation
        let progress = 0;
        const interval = setInterval(() => {
          progress += 5;
          if (progress <= 90) { // Only go up to 90% for visual feedback
            setUploadingFiles(prev =>
              prev.map(item =>
                item.file.name === selectedFile.name
                  ? { ...item, progress: progress }
                  : item
              )
            );
          }
        }, 200);

        // Actually upload the file using the context function
        const result = await uploadFile(selectedFile);

        // Complete the progress
        clearInterval(interval);
        setUploadingFiles(prev =>
          prev.map(item =>
            item.file.name === selectedFile.name
              ? { ...item, progress: 100, complete: true }
              : item
          )
        );

        console.log("File uploaded successfully:", result);

        // No need to store the file path locally, it's now in the context
        setSelectedFile(null);
      } catch (error) {
        console.error("Error uploading file:", error);

        // Show error in the upload list
        setUploadingFiles(prev =>
          prev.map(item =>
            item.file.name === selectedFile.name
              ? { ...item, progress: 0, complete: false }
              : item
          )
        );

        // Optionally notify the user about the error
        alert(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } finally {
        setSelectedFile(null);
      }
    }
  };

  const handleCloseModal = () => {
    setSelectedFile(null);
    setShowUploadModal(false);
  };

  const handleRemoveFile = (fileName: string) => {
    // Remove from the uploading files list
    setUploadingFiles(prev => prev.filter(item => item.file.name !== fileName));

    // Also remove from the context's uploaded files
    removeUploadedFile(fileName);
  };

  const handleEqualsClick = () => {
    setShowFormModal(true);
  };

  const handleCloseFormModal = () => {
    setShowFormModal(false);
  };

  const handleFormFieldChange = (index: number, field: 'key' | 'value', value: string) => {
    const newFormFields = [...formFields];
    newFormFields[index][field] = value;
    setFormFields(newFormFields);
  };

  const handleFormSubmit = () => {
    console.log("Form submitted:", formFields);
    setShowFormModal(false);
  };

  // Add this effect to handle resize when parent containers change
  useEffect(() => {
    // Small delay to ensure DOM has updated after sidebar expansion
    const timer = setTimeout(() => {
      if (autoGrow && textareaRef.current) {
        autoResize();
      }
    }, 300);

    // Add a resize observer to handle container size changes
    if (autoGrow && textareaRef.current) {
      const resizeObserver = new ResizeObserver(() => {
        autoResize();
      });

      resizeObserver.observe(textareaRef.current.parentElement || document.body);

      return () => {
        clearTimeout(timer);
        resizeObserver.disconnect();
      };
    }

    return () => clearTimeout(timer);
  }, [autoGrow]);

  return (
    <div className="relative w-full max-w-2xl">
      {/* MultiStepLoader */}
      <MultiStepLoader
        loadingStates={loadingStates}
        loading={loading}
        duration={2000}
      />

      <div className="glass-card px-5 py-4 flex flex-col border-t-2 border-r-2 border-kairosYellow shadow-[0_0_15px_rgba(255,246,141,0.5)]">
        {/* Input field and send button */}
        <div className="flex items-center w-full">
          {autoGrow ? (
            <textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={autoResize}
              onBlur={autoResize}
              placeholder={placeholder}
              className="flex-grow bg-transparent outline-none text-gray-200 placeholder-gray-500 text-sm resize-none overflow-y-auto min-h-[24px]"
              style={{
                maxHeight,
                scrollbarWidth: 'thin',
                scrollbarColor: 'rgba(180, 180, 180, 0.1) rgba(0, 0, 0, 0.05)'
              }}
              rows={1}
            />
          ) : (
            <input
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              className="flex-grow bg-transparent outline-none text-gray-200 placeholder-gray-500 text-sm"
            />
          )}

          {/* Send button */}
          <button
            type="button"
            onClick={handleSend}
            className="ml-3 text-red-400 hover:text-red-300 transition-colors"
            aria-label="Send message"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M22 2L11 13"></path>
              <path d="M22 2L15 22L11 13L2 9L22 2Z"></path>
            </svg>
          </button>
        </div>



        {/* Uploading files display */}
        <UploadingFilesList
          uploadingFiles={uploadingFiles}
          onRemoveFile={handleRemoveFile}
        />

        {/* Point and Range Selection Context */}
        <SelectedChartContext />

        {/* Action buttons on second line */}
        <div className="flex items-center mt-3">
          {/* Plus button with hover menu */}
          <PlusMenu
            buttonRef={buttonRef as any}
            menuRef={menuRef as any}
            showMenu={showPlusMenu}
            onToggleMenu={() => setShowPlusMenu(!showPlusMenu)}
            onFileUpload={handleFileUpload}
            onServerConnection={handleServerConnection}
            direction="top"
            inSidebar={inSidebar}
          />

          {/* Equals button */}
          <button
            type="button"
            className="w-7 h-7 rounded-full border border-gray-600 flex items-center justify-center text-gray-400 hover:text-gray-200 hover:bg-gray-800 transition-colors ml-2"
            aria-label="Options"
            onClick={handleEqualsClick}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="5" y1="12" x2="19" y2="12"></line>
              <line x1="5" y1="8" x2="19" y2="8"></line>
              <line x1="5" y1="16" x2="19" y2="16"></line>
            </svg>
          </button>
        </div>
      </div>

      {/* File Upload Modal */}
      {showUploadModal && (
        <FileUploadModal
          selectedFile={selectedFile}
          isDragging={isDragging}
          fileInputRef={fileInputRef as React.RefObject<HTMLInputElement>}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onBrowseClick={handleBrowseClick}
          onFileInputChange={handleFileInputChange}
          onClose={handleCloseModal}
          onUpload={handleUploadSubmit}
        />
      )}

      {/* Form Modal */}
      {showFormModal && (
        <FormModal
          formFields={formFields}
          onFieldChange={handleFormFieldChange}
          onClose={handleCloseFormModal}
          onSubmit={handleFormSubmit}
          inSidebar={inSidebar}
        />
      )}
    </div>
  );
}
