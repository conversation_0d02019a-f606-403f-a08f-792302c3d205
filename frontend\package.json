{"name": "kairos-t0", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@heroicons/react": "^2.2.0", "@lottiefiles/dotlottie-react": "^0.14.2", "@next/font": "^14.2.15", "@tabler/icons-react": "^3.31.0", "@types/papaparse": "^5.3.15", "clsx": "^2.1.1", "cytoscape": "^3.32.0", "cytoscape-dagre": "^2.5.0", "dagre": "^0.8.5", "dagre-layout": "^0.8.8", "flexlayout-react": "^0.8.16", "lottie-react": "^2.4.1", "lucide-react": "^0.509.0", "motion": "^12.10.4", "next": "15.3.1", "papaparse": "^5.5.2", "postcss": "^8.5.3", "rc-dock": "^3.3.1", "react": "^19.0.0", "react-cytoscapejs": "^2.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "recharts": "^2.15.3", "shadcn": "^2.5.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.5", "@tailwindcss/typography": "^0.5.16", "@types/cytoscape": "^3.21.9", "@types/cytoscape-dagre": "^2.3.3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "typescript": "^5"}}